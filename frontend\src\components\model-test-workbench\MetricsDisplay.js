import React, { useState, useEffect, useCallback } from 'react';
import Plot from 'react-plotly.js';
import {
    Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper,
    Typography, CircularProgress, Alert, Box, Slider, Grid, Link
} from '@mui/material';
import { getPerformanceSummary, getScoreDistribution, getConfusionMatrix } from '../../services/api_model_workbench';

const MetricsDisplay = ({ ipCategory }) => {
    const [performanceSummary, setPerformanceSummary] = useState([]);
    const [selectedModel, setSelectedModel] = useState(null);
    const [scoreDistribution, setScoreDistribution] = useState({ gt_scores: [], non_gt_scores: [] });
    const [confusionMatrix, setConfusionMatrix] = useState({ tp: 0, fn: 0, fp: 0, tn: 0 });
    const [threshold, setThreshold] = useState(0.5); // Default threshold
    const [loadingSummary, setLoadingSummary] = useState(false);
    const [loadingDetails, setLoadingDetails] = useState(false);
    const [error, setError] = useState(null);

    const ipCategoryDisplay = ipCategory.charAt(0).toUpperCase() + ipCategory.slice(1);

    // Fetch Performance Summary
    useEffect(() => {
        const fetchSummary = async () => {
            setLoadingSummary(true);
            setError(null);
            setSelectedModel(null); // Reset selected model when category changes
            setPerformanceSummary([]);
            try {
                const response = await getPerformanceSummary(ipCategory);
                // Sort by precision_avg_rank ascending before setting state
                const sortedData = response.data.sort((a, b) => a.precision_avg_rank - b.precision_avg_rank);
                setPerformanceSummary(sortedData);
            } catch (err) {
                console.error(`Error fetching performance summary for ${ipCategory}:`, err);
                setError(`Failed to load performance summary for ${ipCategoryDisplay}.`);
            } finally {
                setLoadingSummary(false);
            }
        };
        fetchSummary();
    }, [ipCategory, ipCategoryDisplay]); // Rerun when ipCategory changes

    // Fetch Score Distribution when selectedModel changes
    useEffect(() => {
        if (!selectedModel) {
            setScoreDistribution({ gt_scores: [], non_gt_scores: [] });
            return;
        }
        const fetchScores = async () => {
            setLoadingDetails(true);
            setError(null);
            try {
                // Corrected parameter order: model_id, ip_category
                const response = await getScoreDistribution(selectedModel.model_id, ipCategory);
                console.log("Score distribution data received:", response.data); // Added logging
                setScoreDistribution(response.data);
            } catch (err) {
                console.error(`Error fetching score distribution for model ${selectedModel.model_id}:`, err);
                setError(`Failed to load score distribution for ${selectedModel.model_name}.`);
                setScoreDistribution({ gt_scores: [], non_gt_scores: [] }); // Clear data on error
            } finally {
                setLoadingDetails(false); // Stop loading details only after scores are fetched
            }
        };
        fetchScores();
    }, [selectedModel, ipCategory]); // Rerun when selectedModel or ipCategory changes

    // Fetch Confusion Matrix when selectedModel or threshold changes
    const fetchMatrix = useCallback(async (currentThreshold) => {
        if (!selectedModel) {
            setConfusionMatrix({ tp: 0, fn: 0, fp: 0, tn: 0 });
            return;
        }
        // Don't set loadingDetails here if score distribution is already loading
        if (!loadingDetails) setLoadingDetails(true);
        // Don't clear error if score distribution fetch failed
        // setError(null);
        try {
            // Corrected parameter order: model_id, ip_category, threshold
            const response = await getConfusionMatrix(selectedModel.model_id, ipCategory, currentThreshold);
            setConfusionMatrix(response.data);
        } catch (err) {
            console.error(`Error fetching confusion matrix for model ${selectedModel.model_id} at threshold ${currentThreshold}:`, err);
            setError(`Failed to load confusion matrix for ${selectedModel.model_name}.`);
            setConfusionMatrix({ tp: 0, fn: 0, fp: 0, tn: 0 }); // Clear data on error
        } finally {
            setLoadingDetails(false); // Ensure loading is stopped
        }
    }, [selectedModel, ipCategory]); // Removed loadingDetails from dependency array to prevent potential loops

    useEffect(() => {
        fetchMatrix(threshold);
    }, [selectedModel, threshold, fetchMatrix]); // Rerun when selectedModel or threshold changes


    const handleModelSelect = (model) => {
        setSelectedModel(model);
        setThreshold(0.5); // Reset threshold when selecting a new model
        setError(null); // Clear previous errors
    };

    const handleThresholdChange = (event, newValue) => {
        setThreshold(newValue);
        // Fetching matrix is handled by the useEffect hook watching threshold
    };

    const handleSliderChangeCommitted = (event, newValue) => {
        // Optional: Could trigger fetch only on commit if performance is an issue
        // fetchMatrix(newValue);
    };

    // Plotly data and layout
    const plotData = [
        {
            x: scoreDistribution.non_gt_scores,
            type: 'histogram',
            name: 'Non-Matches',
            opacity: 0.7,
            marker: { color: 'red' },
            nbinsx: 50,
        },
        {
            x: scoreDistribution.gt_scores,
            type: 'histogram',
            name: 'Ground Truth Matches',
            opacity: 0.7,
            marker: { color: 'green' },
            nbinsx: 50,
        },
    ];

    const plotLayout = {
        title: `Score Distribution for ${selectedModel?.model_name || 'Selected Model'}`,
        xaxis: { title: 'Similarity Score', tickangle: 45 }, // Added tickangle to rotate labels
        yaxis: { title: 'Frequency', type: 'log' }, // Added type: 'log' for logarithmic scale
        barmode: 'overlay',
        legend: { x: 0.7, y: 1 },
        margin: { l: 50, r: 50, t: 50, b: 50 },
    };

    return (
        <Box sx={{ p: 3 }}>
            <Typography variant="h5" gutterBottom>{ipCategoryDisplay} Performance Metrics</Typography>

            {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}

            <Typography variant="h6" gutterBottom>Model Performance Summary</Typography>
            {loadingSummary ? (
                <CircularProgress />
            ) : performanceSummary.length === 0 && !error ? (
                 <Alert severity="info">No performance data available for {ipCategoryDisplay}. Run comparisons first.</Alert>
            ) : (
                <TableContainer component={Paper} sx={{ mb: 4 }}>
                    <Table aria-label={`${ipCategory} performance summary table`}>
                        <TableHead>
                            <TableRow>
                                <TableCell>Model Name</TableCell>
                                <TableCell align="right">Precision Avg. Rank</TableCell>
                                {/* Conditionally render these headers if any model has them, or remove if never present */}
                                {performanceSummary.some(m => m.hasOwnProperty('avg_gt_score')) && <TableCell align="right">Avg. GT Score</TableCell>}
                                {performanceSummary.some(m => m.hasOwnProperty('avg_non_gt_score')) && <TableCell align="right">Avg. Non-GT Score</TableCell>}
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {performanceSummary.map((model) => (
                                <TableRow
                                    key={model.model_id}
                                    hover
                                    onClick={() => handleModelSelect(model)}
                                    style={{ cursor: 'pointer' }}
                                    selected={selectedModel?.model_id === model.model_id}
                                >
                                    <TableCell component="th" scope="row">
                                        <Link component="button" variant="body2" onClick={(e) => { e.stopPropagation(); handleModelSelect(model); }}>
                                            {model.model_name}
                                        </Link>
                                        <Typography variant="caption" display="block" color="textSecondary">
                                            ID: {model.model_id}
                                        </Typography>
                                    </TableCell>
                                    <TableCell align="right">{model.precision_avg_rank?.toFixed(2) ?? 'N/A'}</TableCell>
                                    {/* Conditionally render these cells */}
                                    {performanceSummary.some(m => m.hasOwnProperty('avg_gt_score')) &&
                                        <TableCell align="right">{model.avg_gt_score?.toFixed(4) ?? 'N/A'}</TableCell>
                                    }
                                    {performanceSummary.some(m => m.hasOwnProperty('avg_non_gt_score')) &&
                                        <TableCell align="right">{model.avg_non_gt_score?.toFixed(4) ?? 'N/A'}</TableCell>
                                    }
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </TableContainer>
            )}

            {selectedModel && (
                <Box>
                    <Typography variant="h6" gutterBottom>Detailed Analysis for: {selectedModel.model_name}</Typography>
                    {loadingDetails ? (
                        <CircularProgress />
                    ) : (
                        <Grid container spacing={3}>
                            {/* Score Distribution Plot */}
                            <Grid item xs={12} md={12}> {/* Changed md={6} to md={12} */}
                                {/* Removed Paper component */}
                                <Typography variant="subtitle1" gutterBottom>Score Distribution</Typography>
                                {(scoreDistribution.gt_scores.length > 0 || scoreDistribution.non_gt_scores.length > 0) ? (
                                    <Plot
                                        data={plotData}
                                        layout={plotLayout}
                                        useResizeHandler={true}
                                        style={{ width: '100%', height: '400px', minWidth: '800px' }} // Added minWidth
                                        config={{ responsive: true }}
                                    />
                                ) : (
                                    <Alert severity="info">Score distribution data not available for this model.</Alert>
                                )}
                            </Grid> {/* Added padding sx={{ p: 2 }} to the Grid item */}

                            {/* Threshold Analysis & Confusion Matrix */}
                            <Grid item xs={12} md={12}> {/* Changed md={6} to md={12} */}
                                <Paper elevation={3} sx={{ p: 2 }}>
                                    <Typography variant="subtitle1" gutterBottom>Threshold Analysis</Typography>
                                    <Box sx={{ width: '90%', margin: 'auto', mt: 2, mb: 4 }}>
                                        <Typography id="threshold-slider-label" gutterBottom>
                                            Similarity Threshold: {threshold.toFixed(2)}
                                        </Typography>
                                        <Slider
                                            aria-labelledby="threshold-slider-label"
                                            value={threshold}
                                            onChange={handleThresholdChange}
                                            onChangeCommitted={handleSliderChangeCommitted}
                                            step={0.01}
                                            min={0}
                                            max={1}
                                            valueLabelDisplay="auto"
                                        />
                                    </Box>

                                    <Typography variant="subtitle1" gutterBottom sx={{ mt: 2 }}>Confusion Matrix (Threshold: {threshold.toFixed(2)})</Typography>
                                    <TableContainer component={Paper} variant="outlined">
                                        <Table size="small" aria-label="confusion matrix">
                                            <TableHead>
                                                <TableRow>
                                                    <TableCell></TableCell>
                                                    <TableCell align="center"><b>Predicted: Match</b></TableCell>
                                                    <TableCell align="center"><b>Predicted: No Match</b></TableCell>
                                                </TableRow>
                                            </TableHead>
                                            <TableBody>
                                                <TableRow>
                                                    <TableCell component="th" scope="row"><b>Actual: Match</b></TableCell>
                                                    <TableCell align="center" sx={{ backgroundColor: 'rgba(0, 128, 0, 0.1)' }}>
                                                        <Typography variant="h6">{confusionMatrix.tp}</Typography>
                                                        <Typography variant="caption">(True Positives)</Typography>
                                                    </TableCell>
                                                    <TableCell align="center" sx={{ backgroundColor: 'rgba(255, 165, 0, 0.1)' }}>
                                                        <Typography variant="h6">{confusionMatrix.fn}</Typography>
                                                        <Typography variant="caption">(False Negatives)</Typography>
                                                    </TableCell>
                                                </TableRow>
                                                <TableRow>
                                                    <TableCell component="th" scope="row"><b>Actual: No Match</b></TableCell>
                                                    <TableCell align="center" sx={{ backgroundColor: 'rgba(255, 0, 0, 0.1)' }}>
                                                        <Typography variant="h6">{confusionMatrix.fp}</Typography>
                                                        <Typography variant="caption">(False Positives)</Typography>
                                                    </TableCell>
                                                    <TableCell align="center" sx={{ backgroundColor: 'rgba(0, 0, 255, 0.1)' }}>
                                                        <Typography variant="h6">{confusionMatrix.tn}</Typography>
                                                        <Typography variant="caption">(True Negatives)</Typography>
                                                    </TableCell>
                                                </TableRow>
                                            </TableBody>
                                        </Table>
                                    </TableContainer>
                                    <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                                        TP: Correctly identified matches. FN: Actual matches missed. FP: Incorrectly identified as matches. TN: Correctly identified non-matches.
                                    </Typography>
                                </Paper>
                            </Grid>
                        </Grid>
                    )}
                </Box>
            )}
        </Box>
    );
};

export default MetricsDisplay;