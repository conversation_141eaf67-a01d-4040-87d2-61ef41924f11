import React from 'react';
import { useDroppable } from '@dnd-kit/core';

const DroppableZone = ({ id, children, className = '', acceptsFrom = [] }) => {
  const { isOver, setNodeRef } = useDroppable({
    id: id,
  });

  const style = {
    backgroundColor: isOver ? 'rgba(0, 123, 255, 0.1)' : undefined,
    border: isOver ? '2px dashed #007bff' : undefined,
    borderRadius: isOver ? '8px' : undefined,
    transition: 'all 0.2s ease',
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`droppable-container ${isOver ? 'drop-zone-active' : ''}`}
    >
      <div className={className}>
        {children}
        {React.Children.count(children) === 0 && (
          <div className="droppable-placeholder">
            <span>Drop a CN Websites File here</span>
          </div>
        )}
      </div>
      {isOver && acceptsFrom.length > 0 && (
        <div className="drop-indicator">
          <span>Drop here to move to Copyrights Files</span>
        </div>
      )}
    </div>
  );
};

export default DroppableZone;
