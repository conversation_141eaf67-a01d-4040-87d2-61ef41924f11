# backend/AI/shared_models.py
import time
import logging
import threading
from typing import Optional
from backend.AI.siglip_model import SiglipModel

logger = logging.getLogger(__name__)

EMBEDDING_VECTOR_SIZE = 1024
SIGLIP_MODEL_ID = "siglip2_large_patch16_512"
MODEL_NAME_OR_PATH = "google/siglip2-large-patch16-512"

# Lazy, thread-safe singleton for SiglipModel
_siglip_instance: Optional[SiglipModel] = None
_siglip_lock = threading.Lock()
_siglip_loaded_event = threading.Event()

def _create_siglip_model_unloaded() -> SiglipModel:
    """Create the SiglipModel object without loading weights/processors."""
    return SiglipModel(
        model_id=SIGLIP_MODEL_ID,
        config={
            "vector_size": EMBEDDING_VECTOR_SIZE,
            "model_name_or_path": MODEL_NAME_OR_PATH
        }
    )

def get_siglip_model(load_if_needed: bool = True) -> SiglipModel:
    """
    Returns the singleton SiglipModel instance.
    If load_if_needed is True, ensures the model is loaded (loading on first use).
    Thread-safe via double-checked locking.
    """
    global _siglip_instance
    if _siglip_instance is not None and _siglip_instance.model is not None and _siglip_instance.processor is not None:
        return _siglip_instance

    with _siglip_lock:
        if _siglip_instance is None:
            # Create instance
            _siglip_instance = _create_siglip_model_unloaded()
        if load_if_needed and (_siglip_instance.model is None or _siglip_instance.processor is None):
            start_time = time.time()
            try:
                logger.info("Loading Siglip model lazily on first access...")
                _siglip_instance.load()
                logger.info("Siglip model loaded in %.4f sec", time.time() - start_time)
                _siglip_loaded_event.set()
            except Exception:
                # Do not set the event on failure; let callers decide how to handle
                logger.critical("Failed to load Siglip model during lazy init", exc_info=True)
                raise
    return _siglip_instance

def warmup_siglip_model(timeout_seconds: Optional[float] = None) -> None:
    """
    Proactively loads the Siglip model in the background initialization phase.
    Safe to call multiple times; returns when load completes or immediately if already loaded.
    """
    try:
        model = get_siglip_model(load_if_needed=True)
        # If we got here, loading succeeded; ensure event is set
        _siglip_loaded_event.set()
        logger.info("Siglip warmup complete.")
    except Exception as e:
        logger.error("Siglip warmup failed: %s", e, exc_info=True)

def wait_for_siglip_loaded(timeout: Optional[float] = None) -> bool:
    """
    Allows callers to wait until the model has finished loading.
    Returns True if loaded event was set, False if timeout.
    """
    return _siglip_loaded_event.wait(timeout=timeout)