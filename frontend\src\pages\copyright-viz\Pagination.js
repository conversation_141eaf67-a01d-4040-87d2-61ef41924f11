import React, { useState, useEffect, useRef } from 'react';
import './Pagination.css';

const Pagination = ({ currentPage, totalPages, onPageChange }) => {
    const [inputPage, setInputPage] = useState(currentPage);
    const debounceTimeout = useRef(null);

    useEffect(() => {
        setInputPage(currentPage);
    }, [currentPage]);

    const handlePrevious = () => {
        if (currentPage > 1) {
            onPageChange(currentPage - 1);
        }
    };

    const handleNext = () => {
        if (currentPage < totalPages) {
            onPageChange(currentPage + 1);
        }
    };

    const handleInputChange = (e) => {
        const value = e.target.value;
        setInputPage(value);

        if (debounceTimeout.current) {
            clearTimeout(debounceTimeout.current);
        }

        debounceTimeout.current = setTimeout(() => {
            const pageNumber = parseInt(value, 10);
            if (!isNaN(pageNumber) && pageNumber >= 1 && pageNumber <= totalPages) {
                if (pageNumber !== currentPage) {
                    onPageChange(pageNumber);
                }
            }
        }, 800); // 800ms delay after user stops typing
    };

    const handleInputBlur = () => {
        // On blur, if the input is invalid or empty, reset it to the current page.
        const pageNumber = parseInt(inputPage, 10);
        if (isNaN(pageNumber) || pageNumber < 1 || pageNumber > totalPages) {
            setInputPage(currentPage);
        }
    };
    
    const handleInputKeyPress = (e) => {
        if (e.key === 'Enter') {
            if (debounceTimeout.current) {
                clearTimeout(debounceTimeout.current);
            }
            const pageNumber = parseInt(inputPage, 10);
            if (!isNaN(pageNumber) && pageNumber >= 1 && pageNumber <= totalPages) {
                if (pageNumber !== currentPage) {
                    onPageChange(pageNumber);
                }
            } else {
                setInputPage(currentPage);
            }
        }
    };

    if (totalPages <= 1) {
        return null;
    }

    return (
        <div className="pagination-container">
            <button onClick={handlePrevious} disabled={currentPage === 1} className="pagination-button">
                Previous
            </button>
            <span className="pagination-info">
                Page{' '}
                <input
                    type="number"
                    value={inputPage}
                    onChange={handleInputChange}
                    onBlur={handleInputBlur}
                    onKeyPress={handleInputKeyPress}
                    className="pagination-input"
                    min="1"
                    max={totalPages}
                />
                {' '}of {totalPages}
            </span>
            <button onClick={handleNext} disabled={currentPage === totalPages} className="pagination-button">
                Next
            </button>
        </div>
    );
};

export default Pagination;