import React, { useState, useEffect, useCallback } from 'react';
import { getCopyrightTypes, manageCopyrightTypes, refreshCache } from '../../services/api_copyright_viz';

const Settings = () => {
    const [types, setTypes] = useState([]);
    const [newTypeName, setNewTypeName] = useState('');
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const fetchTypes = useCallback(async () => {
        setLoading(true);
        setError(null);
        try {
            const result = await getCopyrightTypes();
            setTypes(result.data);
        } catch (err) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchTypes();
    }, [fetchTypes]);

    const handleManageType = async (action, id = null, name = null) => {
        setLoading(true);
        setError(null);
        try {
            await manageCopyrightTypes({ action, id, name });
            fetchTypes();
            setNewTypeName('');
        } catch (err) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };

    const handleRefreshCache = async () => {
        setLoading(true);
        setError(null);
        try {
            await refreshCache();
            alert('Cache refresh initiated.');
        } catch (err) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };

    if (error) return <p style={{ color: 'red' }}>{error}</p>;

    return (
        <div>
            <h1>Settings</h1>

            <div className="settings-section">
                <h2>Type Taxonomy</h2>
                {loading && <p>Loading...</p>}
                <ul>
                    {types.map(type => (
                        <li key={type.id}>
                            {type.name}
                            <button onClick={() => handleManageType('delete', type.id)} disabled={loading}>Delete</button>
                        </li>
                    ))}
                </ul>
                <input
                    type="text"
                    value={newTypeName}
                    onChange={(e) => setNewTypeName(e.target.value)}
                    placeholder="New type name"
                />
                <button onClick={() => handleManageType('add', null, newTypeName)} disabled={!newTypeName || loading}>Add Type</button>
            </div>

            <div className="settings-section">
                <h2>Cache Management</h2>
                <button onClick={handleRefreshCache} disabled={loading}>Refresh Cache</button>
            </div>

            <div className="settings-section">
                <h2>Maintenance</h2>
                <p>Maintenance actions not yet implemented.</p>
            </div>
        </div>
    );
};

export default Settings;