import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
    Box,
    Typography,
    Select,
    MenuItem,
    FormControl,
    InputLabel,
    Grid,
    Card,
    CardMedia,
    CardContent,
    CircularProgress,
    Alert,
    Pagination,
    IconButton,
    Tooltip,
    Modal,
    Paper,
    Button, // Added Button
} from '@mui/material';
import ThumbUpIcon from '@mui/icons-material/ThumbUp';
import ThumbUpOutlinedIcon from '@mui/icons-material/ThumbUpOutlined';
import ZoomInIcon from '@mui/icons-material/ZoomIn';
import ClearIcon from '@mui/icons-material/Clear'; // Added ClearIcon
// Use named imports for API functions
import { listModels, getCombinedScores, getResultsByModel, addGroundTruth, removeGroundTruth } from '../../services/api_model_workbench'; // Added getCombinedScores

const RESULTS_PER_PAGE = 10; // Or make this configurable

const ByModelView = ({ ipCategory }) => {
    const [models, setModels] = useState([]);
    const [selectedModelId, setSelectedModelId] = useState('');
    const [results, setResults] = useState([]);
    const [currentPage, setCurrentPage] = useState(1); // Step 1: Dedicated current page state
    const [paginationMeta, setPaginationMeta] = useState({ totalPages: 1 }); // Step 2: Separate pagination meta
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [imageModalOpen, setImageModalOpen] = useState(false);
    const [selectedImage, setSelectedImage] = useState('');
    const INITIAL_SUGGESTIONS_COUNT = 5; // Number of suggestions to show initially
    // Removed suggestionsApiLimit, will manage per product and a max for API
    const [lastApiLimit, setLastApiLimit] = useState(INITIAL_SUGGESTIONS_COUNT);

    const handleOpenImageModal = (imageUrl) => {
        setSelectedImage(imageUrl);
        setImageModalOpen(true);
    };

    const handleCloseImageModal = () => {
        setImageModalOpen(false);
        setSelectedImage('');
    };

    // Fetch models and combined scores
    useEffect(() => {
        const fetchModels = async () => {
            setLoading(true);
            setError(null);
            try {
                // Fetch active models for the category
                // TODO: The backend listModels doesn't filter by category yet, filter client-side for now
                const modelsResponse = await listModels(); // Fetch all models
                const combinedScoresResponse = await getCombinedScores({ ip_category: ipCategory, is_active: true }); // Fetch active combined scores for category

                // Combine and filter models applicable to this category
                const applicableModels = (modelsResponse.data || [])
                    .filter(m => m.is_active && (m.applicable_ip_category.includes(ipCategory) || m.applicable_ip_category.includes('all')))
                    .map(m => ({ id: m.model_id, name: m.model_name, type: 'model' }));

                const applicableCombined = (combinedScoresResponse.data || [])
                    .map(c => ({ id: c.config_id, name: c.config_name, type: 'combined' }));

                const allApplicable = [...applicableModels, ...applicableCombined];

                setModels(allApplicable);
                if (allApplicable.length > 0) {
                    // Optionally pre-select the first model
                    // setSelectedModelId(allApplicable[0].id);
                } else {
                    setError(`No active models or combined scores found for ${ipCategory}.`);
                }
            } catch (err) {
                console.error("Error fetching models:", err);
                setError(`Failed to fetch models for ${ipCategory}. Please try again later.`);
                setModels([]);
            } finally {
                setLoading(false);
            }
        };

        if (ipCategory) {
            fetchModels();
        }
    }, [ipCategory]);

    // Step 4: Refactor useEffect for Resetting Page on Filter Change
    useEffect(() => {
        // Reset to page 1 when selectedModelId or ipCategory changes
        // This will trigger the data fetching effect for the new page 1
        setCurrentPage(1);
        setResults([]); // Clear results, so displayedSuggestionsCount are effectively reset for new model/category
        setLastApiLimit(INITIAL_SUGGESTIONS_COUNT); // Reset last API limit as well
    }, [selectedModelId, ipCategory]);

    // Step 5: Review fetchResults (or equivalent data fetching function)
    // Step 3: Refactor useEffect for Fetching Results
    const fetchResults = useCallback(async (pageToFetch, currentResultsSnapshot) => {
        if (!selectedModelId) {
            setResults([]);
            setPaginationMeta({ totalPages: 1 });
            return;
        }

        setLoading(true);
        setError(null);
        try {
            let limitForApi = INITIAL_SUGGESTIONS_COUNT;
            if (currentResultsSnapshot && currentResultsSnapshot.length > 0) {
                limitForApi = Math.max(
                    INITIAL_SUGGESTIONS_COUNT,
                    ...currentResultsSnapshot.map(r => r.displayedSuggestionsCount || INITIAL_SUGGESTIONS_COUNT)
                );
            }
            setLastApiLimit(limitForApi);

            const params = {
                model_id: selectedModelId,
                ip_category: ipCategory,
                page: pageToFetch,
                per_page: RESULTS_PER_PAGE,
                limit: limitForApi,
            };
            const response = await getResultsByModel(params);
            const newApiResultsForPage = response.data.results || [];

            const processedResults = newApiResultsForPage.map(apiProd => {
                const existingProductState = currentResultsSnapshot.find(
                    r => r.product_image?.id === apiProd.product_image?.id
                );
                const displayCount = existingProductState
                    ? existingProductState.displayedSuggestionsCount
                    : INITIAL_SUGGESTIONS_COUNT;
                return {
                    ...apiProd,
                    displayedSuggestionsCount: displayCount,
                    model_suggestions: apiProd.model_suggestions || [], // Ensure it's an array
                };
            });

            setResults(processedResults);
            setPaginationMeta({
                totalPages: response.data.pagination?.total_pages || 1,
            });
        } catch (err) {
            console.error("Error fetching results:", err);
            setError(`Failed to fetch results for model ${selectedModelId}. ${err.response?.data?.detail || err.response?.data?.error || err.message}`);
            setResults([]); // Clear results on error
            setPaginationMeta({ totalPages: 1 });
        } finally {
            setLoading(false);
        }
    }, [selectedModelId, ipCategory]); // Removed suggestionsApiLimit, fetchResults itself doesn't depend on 'results' state directly in definition

    // Effect for fetching results based on currentPage, selectedModelId, or ipCategory
    useEffect(() => {
        if (selectedModelId && ipCategory) {
            // Pass the current 'results' state to fetchResults for limit calculation
            fetchResults(currentPage, results);
        }
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [selectedModelId, ipCategory, currentPage, fetchResults]); // REMOVED 'results' to prevent loop


    const handleModelChange = (event) => {
        setSelectedModelId(event.target.value);
        // setCurrentPage(1) is handled by the useEffect hook dependent on selectedModelId
    };

    const handlePageChange = (event, value) => {
        setCurrentPage(value); // Step 1: Pagination onChange calls setCurrentPage
    };

    const handleGroundTruthToggle = async (productImageId, ipImageId, currentIsGroundTruth) => {
        setError(null);
        try {
            if (currentIsGroundTruth) {
                await removeGroundTruth(productImageId, ipImageId);
            } else {
                await addGroundTruth(productImageId, ipImageId);
            }
            fetchResults(currentPage, results); // Refresh results for the current page
        } catch (err) {
            console.error("Error updating ground truth:", err);
            setError(`Failed to update ground truth. ${err.response?.data?.detail || err.response?.data?.error || err.message}`);
        }
    };

    const handleRemoveDirectGroundTruth = async (productId, ipId) => {
        if (!productId) {
            setError("Cannot remove ground truth: Product ID is missing.");
            console.error("Product ID is missing for removeGroundTruth call.");
            return;
        }
        setError(null);
        try {
            await removeGroundTruth(productId, ipId);
            fetchResults(currentPage, results); // Refresh results for the current page
        } catch (err) {
            console.error("Error removing ground truth directly:", err);
            setError(`Failed to remove ground truth. ${err.response?.data?.detail || err.response?.data?.error || err.message}`);
        }
    };

    const handleShowMoreSuggestions = (productId) => {
        // Create the next state for 'results' by updating the specific product's display count
        const updatedResults = results.map(p => {
            if (p.product_image?.id === productId) {
                return {
                    ...p,
                    displayedSuggestionsCount: (p.displayedSuggestionsCount || INITIAL_SUGGESTIONS_COUNT) + INITIAL_SUGGESTIONS_COUNT,
                };
            }
            return p;
        });

        setResults(updatedResults); // Apply the UI change immediately

        // Now, determine if an API call is needed because the overall max displayed suggestions increased
        const overallMaxDisplayed = Math.max(
            INITIAL_SUGGESTIONS_COUNT,
            ...updatedResults.map(r => r.displayedSuggestionsCount || INITIAL_SUGGESTIONS_COUNT)
        );

        // If the new max count required for display exceeds the limit used for the last API call,
        // then we need to fetch results again with an updated limit.
        if (overallMaxDisplayed > lastApiLimit) {
            // Pass currentPage and the *newly updated* results state (updatedResults)
            // so fetchResults can calculate the correct new limitForApi.
            fetchResults(currentPage, updatedResults);
        }
    };
 
    // Consistent image URL construction
    const getImageUrl = (imageId) => imageId ? `/api/data/images/file/${imageId}` : '';


    return (
        <Box sx={{ p: 3 }}>
            <Typography variant="h5" gutterBottom>
                Results by Model ({ipCategory.charAt(0).toUpperCase() + ipCategory.slice(1)})
            </Typography>

            <FormControl fullWidth sx={{ mb: 3 }}>
                <InputLabel id="model-select-label">Select Model</InputLabel>
                <Select
                    labelId="model-select-label"
                    id="model-select"
                    value={selectedModelId}
                    label="Select Model"
                    onChange={handleModelChange}
                    disabled={loading || models.length === 0}
                >
                    <MenuItem value="" disabled>
                        <em>Select a model</em>
                    </MenuItem>
                    {models.map((model) => (
                        <MenuItem key={model.id} value={model.id}>
                            {model.name}
                        </MenuItem>
                    ))}
                </Select>
            </FormControl>

            {loading && <CircularProgress sx={{ display: 'block', margin: 'auto', my: 2 }} />}
            {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}

            {!loading && !error && !selectedModelId && (
                <Alert severity="info">Please select a model to view results.</Alert>
            )}

            {!loading && !error && selectedModelId && results.length === 0 && (
                 <Alert severity="info">No results found for the selected model and category.</Alert>
            )}

            {results.length > 0 && (
                <Box>
                    <Grid container spacing={3}>
                        {/* Each 'productResult' is an item from the 'results' array */}
                        {results.map((productResult) => (
                            // Use product_image.id as key for the outer Grid item
                            <Grid item xs={12} key={productResult.product_image?.id || Math.random()}>
                                <Card variant="outlined">
                                    <CardContent>
                                        <Grid container spacing={2} alignItems="flex-start">
                                            {/* Product Image Section */}
                                            <Grid item xs={12} sm={2} md={1.5} sx={{ textAlign: 'center' }}>
                                                <Typography variant="subtitle2" gutterBottom>Product</Typography>
                                                <CardMedia
                                                    component="img"
                                                    sx={{ width: 100, height: 100, objectFit: 'contain', margin: 'auto', mb: 1, cursor: 'pointer', border: '1px solid lightgray', borderRadius: 1 }}
                                                    image={getImageUrl(productResult.product_image?.id)}
                                                    alt={`Product ${productResult.product_image?.filename || 'N/A'}`}
                                                    onClick={() => productResult.product_image?.id && handleOpenImageModal(getImageUrl(productResult.product_image.id))}
                                                />
                                                <Typography variant="caption" display="block">ID: {productResult.product_image?.id || 'N/A'}</Typography>
                                                <Typography variant="caption" display="block">File: {productResult.product_image?.filename || 'N/A'}</Typography>
                                                {/* Gracefully handle missing product_image.ip_category */}
                                                <Typography variant="caption" display="block" sx={{ fontStyle: 'italic' }}>
                                                    Category: {productResult.product_image?.ip_category || 'N/A'}
                                                </Typography>
                                            </Grid>

                                            {/* Ground Truth IPs Section */}
                                            <Grid item xs={12} sm={10} md={3.5}>
                                                <Typography variant="subtitle2" gutterBottom>Ground Truth IPs</Typography>
                                                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                                                    {/* Use 'ground_truth_ips' list name and 'item.id' for IP image identifiers */}
                                                    {productResult.ground_truth_ips && productResult.ground_truth_ips.length > 0 ? (
                                                        productResult.ground_truth_ips.map(gt_item => ( // gt_item is an IPImageSchema object
                                                            <Tooltip key={gt_item.id} title={`IP ID: ${gt_item.id} | File: ${gt_item.filename || 'N/A'} | Owner: ${gt_item.ip_owner || 'N/A'}`}>
                                                                <Paper variant="outlined" sx={{ p: 0.5, textAlign: 'center', width: 'auto', minWidth: 90, display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 0.5 }}>
                                                                    <CardMedia
                                                                        component="img"
                                                                        sx={{ width: 70, height: 70, objectFit: 'contain', cursor: 'pointer', border: '1px solid lightgray', borderRadius: 1 }}
                                                                        image={getImageUrl(gt_item.id)}
                                                                        alt={`Ground Truth ${gt_item.filename || gt_item.id}`}
                                                                        onClick={() => handleOpenImageModal(getImageUrl(gt_item.id))}
                                                                    />
                                                                    <Typography variant="caption" display="block" sx={{ maxWidth: 80, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                                                                        {gt_item.filename || gt_item.id.substring(0,8)}
                                                                    </Typography>
                                                                    <Tooltip title="Remove from Ground Truth">
                                                                        <IconButton
                                                                            size="small"
                                                                            onClick={() => {
                                                                                if (productResult.product_image && productResult.product_image.id) {
                                                                                    handleRemoveDirectGroundTruth(productResult.product_image.id, gt_item.id);
                                                                                } else {
                                                                                    console.error("Product image ID is not available for removing ground truth.");
                                                                                    setError("Cannot remove ground truth: Product information missing.");
                                                                                }
                                                                            }}
                                                                            aria-label="Remove from Ground Truth"
                                                                        >
                                                                            <ClearIcon fontSize="small" />
                                                                        </IconButton>
                                                                    </Tooltip>
                                                                </Paper>
                                                            </Tooltip>
                                                        ))
                                                    ) : (
                                                        <Typography variant="caption">None specified.</Typography>
                                                    )}
                                                </Box>
                                            </Grid>

                                            {/* Model Suggestions Section */}
                                            <Grid item xs={12} md={7}>
                                                {/* Use 'model_suggestions' list name */}
                                                <Typography variant="subtitle2" gutterBottom>
                                                    Model Suggestions (Displaying up to {productResult.displayedSuggestionsCount || INITIAL_SUGGESTIONS_COUNT})
                                                </Typography>
                                                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                                                    {(productResult.model_suggestions && productResult.model_suggestions.length > 0) ? (
                                                        productResult.model_suggestions
                                                            .slice(0, productResult.displayedSuggestionsCount || INITIAL_SUGGESTIONS_COUNT)
                                                            .map((suggestion) => ( // suggestion has ip_image_id, similarity_score, is_ground_truth, ip_filename, ip_owner
                                                                <Paper
                                                                    key={`${productResult.product_image?.id}-${suggestion.ip_image_id}`}
                                                                    variant="outlined"
                                                                    sx={{ p: 1, textAlign: 'center', border: suggestion.is_ground_truth ? '2px solid green' : '1px solid lightgray', width: 120 }}
                                                                >
                                                                    <CardMedia
                                                                        component="img"
                                                                        sx={{ width: 80, height: 80, objectFit: 'contain', margin: 'auto', mb: 1, cursor: 'pointer' }}
                                                                        image={getImageUrl(suggestion.ip_image_id)}
                                                                        alt={`Suggestion ${suggestion.ip_filename || suggestion.ip_image_id.substring(0,8)}`}
                                                                        onClick={() => handleOpenImageModal(getImageUrl(suggestion.ip_image_id))}
                                                                    />
                                                                    <Tooltip title={suggestion.ip_filename || `IP ID: ${suggestion.ip_image_id}`}>
                                                                        <Typography variant="caption" display="block" sx={{ overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                                                                            {suggestion.ip_filename || `ID: ${suggestion.ip_image_id.substring(0,8)}`}
                                                                        </Typography>
                                                                    </Tooltip>
                                                                    <Typography variant="caption" display="block">Score: {suggestion.similarity_score?.toFixed(4) ?? 'N/A'}</Typography>
                                                                    <Tooltip title={suggestion.ip_owner || 'No Owner'}>
                                                                        <Typography variant="caption" display="block" sx={{ overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                                                                            Owner: {suggestion.ip_owner || 'N/A'}
                                                                        </Typography>
                                                                    </Tooltip>
                                                                    <Tooltip title={suggestion.is_ground_truth ? "Marked as Ground Truth" : "Mark as Ground Truth"}>
                                                                        <span>
                                                                            <IconButton
                                                                                size="small"
                                                                                color={suggestion.is_ground_truth ? "success" : "default"}
                                                                                onClick={() => handleGroundTruthToggle(productResult.product_image.id, suggestion.ip_image_id, suggestion.is_ground_truth)}
                                                                            >
                                                                                {suggestion.is_ground_truth ? <ThumbUpIcon fontSize="small" /> : <ThumbUpOutlinedIcon fontSize="small" />}
                                                                            </IconButton>
                                                                        </span>
                                                                    </Tooltip>
                                                                </Paper>
                                                            ))
                                                    ) : (
                                                        <Typography variant="caption">No suggestions from this model.</Typography>
                                                    )}
                                                </Box>
                                                {(() => {
                                                    const productSuggestions = productResult.model_suggestions || [];
                                                    const displayCount = productResult.displayedSuggestionsCount || INITIAL_SUGGESTIONS_COUNT;
                                                    const hasFetchedSuggestions = productSuggestions.length > 0;
                                                    
                                                    const canDisplayMoreOfFetched = hasFetchedSuggestions && displayCount < productSuggestions.length;
                                                    const hitApiLimitAndDisplayedAllFetched = hasFetchedSuggestions && displayCount === productSuggestions.length && productSuggestions.length === lastApiLimit;
                                                    
                                                    const showMoreButton = canDisplayMoreOfFetched || hitApiLimitAndDisplayedAllFetched;

                                                    if (productResult.product_image?.id && showMoreButton) {
                                                        return (
                                                            <Button
                                                                onClick={() => handleShowMoreSuggestions(productResult.product_image.id)}
                                                                variant="outlined"
                                                                size="small"
                                                                sx={{ mt: 2, display: 'block', mx: 'auto' }}
                                                            >
                                                                Show 5 More Suggestions
                                                            </Button>
                                                        );
                                                    }
                                                    return null;
                                                })()}
                                            </Grid>
                                        </Grid>
                                    </CardContent>
                                </Card>
                            </Grid>
                        ))}
                    </Grid>

                    {/* Pagination - uses paginationMeta.totalPages and currentPage */}
                    {paginationMeta.totalPages > 1 && (
                        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
                            <Pagination
                                count={paginationMeta.totalPages} // Step 2: Use paginationMeta
                                page={currentPage} // Step 1: Bind page to currentPage
                                onChange={handlePageChange} // Step 1: onChange calls setCurrentPage (via handlePageChange)
                                color="primary"
                                disabled={loading}
                            />
                        </Box>
                    )}
                </Box>
            )}

            {/* Image Enlarge Modal */}
            <Modal
                open={imageModalOpen}
                onClose={handleCloseImageModal}
                aria-labelledby="enlarge-image-modal-title"
                aria-describedby="enlarge-image-modal-description"
            >
                <Box sx={{
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)',
                    bgcolor: 'background.paper',
                    boxShadow: 24,
                    p: 2, // Padding around the image
                    outline: 'none',
                    maxWidth: '90vw', // Max width relative to viewport width
                    maxHeight: '90vh', // Max height relative to viewport height
                    display: 'flex', // Use flexbox for centering
                    justifyContent: 'center', // Center horizontally
                    alignItems: 'center', // Center vertically
                }}>
                    <img
                        src={selectedImage}
                        alt="Enlarged view"
                        style={{ maxWidth: '100%', maxHeight: '100%', objectFit: 'contain' }} // Image scales within the box
                    />
                </Box>
            </Modal>
        </Box>
    );
};

export default ByModelView;
