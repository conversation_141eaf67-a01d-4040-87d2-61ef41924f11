"""
Orphan Fixer Class

This module contains the OrphanFixer class which handles all orphan fixing operations.
"""

from flask import current_app
from backend.api.copyright.move_cn_to_copyright import save_image_ssd_and_cos
from backend.extensions import db
from backend.database.copyrights_file_model import CopyrightsFiles
from backend.utils.cache_utils import get_case_df
from backend.utils.vector_store import get_qdrant_client
from backend.api.copyright.helpers import QDRANT_COLLECTION_NAME, download_image_bytes, get_certificate_path, get_high_res_path, get_low_res_path
from qdrant_client import models
from datetime import datetime
import uuid
import traceback

from .orphan_helpers import (
    get_plaintiff_lookup, generate_file_paths, get_specific_qdrant_points,
    extract_method_from_filename, generate_unique_filename
)
from .orphan_types import FixResult


class OrphanFixer:
    """Class for fixing different types of orphan records."""

    def __init__(self):
        self.plaintiff_lookup = None
        self._init_cache()

    def _init_cache(self):
        """Initialize cached data."""
        try:
            self.plaintiff_lookup = get_plaintiff_lookup()
        except Exception as e:
            current_app.logger.error(f"Error initializing cache: {str(e)}")

    def fix_court_only_assets(self, action, ids):
        """Fix court-only copyright assets."""
        deleted_count = 0
        errors = []

        # Court records are typically read-only
        for asset_id in ids:
            try:
                current_app.logger.info(f"Court-only record deletion requested for: {asset_id}")
                current_app.logger.warning("Note: Court records are typically read-only and may not be deletable")
                deleted_count += 1
            except Exception as e:
                errors.append(f"Failed to delete court record {asset_id}: {str(e)}")

        return deleted_count, errors

    def fix_db_only_assets(self, action, ids):
        """Fix database-only copyright assets."""
        deleted_count = 0
        errors = []

        for asset_id in ids:
            try:
                try:
                    asset_uuid = uuid.UUID(asset_id)
                except ValueError:
                    errors.append(f"Invalid UUID format: {asset_id}")
                    continue

                db_record = CopyrightsFiles.query.get(asset_uuid)
                if db_record:
                    current_app.logger.info(f"Deleting db-only record: {asset_id} (filename: {db_record.filename})")
                    db.session.delete(db_record)
                    db.session.commit()
                    deleted_count += 1
                else:
                    errors.append(f"Record not found: {asset_id}")
            except Exception as e:
                db.session.rollback()
                current_app.logger.error(f"Error deleting db-only record {asset_id}: {str(e)}")
                errors.append(f"Failed to delete database record {asset_id}: {str(e)}")

        return deleted_count, errors

    def fix_prod_no_qdrant_assets(self, action, ids):
        """Fix production files missing from Qdrant."""
        deleted_count = 0
        errors = []

        for asset_id in ids:
            try:
                current_app.logger.info(f"TODO: Delete prod-no-qdrant record {asset_id}")
                deleted_count += 1
            except Exception as e:
                errors.append(f"Failed to delete non-production record {asset_id}: {str(e)}")

        return deleted_count, errors

    def _fix_qdrant_only_assets_delete_qdrant(self, ids):
        """Delete qdrant-only records from Qdrant."""
        deleted_count = 0
        errors = []

        for asset_id in ids:
            try:
                client = get_qdrant_client()
                if client:
                    client.delete(
                        collection_name=QDRANT_COLLECTION_NAME,
                        points_selector=models.PointIdsList(points=[asset_id])
                    )
                    current_app.logger.info(f"Deleted qdrant-only record {asset_id} from Qdrant")
                    deleted_count += 1
                else:
                    errors.append(f"Failed to connect to Qdrant client for {asset_id}")
            except Exception as e:
                current_app.logger.error(f"Error deleting qdrant-only record {asset_id}: {str(e)}")
                errors.append(f"Failed to delete Qdrant record {asset_id}: {str(e)}")

        return deleted_count, errors

    def _fix_qdrant_only_assets_add_prod_true(self, ids):
        """Add qdrant-only records to database with production=true."""
        added_count = 0
        errors = []

        try:
            # Fetch specific points from Qdrant
            qdrant_points = get_specific_qdrant_points(ids)

            if not qdrant_points:
                errors.append("No points found in Qdrant for the provided IDs")
                return added_count, errors

            # Process each point
            for point in qdrant_points:
                try:
                    payload = point.payload
                    if not payload:
                        errors.append(f"Point {point.id} has no payload")
                        continue

                    # Extract required fields from payload
                    reg_no = payload.get('reg_no')
                    filename = payload.get('filename')

                    if not reg_no or not filename:
                        errors.append(f"Point {point.id} missing reg_no or filename in payload")
                        continue

                    # Check if copyright exists in database
                    from backend.database.copyright_model import Copyrights
                    copyright_record = Copyrights.query.filter_by(registration_number=reg_no).first()
                    copyright_files_record = CopyrightsFiles.query.filter_by(filename=filename).first()
                    if copyright_record.plaintiff_id == None:
                        copyright_record.plaintiff_id = payload.get('plaintiff_id')

                    if not copyright_record:
                        # Create new copyright record using data from Qdrant payload
                        plaintiff_id = payload.get('plaintiff_id')
                        plaintiff_name = payload.get('plaintiff_name')

                        if not plaintiff_id or not plaintiff_name:
                            errors.append(f"Point {point.id} missing plaintiff_id or plaintiff_name in payload for creating copyright record")
                            continue

                        try:
                            new_copyright = Copyrights(
                                id=uuid.uuid4(),
                                registration_number=reg_no,
                                type_of_work=None,  # Default values since not provided in payload
                                title=None,
                                date_of_creation=None,
                                tro=True,
                                date_of_publication=None,
                                copyright_claimant=None,
                                authorship_on_application=None,
                                names=plaintiff_name,
                                plaintiff_id=plaintiff_id,
                                create_time=datetime.now(),
                                update_time=datetime.now(),
                                deleted=False,
                                certificate_status=None
                            )

                            db.session.add(new_copyright)
                            db.session.commit()
                            copyright_record = new_copyright
                            current_app.logger.info(f"Created new copyright record: reg_no={reg_no}, plaintiff_id={plaintiff_id}")

                        except Exception as e:
                            db.session.rollback()
                            error_msg = f"Error creating copyright record for reg_no {reg_no}: {str(e)}"
                            current_app.logger.error(error_msg)
                            errors.append(error_msg)
                            continue

                    if copyright_files_record:
                        copyright_files_record.production = True
                    else:
                        method = extract_method_from_filename(filename)
                        if not method:
                            current_app.logger.warning(f"Could not extract method from filename: {filename}")

                        file_extension = filename.split('.')[-1] if '.' in filename else 'webp'
                        unique_filename = generate_unique_filename(reg_no, method, file_extension)

                        current_app.logger.info(f"Generated unique filename: {unique_filename} (original: {filename})")

                        new_file_entry = CopyrightsFiles(
                            id=uuid.uuid4(),
                            filename=unique_filename,
                            registration_number=reg_no,
                            method=method,
                            production=True,
                            type='image',
                            create_time=datetime.now(),
                            update_time=datetime.now()
                        )
                        db.session.add(new_file_entry)
                        
                    db.session.commit()
                    current_app.logger.info(f"Successfully added Copyright_files entry: reg_no={reg_no}, filename={filename}, method={method}")
                    added_count += 1

                except Exception as e:
                    db.session.rollback()
                    error_msg = f"Error processing point {point.id}: {str(e)}"
                    current_app.logger.error(error_msg)
                    errors.append(error_msg)
                    continue

        except Exception as e:
            error_msg = f"Error in add_prod_true operation: {str(e)}"
            current_app.logger.error(error_msg)
            errors.append(error_msg)

        return added_count, errors

    def _fix_qdrant_only_assets_add_prod_true_remove_similar(self, ids):
        """Add qdrant-only records with production=true and remove similar images."""
        processed_count = 0
        errors = []

        for item in ids:
            try:
                similar_point = item.get('similar_point')
                if not similar_point:
                    errors.append(f"Item missing 'similar_point': {item}")
                    continue

                filename = similar_point.get('filename')
                similar_id = similar_point.get('id')

                if not filename or not similar_id:
                    errors.append(f"similar_point missing filename or id: {similar_point}")
                    continue

                # Fetch the record from CopyrightsFiles using filename
                db_record = CopyrightsFiles.query.filter_by(filename=filename).first()
                if db_record:
                    # Set production to False
                    db_record.production = False
                    db.session.commit()
                    current_app.logger.info(f"Set production=False for filename: {filename}")
                else:
                    current_app.logger.warning(f"No record found for filename: {filename}")

                # Delete the QDrant point
                client = get_qdrant_client()
                if client:
                    client.delete(
                        collection_name=QDRANT_COLLECTION_NAME,
                        points_selector=models.PointIdsList(points=[similar_id])
                    )
                    current_app.logger.info(f"Deleted QDrant point: {similar_id}")
                else:
                    errors.append(f"Failed to connect to QDrant client for {similar_id}")

                # Add point_id to database using existing function
                point_id = item.get('id')
                if point_id:
                    add_count, add_errors = self._fix_qdrant_only_assets_add_prod_true([point_id])
                    processed_count += add_count
                    errors.extend(add_errors)
                    current_app.logger.info(f"Added point_id {point_id} to database")
                else:
                    errors.append(f"Item missing 'point_id': {item}")

                processed_count += 1

            except Exception as e:
                db.session.rollback()
                current_app.logger.error(f"Error processing item {item}: {str(e)}")
                errors.append(f"Failed to process item {item}: {str(e)}")

        return processed_count, errors
    def _copy_image_files(self,data):
            cases = get_case_df()
            docket = data.get('docket')
            if not docket:
                current_app.logger.error("No docket provided in data")
                return
            matching_cases = cases[cases['docket'] == docket]
            if matching_cases.empty:
                current_app.logger.warning(f"No case found for docket: {docket}")
                return
            case_id = matching_cases.iloc[0]['id']

            img_bytes = download_image_bytes(get_high_res_path(data.get('plaintiff_id'), data.get('filename')))
            if img_bytes:
                list(save_image_ssd_and_cos(
                    image_data=img_bytes,
                    filename=data.get('filename'),
                    plaintiff_id=data.get('db_plaintiff_id'),
                    case_id=case_id
                ))

            img_bytes = download_image_bytes(get_low_res_path(data.get('plaintiff_id'), data.get('filename')))
            if img_bytes:
                list(save_image_ssd_and_cos(
                    image_data=img_bytes,
                    filename=data.get('filename'),
                    plaintiff_id=data.get('db_plaintiff_id'),
                    case_id=case_id
                ))

            img_bytes = download_image_bytes(get_certificate_path(data.get('plaintiff_id'), data.get('filename')))
            if img_bytes:
                list(save_image_ssd_and_cos(
                    image_data=img_bytes,
                    filename=data.get('filename'),
                    plaintiff_id=data.get('db_plaintiff_id'),
                    case_id=case_id
                ))
    def _fix_qdrant_only_assets_set_db_plaintiff_id(self, payload_items):
        """Update plaintiff_id and plaintiff_name in QDrant for qdrant-only records."""
        updated_payload = []
        errors = []

        try:
            # Extract qdrant_ids from payload items
            qdrant_ids = []
            payload_map = {}

            for item in payload_items:
                data = item.get('data', {})
                qdrant_id = data.get('qdrant_id')
                if qdrant_id:
                    qdrant_ids.append(qdrant_id)
                    payload_map[qdrant_id] = {
                        'db_plaintiff_id': data.get('db_plaintiff_id'),
                        'db_plaintiff_name': data.get('db_plaintiff_name')
                    }
                else:
                    errors.append(f"Item missing qdrant_id: {item}")

            if not qdrant_ids:
                errors.append("No valid qdrant_ids found in payload")
                return updated_payload, errors

            # Fetch QDrant points
            qdrant_points = get_specific_qdrant_points(qdrant_ids)

            if not qdrant_points:
                errors.append("No points found in QDrant for the provided IDs")
                return updated_payload, errors

            # Prepare payload update operations
            update_operations = []

            for point in qdrant_points:
                try:
                    qdrant_id = point.id
                    if qdrant_id not in payload_map:
                        errors.append(f"No update data found for point {qdrant_id}")
                        continue

                    update_data = payload_map[qdrant_id]

                    # Get current payload
                    current_payload = point.payload if point.payload else {}

                    # Update plaintiff_id and plaintiff_name
                    updated_payload_data = current_payload.copy()
                    updated_payload_data['plaintiff_id'] = update_data['db_plaintiff_id']
                    updated_payload_data['plaintiff_name'] = update_data['db_plaintiff_name']

                    # Create set_payload operation instead of full PointStruct
                    update_operations.append(
                        models.SetPayloadOperation(
                            set_payload=models.SetPayload(
                                payload=updated_payload_data,
                                points=[qdrant_id]
                            )
                        )
                    )
                    self._copy_image_files(data)
                    current_app.logger.info(f"Prepared payload update for point {qdrant_id}: plaintiff_id={update_data['db_plaintiff_id']}, plaintiff_name={update_data['db_plaintiff_name']}")

                except Exception as e:
                    error_msg = f"Error preparing payload update for point {point.id}: {str(e)}"
                    current_app.logger.error(error_msg)
                    print(traceback.format_exc())
                    errors.append(error_msg)
                    continue

            # Execute batch payload updates
            if update_operations:
                client = get_qdrant_client()
                if client:
                    client.batch_update_points(
                        collection_name=QDRANT_COLLECTION_NAME,
                        update_operations=update_operations,
                        wait=True
                    )
                    current_app.logger.info(f"Successfully updated payloads for {len(update_operations)} QDrant points")
                else:
                    errors.append("Failed to connect to QDrant client for payload updates")
            else:
                errors.append("No payload update operations prepared")

        except Exception as e:
            error_msg = f"Error in set_db_plaintiff_id operation: {str(e)}"
            current_app.logger.error(error_msg)
            errors.append(error_msg)

        # Create updated payload with modified data
        for item in payload_items:
            try:
                data = item.get('data', {})
                qdrant_id = data.get('qdrant_id')

                if qdrant_id and qdrant_id in payload_map:
                    update_data = payload_map[qdrant_id]
                    # Create a copy of the item with updated data
                    updated_item = item.copy()
                    updated_data = data.copy()
                    updated_data['plaintiff_id'] = update_data['db_plaintiff_id']
                    updated_data['plaintiff_name'] = update_data['db_plaintiff_name']
                    updated_item['data'] = updated_data
                    updated_payload.append(updated_item)
                else:
                    # If no update data, append original item
                    updated_payload.append(item)
            except Exception as e:
                current_app.logger.error(f"Error updating payload item: {str(e)}")
                updated_payload.append(item)

        return updated_payload, errors

    def _fix_qdrant_only_assets_set_qdrant_plaintiff_id(self, ids):
        """Update plaintiff_id in database and modify payload for qdrant-only records."""
        errors = []
        updated_payload = []

        for item in ids:
            try:
                data = item.get('data', {})
                registration_number = data.get('registration_number')
                plaintiff_id = data.get('plaintiff_id')
                plaintiff_name = data.get('plaintiff_name')
                filename = data.get('filename')

                if not registration_number or plaintiff_id is None:
                    errors.append(f"Missing registration_number or plaintiff_id in item: {item}")
                    updated_payload.append(item)
                    continue

                # Query the database for the Copyright record
                from backend.database.copyright_model import Copyrights
                copyright_record = Copyrights.query.filter_by(registration_number=registration_number).first()

                if copyright_record:
                    # Update the plaintiff_id in the database
                    copyright_record.plaintiff_id = plaintiff_id
                    db.session.commit()
                    current_app.logger.info(f"Updated plaintiff_id for registration_number {registration_number} to {plaintiff_id}")
                else:
                    new_copyright = Copyrights(
                                id=uuid.uuid4(),
                                registration_number=registration_number,
                                type_of_work=None,  # Default values since not provided in payload
                                title=None,
                                tro=True,
                                date_of_creation=None,
                                date_of_publication=None,
                                copyright_claimant=None,
                                authorship_on_application=None,
                                names=plaintiff_name,
                                plaintiff_id=plaintiff_id,
                                create_time=datetime.now(),
                                update_time=datetime.now(),
                                deleted=False,
                                certificate_status=None
                            )
                    
                    # Extract method from filename
                    method = extract_method_from_filename(filename)
                    if not method:
                        current_app.logger.warning(f"Could not extract method from filename: {filename}")

                    file_extension = filename.split('.')[-1] if '.' in filename else 'webp'
                    unique_filename = generate_unique_filename(registration_number, method, file_extension)
                    new_file_entry = CopyrightsFiles(
                        id=uuid.uuid4(),
                        filename=unique_filename,
                        registration_number=registration_number,
                        method=method,
                        production=True,
                        type='image',
                        create_time=datetime.now(),
                        update_time=datetime.now()
                    )
                    db.session.add(new_copyright)
                    db.session.add(new_file_entry)
                    db.session.commit()
                    current_app.logger.warning(f"No Copyright record found for registration_number {registration_number}.Created new Copyright")

                # Modify the payload
                updated_data = data.copy()
                updated_data['db_plaintiff_id'] = plaintiff_id
                updated_data['db_plaintiff_name'] = plaintiff_name

                updated_item = item.copy()
                updated_item['data'] = updated_data
                updated_payload.append(updated_item)

            except Exception as e:
                db.session.rollback()
                current_app.logger.error(f"Error processing item {item}: {str(e)}")
                errors.append(f"Failed to process item {item}: {str(e)}")
                updated_payload.append(item)

        return updated_payload, errors

    def fix_qdrant_only_assets(self, action, ids):
        """Fix qdrant-only copyright assets based on action."""
        action_handlers = {
            'add_prod_true': self._fix_qdrant_only_assets_add_prod_true,
            'add_prod_true_remove_similar': self._fix_qdrant_only_assets_add_prod_true_remove_similar,
            'set_db_plaintiff_id': self._fix_qdrant_only_assets_set_db_plaintiff_id,
            'set_qdrant_plaintiff_id': self._fix_qdrant_only_assets_set_qdrant_plaintiff_id,
            'delete_qdrant': self._fix_qdrant_only_assets_delete_qdrant
        }

        handler = action_handlers.get(action)
        if handler:
            return handler(ids)
        else:
            return 0, [f"Unknown action '{action}' for category 'qdrant-only'"]

    def fix_missing_cos_assets(self, action, ids):
        """Fix missing COS storage assets."""
        deleted_count = 0
        errors = []

        for asset_id in ids:
            try:
                current_app.logger.info(f"TODO: Delete missing-cos record {asset_id}")
                deleted_count += 1
            except Exception as e:
                errors.append(f"Failed to delete COS record {asset_id}: {str(e)}")

        return deleted_count, errors

    def fix_duplicate_reg_no_assets(self, action, ids):
        """Fix duplicate registration number assets."""
        deleted_count = 0
        errors = []

        for asset_id in ids:
            try:
                current_app.logger.info(f"TODO: Delete duplicate-reg-no record {asset_id}")
                deleted_count += 1
            except Exception as e:
                errors.append(f"Failed to delete duplicate record {asset_id}: {str(e)}")

        return deleted_count, errors
