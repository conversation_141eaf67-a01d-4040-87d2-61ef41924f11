from flask import Blueprint, jsonify, request, current_app, send_file
from backend.database.models_bounding_box import BoundingBoxModels, BoundingBoxPictures, BoundingBoxExperiments, BoundingBoxResults
from backend.extensions import db
import datetime
import os
import asyncio
import base64
import json
import threading

# Import AI functionality
try:
    from backend.AI.BoundingBox import get_image_with_bounding_boxes_async
    AI_AVAILABLE = True
    print("AI module successfully imported and available.")
except ImportError as e:
    print(f"Warning: AI module not available: {e}")
    AI_AVAILABLE = False

# Helper function to add to models for simple serialization
def to_dict_mixin(self):
    return {c.name: getattr(self, c.name) for c in self.__table__.columns}

BoundingBoxModels.to_dict = to_dict_mixin
BoundingBoxPictures.to_dict = to_dict_mixin
BoundingBoxExperiments.to_dict = to_dict_mixin
BoundingBoxResults.to_dict = to_dict_mixin


bounding_box_bp = Blueprint('bounding_box_api', __name__, url_prefix='/api/v1/boundingbox')


async def process_experiment_result(result_id, image_data, prompt, model_name, resize_width, resize_height, output_type):
    """
    Process a single experiment result using AI.
    """
    try:
        current_app.logger.info(f"Starting AI processing for result {result_id} with model {model_name}")

        # Update result status to processing
        result = BoundingBoxResults.query.get(result_id)
        if not result:
            current_app.logger.error(f"Result {result_id} not found")
            return

        result.status = 'processing'
        db.session.commit()

        # Convert base64 image data to bytes
        if image_data and image_data.startswith('data:image'):
            # Extract base64 data after the comma
            base64_data = image_data.split(',')[1]
            image_bytes = base64.b64decode(base64_data)
        else:
            raise ValueError(f"Invalid image_data format for result {result_id}")

        # Determine if segmentation masks are requested
        include_masks = "Segmentation" in output_type
        current_app.logger.info(f"Output type: '{output_type}', Include masks: {include_masks}")

        # Determine proper image format based on output_type
        if include_masks:
            image_format = "png"  # Use PNG for segmentation tasks (supports transparency)
        else:
            image_format = "png"  # Use PNG for bounding box tasks

        # Call the AI function with more lenient box size filtering
        processed_bytes, json_output, masks, output_file_path = await get_image_with_bounding_boxes_async(
            image_bytes=image_bytes,
            prompt=prompt,
            model_name=model_name,
            resize_x=resize_width,
            resize_y=resize_height,
            image_format=image_format,
            include_masks=include_masks,
            save_output=True,
            output_filename=f"result_{result_id}_{model_name.replace(' ', '_').replace('-', '_')}.{image_format}",
            min_box_size=5  # Use very lenient filtering (5x5 pixels minimum)
        )

        # Update result with AI output
        result = BoundingBoxResults.query.get(result_id)  # Refresh from DB
        if result:
            if processed_bytes and output_file_path:
                result.status = 'success'
                # Convert absolute path to relative URL for frontend
                if output_file_path:
                    filename = os.path.basename(output_file_path)
                    result.output_image_path = f"/api/v1/boundingbox/output-images/{filename}"
                else:
                    result.output_image_path = output_file_path
                result.bounding_boxes = json.dumps(json_output) if json_output else None

                # Enhanced logging for segmentation masks
                if masks:
                    current_app.logger.info(f"Saving {len(masks)} segmentation masks for result {result_id}")
                    for i, mask in enumerate(masks):
                        current_app.logger.info(f"  Mask {i+1}: label='{mask.get('label', 'unknown')}', data_length={len(str(mask.get('mask_base64', '')))}")
                    result.segmentation_masks = json.dumps(masks)
                else:
                    current_app.logger.info(f"No segmentation masks to save for result {result_id}")
                    result.segmentation_masks = None

                current_app.logger.info(f"AI processing completed successfully for result {result_id}")
            else:
                result.status = 'failed'
                # Check if json_output contains structured error information
                if isinstance(json_output, dict) and "error" in json_output:
                    error_msg = json_output.get("error", "AI processing failed")
                    error_details = json_output.get("details", "")
                    if error_details:
                        result.error_message = f"{error_msg}: {error_details}"
                    else:
                        result.error_message = error_msg
                    current_app.logger.error(f"AI processing failed for result {result_id}: {error_msg} - {error_details}")
                else:
                    result.error_message = 'AI processing failed to generate output'
                    current_app.logger.error(f"AI processing failed for result {result_id}: No output generated")

            result.updated_at = datetime.datetime.utcnow()
            db.session.commit()

    except Exception as e:
        # Update result status to failed
        result = BoundingBoxResults.query.get(result_id)
        if result:
            result.status = 'failed'
            result.error_message = str(e)
            result.updated_at = datetime.datetime.utcnow()
            db.session.commit()

        current_app.logger.error(f"Error processing result {result_id}: {e}")


def run_async_processing(app, result_id, image_data, prompt, model_name, resize_width, resize_height, output_type):
    """
    Wrapper function to run async processing in a thread.
    """
    with app.app_context():
        try:
            # Create a new event loop for this thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            # Run the async function
            loop.run_until_complete(process_experiment_result(
                result_id, image_data, prompt, model_name, resize_width, resize_height, output_type
            ))
        except Exception as e:
            current_app.logger.error(f"Error in async processing thread for result {result_id}: {e}")
        finally:
            loop.close()


@bounding_box_bp.route('/pictures', methods=['GET'])
def get_pictures():
    """
    Get all bounding box pictures with pagination and search.
    """
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 5, type=int)
        search = request.args.get('search', '', type=str)

        query = BoundingBoxPictures.query
        if search:
            query = query.filter(BoundingBoxPictures.filename.ilike(f'%{search}%'))

        paginated_pictures = query.order_by(BoundingBoxPictures.created_at.desc())\
            .paginate(page=page, per_page=per_page, error_out=False)

        pictures_data = []
        for pic in paginated_pictures.items:
            pic_dict = pic.to_dict()
            # Use filename as name for frontend compatibility
            pic_dict['name'] = pic_dict.get('filename', 'Unknown')
            pictures_data.append(pic_dict)

        return jsonify({
            "pictures": pictures_data,
            "totalPages": paginated_pictures.pages,
            "totalPictures": paginated_pictures.total,
            "currentPage": paginated_pictures.page
        }), 200

    except Exception as e:
        current_app.logger.error(f"Error fetching pictures: {e}")
        return jsonify({"error": "Failed to fetch pictures"}), 500

@bounding_box_bp.route('/pictures', methods=['POST'])
def upload_picture():
    """
    Upload a new bounding box picture.
    """
    try:
        if 'picture' not in request.files:
            return jsonify({"error": "No picture file provided"}), 400

        file = request.files['picture']
        if file.filename == '':
            return jsonify({"error": "No file selected"}), 400

        # Ensure input directory exists
        input_dir = get_input_images_path()
        os.makedirs(input_dir, exist_ok=True)

        # Save file to input directory
        file_path = os.path.join(input_dir, file.filename)
        file.save(file_path)

        # Convert to base64 for storage in database
        import base64
        with open(file_path, 'rb') as img_file:
            image_data = base64.b64encode(img_file.read()).decode('utf-8')
            image_data = f"data:image/{file.filename.split('.')[-1].lower()};base64,{image_data}"

        new_picture = BoundingBoxPictures(
            filename=file.filename,
            image_data=image_data
        )

        db.session.add(new_picture)
        db.session.commit()

        pic_dict = new_picture.to_dict()
        pic_dict['name'] = pic_dict.get('filename', 'Unknown')

        return jsonify(pic_dict), 201

    except Exception as e:
        current_app.logger.error(f"Error uploading picture: {e}")
        db.session.rollback()
        return jsonify({"error": "Failed to upload picture"}), 500

@bounding_box_bp.route('/pictures/<int:picture_id>', methods=['PUT'])
def update_picture(picture_id):
    """
    Update a bounding box picture (e.g., rename).
    """
    try:
        data = request.json
        if not data:
            return jsonify({"error": "No data provided"}), 400

        picture = BoundingBoxPictures.query.get(picture_id)
        if not picture:
            return jsonify({"error": "Picture not found"}), 404

        if 'name' in data:
            picture.filename = data['name']

        db.session.commit()

        pic_dict = picture.to_dict()
        pic_dict['name'] = pic_dict.get('filename', 'Unknown')

        return jsonify(pic_dict), 200

    except Exception as e:
        current_app.logger.error(f"Error updating picture {picture_id}: {e}")
        db.session.rollback()
        return jsonify({"error": "Failed to update picture"}), 500

@bounding_box_bp.route('/pictures/<int:picture_id>', methods=['DELETE'])
def delete_picture(picture_id):
    """
    Delete a bounding box picture.
    """
    try:
        picture = BoundingBoxPictures.query.get(picture_id)
        if not picture:
            return jsonify({"error": "Picture not found"}), 404

        # Also delete associated experiments and results
        experiments = BoundingBoxExperiments.query.filter_by(picture_id=picture_id).all()
        for exp in experiments:
            # Delete results first
            BoundingBoxResults.query.filter_by(experiment_id=exp.id).delete()
            # Delete experiment
            db.session.delete(exp)

        # Delete the picture
        db.session.delete(picture)
        db.session.commit()

        return jsonify({"message": f"Picture {picture_id} deleted successfully"}), 200

    except Exception as e:
        current_app.logger.error(f"Error deleting picture {picture_id}: {e}")
        db.session.rollback()
        return jsonify({"error": "Failed to delete picture"}), 500

@bounding_box_bp.route('/models', methods=['GET'])
def get_models():
    """
    Get all bounding box models.
    """
    try:
        models = BoundingBoxModels.query.all()
        return jsonify([model.to_dict() for model in models]), 200
    except Exception as e:
        current_app.logger.error(f"Error fetching models: {e}")
        return jsonify({"error": "Failed to fetch models"}), 500

@bounding_box_bp.route('/models', methods=['POST'])
def create_model():
    """
    Create a new bounding box model.
    Expects `name` (string) and optionally `description` (string) and `is_active` (boolean).
    """
    data = request.json
    if not data or 'name' not in data:
        return jsonify({"error": "Missing required field: 'name'"}), 400

    # Check if model with same name already exists
    existing_model = BoundingBoxModels.query.filter_by(name=data['name']).first()
    if existing_model:
        return jsonify({"error": f"Model with name '{data['name']}' already exists"}), 400

    try:
        new_model = BoundingBoxModels(
            name=data['name'],
            description=data.get('description', ''),
            is_active=data.get('is_active', True)
        )
        db.session.add(new_model)
        db.session.flush()  # Get the ID for the new model

        # Auto-add this model to all existing experiments
        if new_model.is_active:
            existing_experiments = BoundingBoxExperiments.query.all()
            results_created = 0

            for experiment in existing_experiments:
                # Check if this model already has a result for this experiment
                existing_result = BoundingBoxResults.query.filter_by(
                    experiment_id=experiment.id,
                    model_id=new_model.id
                ).first()

                if not existing_result:
                    # Create a new result for this experiment with the new model
                    new_result = BoundingBoxResults(
                        experiment_id=experiment.id,
                        model_id=new_model.id,
                        status='pending'
                    )
                    db.session.add(new_result)
                    db.session.flush()  # Get the ID for the new result
                    results_created += 1

                    # Start AI processing for this result if AI is available
                    if AI_AVAILABLE:
                        picture = BoundingBoxPictures.query.get(experiment.picture_id)
                        if picture:
                            current_app.logger.info(f"Starting AI processing for new model {new_model.name} on experiment {experiment.id}")
                            thread = threading.Thread(
                                target=run_async_processing,
                                args=(
                                    current_app._get_current_object(),
                                    new_result.id,
                                    picture.image_data,
                                    experiment.prompt,
                                    new_model.name,
                                    experiment.resize_width,
                                    experiment.resize_height,
                                    experiment.output_type
                                )
                            )
                            thread.daemon = True
                            thread.start()

            current_app.logger.info(f"Created {results_created} new results for existing experiments with new model {new_model.name}")

        db.session.commit()
        current_app.logger.info(f"Created new bounding box model: {new_model.name} (ID: {new_model.id})")
        return jsonify(new_model.to_dict()), 201
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error creating model: {e}")
        return jsonify({"error": f"Failed to create model: {str(e)}"}), 500

@bounding_box_bp.route('/models/<int:model_id>', methods=['PUT'])
def update_model(model_id):
    """
    Update a specific bounding box model.
    Expects `is_active` (boolean) and optionally `name` (string) and `description` (string).
    """
    data = request.json
    if not data:
        return jsonify({"error": "No data provided"}), 400

    model = BoundingBoxModels.query.get(model_id)
    if not model:
        return jsonify({"error": "Model not found"}), 404

    try:
        if 'name' in data:
            # Check if another model with same name exists
            existing_model = BoundingBoxModels.query.filter(
                BoundingBoxModels.name == data['name'],
                BoundingBoxModels.id != model_id
            ).first()
            if existing_model:
                return jsonify({"error": f"Model with name '{data['name']}' already exists"}), 400
            model.name = data['name']
        if 'description' in data:
            model.description = data['description']
        if 'is_active' in data:
            if not isinstance(data['is_active'], bool):
                return jsonify({"error": "'is_active' must be a boolean"}), 400
            model.is_active = data['is_active']

        model.updated_at = datetime.datetime.utcnow()
        db.session.commit()
        return jsonify(model.to_dict()), 200
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error updating model {model_id}: {e}")
        return jsonify({"error": f"Failed to update model: {str(e)}"}), 500

@bounding_box_bp.route('/models/<int:model_id>', methods=['DELETE'])
def delete_model(model_id):
    """
    Delete a specific bounding box model and all associated results.
    """
    model = BoundingBoxModels.query.get(model_id)
    if not model:
        return jsonify({"error": "Model not found"}), 404

    try:
        # Delete all results associated with this model
        results_count = BoundingBoxResults.query.filter_by(model_id=model_id).count()
        BoundingBoxResults.query.filter_by(model_id=model_id).delete()

        # Delete the model
        model_name = model.name
        db.session.delete(model)
        db.session.commit()

        current_app.logger.info(f"Deleted bounding box model: {model_name} (ID: {model_id}) and {results_count} associated results")
        return jsonify({"message": f"Model '{model_name}' and {results_count} associated results deleted successfully"}), 200
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error deleting model {model_id}: {e}")
        return jsonify({"error": f"Failed to delete model: {str(e)}"}), 500


@bounding_box_bp.route('/experiments', methods=['GET'])
def get_experiments():
    """
    Get experiments for a required picture_id, paginated.
    Includes associated results and model names.
    """
    picture_id = request.args.get('picture_id', type=int)
    if not picture_id:
        return jsonify({"error": "picture_id query parameter is required"}), 400

    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 5, type=int)

    try:
        picture = BoundingBoxPictures.query.get(picture_id)
        if not picture:
            return jsonify({"error": "Picture not found"}), 404

        paginated_experiments = BoundingBoxExperiments.query.filter_by(picture_id=picture_id)\
            .order_by(BoundingBoxExperiments.created_at.desc())\
            .paginate(page=page, per_page=per_page, error_out=False)

        experiments_data = []
        for exp in paginated_experiments.items:
            exp_dict = exp.to_dict()
            exp_dict['results'] = []
            for result in exp.results: # Assumes exp.results is the relationship
                model = BoundingBoxModels.query.get(result.model_id) # Fetch model for name
                result_dict = result.to_dict()
                result_dict['model_name'] = model.name if model else "Unknown Model"
                exp_dict['results'].append(result_dict)
            experiments_data.append(exp_dict)

        return jsonify({
            "experiments": experiments_data,
            "total": paginated_experiments.total,
            "pages": paginated_experiments.pages,
            "current_page": paginated_experiments.page,
            "per_page": paginated_experiments.per_page
        }), 200

    except Exception as e:
        current_app.logger.error(f"Error fetching experiments for picture_id {picture_id}: {e}")
        return jsonify({"error": "Failed to fetch experiments"}), 500


@bounding_box_bp.route('/experiments/configurations', methods=['GET'])
def get_experiment_configurations():
    """
    Get all distinct experiment configurations.
    """
    try:
        # Query for distinct combinations of prompt, resize_height, resize_width, output_type
        distinct_configs = db.session.query(
            BoundingBoxExperiments.prompt,
            BoundingBoxExperiments.resize_height,
            BoundingBoxExperiments.resize_width,
            BoundingBoxExperiments.output_type
        ).distinct().all()

        configurations = [
            {
                "prompt": config.prompt,
                "resize_height": config.resize_height,
                "resize_width": config.resize_width,
                "output_type": config.output_type
            } for config in distinct_configs
        ]
        return jsonify(configurations), 200
    except Exception as e:
        current_app.logger.error(f"Error fetching experiment configurations: {e}")
        return jsonify({"error": "Failed to fetch experiment configurations"}), 500

@bounding_box_bp.route('/experiments/all', methods=['GET'])
def get_all_experiments():
    """
    Get all experiments with results for ranking purposes.
    """
    try:
        experiments = BoundingBoxExperiments.query.order_by(BoundingBoxExperiments.created_at.desc()).all()

        experiments_data = []
        for exp in experiments:
            exp_dict = exp.to_dict()
            exp_dict['results'] = []
            for result in exp.results:
                model = BoundingBoxModels.query.get(result.model_id)
                result_dict = result.to_dict()
                result_dict['model_name'] = model.name if model else "Unknown Model"
                exp_dict['results'].append(result_dict)
            experiments_data.append(exp_dict)

        return jsonify(experiments_data), 200

    except Exception as e:
        current_app.logger.error(f"Error fetching all experiments: {e}")
        return jsonify({"error": "Failed to fetch all experiments"}), 500

@bounding_box_bp.route('/experiments', methods=['POST'])
def create_experiment():
    """
    Create a new experiment and trigger async processing for active models.
    """
    data = request.json
    required_fields = ['picture_id', 'prompt', 'resize_height', 'resize_width', 'output_type']
    if not data or not all(field in data for field in required_fields):
        return jsonify({"error": "Missing required fields", "required": required_fields}), 400

    try:
        picture = db.session.get(BoundingBoxPictures, data['picture_id']) # Use .get for PK lookup
        if not picture:
            return jsonify({"error": f"Picture with id {data['picture_id']} not found."}), 404

        new_experiment = BoundingBoxExperiments(
            picture_id=data['picture_id'],
            prompt=data['prompt'],
            resize_height=data['resize_height'],
            resize_width=data['resize_width'],
            output_type=data['output_type']
        )

        active_models = BoundingBoxModels.query.filter_by(is_active=True).all()
        if not active_models:
            return jsonify({"error": "No active models found to run the experiment against."}), 400

        # Create result entries and commit them
        initial_results_for_response = []

        db.session.add(new_experiment) # Add experiment
        db.session.flush() # Flush to get ID for new_experiment

        for model in active_models:
            new_result = BoundingBoxResults(
                experiment_id=new_experiment.id,
                model_id=model.id,
                status='pending' # Set to pending since AI processing is not implemented yet
            )
            db.session.add(new_result)
            db.session.flush() # Flush to get ID for new_result

            result_dict = new_result.to_dict()
            result_dict['model_name'] = model.name
            initial_results_for_response.append(result_dict)

        db.session.commit() # Commit experiment and all processing results

        # Start AI processing for each model
        if AI_AVAILABLE:
            current_app.logger.info(f"Starting AI processing for experiment {new_experiment.id} with {len(active_models)} models.")
            # Process each model asynchronously
            for model in active_models:
                # Find the corresponding result
                result = next((r for r in new_experiment.results if r.model_id == model.id), None)
                if result:
                    # Start background processing in a thread
                    thread = threading.Thread(
                        target=run_async_processing,
                        args=(
                            current_app._get_current_object(),  # Pass the app instance
                            result.id,
                            picture.image_data,
                            new_experiment.prompt,
                            model.name,
                            new_experiment.resize_width,
                            new_experiment.resize_height,
                            new_experiment.output_type
                        )
                    )
                    thread.daemon = True  # Thread will die when main program exits
                    thread.start()
        else:
            current_app.logger.warning(f"AI processing not available. Experiment {new_experiment.id} results will remain pending.")
            current_app.logger.info("To enable AI processing, ensure the Google Generative AI library is properly installed and configured.")

        current_app.logger.info(f"Experiment {new_experiment.id} created with {len(active_models)} results.")

        experiment_dict = new_experiment.to_dict()
        experiment_dict['results'] = initial_results_for_response # Return initial state (all 'processing')
        # The actual results will be updated in DB by the async functions. Client might need to poll or use WebSocket.

        return jsonify(experiment_dict), 201

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Critical error in create_experiment: {e}", exc_info=True)
        return jsonify({"error": f"Failed to create experiment: {str(e)}"}), 500


@bounding_box_bp.route('/experiments/<int:experiment_id>', methods=['DELETE'])
def delete_experiment(experiment_id):
    """
    Delete a specific experiment and all associated results, including generated image files.
    """
    experiment = BoundingBoxExperiments.query.get(experiment_id)
    if not experiment:
        return jsonify({"error": "Experiment not found"}), 404

    try:
        # Get all results associated with this experiment to delete their image files
        results = BoundingBoxResults.query.filter_by(experiment_id=experiment_id).all()
        deleted_files = []

        # Delete generated image files from local directory
        for result in results:
            if result.output_image_path:
                try:
                    # Extract filename from the API URL path
                    if result.output_image_path.startswith('/api/v1/boundingbox/output-images/'):
                        filename = result.output_image_path.split('/')[-1]

                        # Get the output directory path
                        if os.name == 'nt':  # Windows
                            base_dir = os.environ.get('WIN_MAIN_DIR', os.getcwd())
                        else:  # Linux/Unix
                            base_dir = os.environ.get('LINUX_MAIN_DIR', os.getcwd())

                        output_dir = os.path.join(base_dir, 'BoundingBox', 'Output')
                        file_path = os.path.join(output_dir, filename)

                        # Delete the file if it exists
                        if os.path.exists(file_path):
                            os.remove(file_path)
                            deleted_files.append(filename)
                            current_app.logger.info(f"Deleted image file: {file_path}")
                        else:
                            current_app.logger.warning(f"Image file not found: {file_path}")

                except Exception as file_error:
                    current_app.logger.error(f"Error deleting image file for result {result.id}: {file_error}")
                    # Continue with database deletion even if file deletion fails

        # Delete all results associated with this experiment
        results_count = len(results)
        BoundingBoxResults.query.filter_by(experiment_id=experiment_id).delete()

        # Delete the experiment
        experiment_info = f"Experiment {experiment_id} (Picture ID: {experiment.picture_id}, Prompt: '{experiment.prompt[:50]}...')"
        db.session.delete(experiment)
        db.session.commit()

        current_app.logger.info(f"Deleted {experiment_info}, {results_count} associated results, and {len(deleted_files)} image files")
        return jsonify({
            "message": f"Experiment {experiment_id} and {results_count} associated results deleted successfully",
            "deleted_files": len(deleted_files)
        }), 200
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error deleting experiment {experiment_id}: {e}")
        return jsonify({"error": f"Failed to delete experiment: {str(e)}"}), 500


@bounding_box_bp.route('/results/<int:result_id>', methods=['PUT'])
def update_result(result_id):
    """
    Update the score for a specific result.
    Expects `score` (integer 0-10).
    """
    data = request.json
    if not data or 'score' not in data:
        return jsonify({"error": "'score' is required in the request body"}), 400

    score = data['score']
    if not isinstance(score, int) or not (0 <= score <= 10):
        return jsonify({"error": "'score' must be an integer between 0 and 10"}), 400

    result = BoundingBoxResults.query.get(result_id)
    if not result:
        return jsonify({"error": "Result not found"}), 404

    try:
        result.score = score
        result.updated_at = datetime.datetime.utcnow()
        db.session.commit()
        return jsonify(result.to_dict()), 200
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error updating result {result_id}: {e}")
        return jsonify({"error": f"Failed to update result: {str(e)}"}), 500

def get_bounding_box_storage_path():
    """Get the base directory for bounding box image storage."""
    if os.name == 'nt':  # Windows
        base_dir = os.environ.get("WIN_MAIN_DIR")
    else:  # Linux/Unix
        base_dir = os.environ.get("LINUX_MAIN_DIR")

    if not base_dir:
        # Fallback or error if not set, depends on deployment strategy
        current_app.logger.warning("Main directory environment variable (WIN_MAIN_DIR or LINUX_MAIN_DIR) not set.")
        return os.path.join(os.getcwd(), "BoundingBox") # Example fallback
    return os.path.join(base_dir, "BoundingBox")

def get_input_images_path():
    """Get the directory for input (product) images."""
    return os.path.join(get_bounding_box_storage_path(), "Input")

def get_output_images_path():
    """Get the directory for output (model-generated) images."""
    return os.path.join(get_bounding_box_storage_path(), "Output")


@bounding_box_bp.route('/pictures/scan-local', methods=['POST'])
def scan_local_images():
    """
    Scan the local Input directory for images and import them into the database.
    """
    try:
        input_dir = get_input_images_path()

        if not os.path.exists(input_dir):
            os.makedirs(input_dir, exist_ok=True)
            return jsonify({"message": "Input directory created but no images found", "imported": 0}), 200

        # Supported image extensions
        supported_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp'}

        imported_count = 0
        skipped_count = 0
        errors = []

        for filename in os.listdir(input_dir):
            file_path = os.path.join(input_dir, filename)

            # Skip directories and non-image files
            if not os.path.isfile(file_path):
                continue

            file_ext = os.path.splitext(filename)[1].lower()
            if file_ext not in supported_extensions:
                continue

            # Check if image already exists in database
            existing_picture = BoundingBoxPictures.query.filter_by(filename=filename).first()
            if existing_picture:
                skipped_count += 1
                current_app.logger.warning(f"Skipping existing filename in database: {filename}")
                continue

            try:
                # Read and convert image to base64
                import base64
                with open(file_path, 'rb') as img_file:
                    image_data = base64.b64encode(img_file.read()).decode('utf-8')
                    # Determine MIME type based on extension
                    mime_type = {
                        '.jpg': 'jpeg', '.jpeg': 'jpeg', '.png': 'png',
                        '.gif': 'gif', '.bmp': 'bmp', '.tiff': 'tiff', '.webp': 'webp'
                    }.get(file_ext, 'jpeg')
                    image_data = f"data:image/{mime_type};base64,{image_data}"

                # Create new picture record
                new_picture = BoundingBoxPictures(
                    filename=filename,
                    image_data=image_data
                )
                db.session.add(new_picture)
                imported_count += 1

            except Exception as e:
                errors.append(f"Failed to import {filename}: {str(e)}")
                current_app.logger.error(f"Error importing image {filename}: {e}")

        if imported_count > 0:
            db.session.commit()

        return jsonify({
            "message": f"Scan completed. Imported {imported_count} images, skipped {skipped_count} existing images.",
            "imported": imported_count,
            "skipped": skipped_count,
            "errors": errors
        }), 200

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error scanning local images: {e}")
        return jsonify({"error": f"Failed to scan local images: {str(e)}"}), 500


@bounding_box_bp.route('/output-images/<path:filename>', methods=['GET'])
def serve_output_image(filename):
    """
    Serve output images from the BoundingBox/Output directory.
    """
    try:
        # Get the output directory path
        if os.name == 'nt':  # Windows
            base_dir = os.environ.get('WIN_MAIN_DIR', os.getcwd())
        else:  # Linux/Unix
            base_dir = os.environ.get('LINUX_MAIN_DIR', os.getcwd())

        output_dir = os.path.join(base_dir, 'BoundingBox', 'Output')
        file_path = os.path.join(output_dir, filename)

        # Security check: ensure the file is within the output directory
        if not os.path.abspath(file_path).startswith(os.path.abspath(output_dir)):
            return jsonify({"error": "Invalid file path"}), 400

        # Check if file exists
        if not os.path.exists(file_path):
            current_app.logger.warning(f"Output image not found: {file_path}")
            return jsonify({"error": "Image not found"}), 404

        return send_file(file_path, as_attachment=False)

    except Exception as e:
        current_app.logger.error(f"Error serving output image {filename}: {e}")
        return jsonify({"error": "Failed to serve image"}), 500


