import React, { useState, useRef, useEffect } from 'react';
import React<PERSON>rop from 'react-image-crop';
import 'react-image-crop/dist/ReactCrop.css';
import './MultiCropImage.css';

// MOCK API Service for demonstration. Replace with your actual import.
import { getPlaintiffRegistrations } from './../../services/api_copyright_viz';

// Utility function to create a cropped image blob
function getCroppedImg(image, crop, fileName) {
    const canvas = document.createElement('canvas');
    canvas.width = crop.width;
    canvas.height = crop.height;
    const ctx = canvas.getContext('2d');

    ctx.drawImage(
        image, crop.x, crop.y, crop.width, crop.height,
        0, 0, crop.width, crop.height
    );

    return new Promise((resolve, reject) => {
        canvas.toBlob(blob => {
            if (!blob) {
                reject(new Error('Canvas is empty'));
                return;
            }
            blob.name = fileName;
            resolve(blob);
        }, 'image/png');
    });
}


const MultiCropImage = ({ proxiedImageUrl, onCropChange, plaintiffId }) => {
    const [currentCrop, setCurrentCrop] = useState(null);
    const [cropPreviews, setCropPreviews] = useState([]);
    const [registrations, setRegistrations] = useState([]);
    const [isLoadingRegs, setIsLoadingRegs] = useState(false);
    const [fetchError, setFetchError] = useState(null);
    const imgRef = useRef(null);

    useEffect(() => {
        const fetchRegistrations = async () => {
            if (!plaintiffId) return;

            setIsLoadingRegs(true);
            setFetchError(null);
            try {
                const result = await getPlaintiffRegistrations(plaintiffId);

                if (result && Array.isArray(result.data)) {
                    const dataToSort = [...result.data];

                    dataToSort.sort((a, b) => {
                        const regNoA = a.reg_no || '';
                        const regNoB = b.reg_no || '';

                        const matchA = regNoA.match(/^([a-zA-Z]+)(.*)/);
                        const matchB = regNoB.match(/^([a-zA-Z]+)(.*)/);

                        if (!matchA) return 1;  // Move malformed entries to the end
                        if (!matchB) return -1;

                        const prefixA = matchA[1];
                        const remainderA = matchA[2];
                        const prefixB = matchB[1];
                        const remainderB = matchB[2];

                        const aIsMD = prefixA === 'MD';
                        const bIsMD = prefixB === 'MD';

                        if (aIsMD && !bIsMD) {
                        }
                        if (!aIsMD && bIsMD) {
                            return -1; // 'b' is MD, 'a' is not -> 'a' comes before 'b'
                        }

                        if (prefixA !== prefixB) {
                            return prefixA.localeCompare(prefixB);
                        }
                        const numA = parseInt(remainderA.replace(/\D/g, ''), 10);
                        const numB = parseInt(remainderB.replace(/\D/g, ''), 10);

                        if (isNaN(numA) || isNaN(numB)) {
                            return remainderA.localeCompare(remainderB); // Fallback for non-numeric parts
                        }

                        return numA - numB;
                    });

                    setRegistrations(dataToSort);
                } else {
                    console.error("API did not return an array:", result);
                    setFetchError("Failed to load registrations due to unexpected format.");
                    setRegistrations([]);
                }
            } catch (error) {
                console.error("Failed to fetch plaintiff registrations:", error);
                setFetchError("Could not load registrations. Please try again.");
            } finally {
                setIsLoadingRegs(false);
            }
        };

        fetchRegistrations();
    }, [plaintiffId]);

    useEffect(() => {
        return () => {
            cropPreviews.forEach(preview => URL.revokeObjectURL(preview.previewUrl));
        };
    }, [cropPreviews]);

    // ... (handleCropComplete, removeCrop, and handleRegistrationChange logic remains the same) ...
    const handleCropComplete = async (crop) => {
        if (crop.width && crop.height && imgRef.current) {
            const image = imgRef.current;
            const { naturalWidth, naturalHeight } = image;
            const { width: displayedWidth, height: displayedHeight } = image.getBoundingClientRect();
            const scaleX = naturalWidth / displayedWidth;
            const scaleY = naturalHeight / displayedHeight;
            const scaledCrop = {
                x: Math.round(crop.x * scaleX),
                y: Math.round(crop.y * scaleY),
                width: Math.round(crop.width * scaleX),
                height: Math.round(crop.height * scaleY),
            };
            try {
                const croppedImageBlob = await getCroppedImg(image, scaledCrop, `crop-${cropPreviews.length + 1}.png`);
                const previewUrl = URL.createObjectURL(croppedImageBlob);
                const newPreviews = [...cropPreviews, {
                    blob: croppedImageBlob,
                    previewUrl,
                    // Initialize with separate fields for clarity
                    registrationIdentifier: '',
                    action: ''
                }];
                setCropPreviews(newPreviews);
                onCropChange(newPreviews);
            } catch (e) {
                console.error("Error creating cropped image:", e);
            }
            setCurrentCrop(null);
        }
    };
    const removeCrop = (indexToRemove) => {
        URL.revokeObjectURL(cropPreviews[indexToRemove].previewUrl);
        const newPreviews = cropPreviews.filter((_, index) => index !== indexToRemove);
        setCropPreviews(newPreviews);
        onCropChange(newPreviews);
    };

    const handleRegistrationChange = (event, indexToUpdate) => {
        const newSelectedId = event.target.value;
        const newPreviews = cropPreviews.map((preview, index) => {
            if (index === indexToUpdate) {
                // When dropdown is used, set the identifier and clear any action
                return {
                    ...preview,
                    registrationIdentifier: newSelectedId,
                    action: '' // Clear action when a registration is selected
                };
            }
            return preview;
        });
        setCropPreviews(newPreviews);
        console.log("Updated crop previews:", newPreviews);
        onCropChange(newPreviews);
    };

    // ==================================================================
    // NEW FEATURE: Handler for the new toggles (MODIFIED)
    // ==================================================================
    const handleToggleChange = (event, indexToUpdate, actionValue) => {
        const isChecked = event.target.checked;
        const newPreviews = cropPreviews.map((preview, index) => {
            if (index === indexToUpdate) {
                return {
                    ...preview,
                    // If a toggle is checked, set its action value and clear the registration.
                    // If it's unchecked, clear the action.
                    action: isChecked ? actionValue : '',
                    registrationIdentifier: '', // Clear registration when an action is set
                };
            }
            return preview;
        });
        console.log("Updated crop previews with toggle:", newPreviews);
        setCropPreviews(newPreviews);
        onCropChange(newPreviews);
    };

    return (
        <div>
            <div style={{ position: 'relative', maxWidth: '800px', margin: '0 auto' }}>
                <ReactCrop
                    crop={currentCrop}
                    onChange={c => setCurrentCrop(c)}
                    onComplete={handleCropComplete}
                >
                    <img
                        ref={imgRef}
                        src={proxiedImageUrl}
                        alt="Crop source"
                        style={{ maxWidth: '100%', maxHeight: '100%', display: 'block' }}
                    />
                </ReactCrop>
            </div>

            <div className="crop-previews-container" style={{ marginTop: '20px' }}>
                <h4>Cropped Snippets</h4>
                {fetchError && <p className="error-message">{fetchError}</p>}
                {cropPreviews.length === 0 && <p>No crops selected yet.</p>}

                <div style={{ display: 'flex', flexDirection: 'column', gap: '15px' }}>
                    {cropPreviews.map((item, index) => (
                        <div key={index} className="crop-preview-item">
                            <img
                                src={item.previewUrl}
                                alt={`Crop preview ${index + 1}`}
                                className="crop-preview-image"
                            />
                            <div className="crop-details">
                                <label htmlFor={`registration-select-${index}`}>Link to Registration:</label>
                                <div className="custom-select-wrapper">
                                    <select
                                        id={`registration-select-${index}`}
                                        // The value is now directly from registrationIdentifier
                                        value={item.registrationIdentifier}
                                        onChange={(e) => handleRegistrationChange(e, index)}
                                        // Disable dropdown if an action (GENERATE/REPLACE) is active
                                        disabled={isLoadingRegs || !!item.action}
                                    >
                                        <option value="" disabled>
                                            {isLoadingRegs ? 'Loading...' : 'Select a registration'}
                                        </option>
                                        {registrations.map(reg => {
                                            const displayText = reg.title ? `${reg.reg_no} - ${reg.title}` : reg.reg_no;
                                            return (
                                                <option key={reg.reg_no} value={reg.reg_no}>
                                                    {displayText}
                                                </option>
                                            );
                                        })}
                                    </select>
                                </div>
                                {/* ================================================================== */}
                                {/* NEW FEATURE: Toggles now check against the 'action' field        */}
                                {/* ================================================================== */}
                                <div className="toggle-options">
                                    <label>
                                        <input
                                            type="checkbox"
                                            checked={item.action === 'GENERATE'}
                                            onChange={(e) => handleToggleChange(e, index, 'GENERATE')}
                                        />
                                        Generate New Reg No
                                    </label>
                                    <label>
                                        <input
                                            type="checkbox"
                                            checked={item.action === 'REPLACE'}
                                            onChange={(e) => handleToggleChange(e, index, 'REPLACE')}
                                        />
                                        Replace Original
                                    </label>
                                </div>
                            </div>

                            <button
                                onClick={() => removeCrop(index)}
                                className="remove-crop-button"
                            >
                                &times;
                            </button>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
};

export default MultiCropImage;

