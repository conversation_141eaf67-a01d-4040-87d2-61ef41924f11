from sqlalchemy import Column, Integer, String, Float, DateTime, ForeignKey, Text
from sqlalchemy.orm import relationship
import datetime
from backend.extensions import db # Import db instance

class BoundingBoxModels(db.Model):
    __tablename__ = 'bounding_box_models'
    __table_args__ = {'extend_existing': True}
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, unique=True, index=True, nullable=False)
    description = Column(Text, nullable=True)
    is_active = Column(db.<PERSON>olean, default=True, nullable=False)
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)

    # Note: No direct relationship to experiments - relationship is through results table

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class BoundingBoxPictures(db.Model):
    __tablename__ = 'bounding_box_pictures'
    __table_args__ = {'extend_existing': True}
    id = Column(Integer, primary_key=True, index=True)
    filename = Column(String, unique=True, index=True, nullable=False)
    # image_data is large, consider if it's always needed or if a path/URL is better
    image_data = Column(Text, nullable=True) # Base64 encoded image - make nullable if path is primary
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)

    experiments = relationship("BoundingBoxExperiments", back_populates="picture")

    def to_dict(self):
        return {
            'id': self.id,
            'filename': self.filename,
            'image_data': self.image_data,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class BoundingBoxExperiments(db.Model):
    __tablename__ = 'bounding_box_experiments'
    __table_args__ = {'extend_existing': True}
    id = Column(Integer, primary_key=True, index=True)
    picture_id = Column(Integer, ForeignKey('bounding_box_pictures.id'), nullable=False)
    prompt = Column(Text, nullable=False)
    resize_height = Column(Integer, nullable=False)
    resize_width = Column(Integer, nullable=False)
    output_type = Column(String, nullable=False) # e.g. 'grayscale', 'color_corrected'
    # Removed 'configuration' as individual fields are now present
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)

    # Note: model_id removed - one experiment can run against multiple models
    # The relationship is through the results table
    picture = relationship("BoundingBoxPictures", back_populates="experiments")
    results = relationship("BoundingBoxResults", back_populates="experiment") # Multiple results per experiment (one per model)

    def to_dict(self):
        return {
            'id': self.id,
            'picture_id': self.picture_id,
            'prompt': self.prompt,
            'resize_height': self.resize_height,
            'resize_width': self.resize_width,
            'output_type': self.output_type,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class BoundingBoxResults(db.Model):
    __tablename__ = 'bounding_box_results'
    __table_args__ = {'extend_existing': True}
    id = Column(Integer, primary_key=True, index=True)
    experiment_id = Column(Integer, ForeignKey('bounding_box_experiments.id'), nullable=False)
    model_id = Column(Integer, ForeignKey('bounding_box_models.id'), nullable=False)
    status = Column(String(50), default='pending', nullable=False) # pending, processing, success, failed
    output_image_path = Column(String(1024), nullable=True) # Path to the generated image
    score = Column(Integer, nullable=True) # Score from 0-10
    bounding_boxes = Column(Text, nullable=True) # JSON string for bounding boxes
    segmentation_masks = Column(Text, nullable=True) # JSON string for segmentation masks data
    inference_time = Column(Float, nullable=True)
    error_message = Column(Text, nullable=True) # To store error messages from task failures
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)

    experiment = relationship("BoundingBoxExperiments", back_populates="results")
    model = relationship("BoundingBoxModels")

    def to_dict(self):
        return {
            'id': self.id,
            'experiment_id': self.experiment_id,
            'model_id': self.model_id,
            'status': self.status,
            'output_image_path': self.output_image_path,
            'score': self.score,
            'bounding_boxes': self.bounding_boxes,
            'segmentation_masks': self.segmentation_masks,
            'inference_time': self.inference_time,
            'error_message': self.error_message,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
