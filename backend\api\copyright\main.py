"""
Main copyright API module that brings together all the sub-modules
"""
from flask import Blueprint, Response

from backend.api.copyright.orphan_processing import get_orphan_copyrights, fix_copyright_asset

# Import all the operation functions
from .core_operations import get_copyright_assets, update_copyrights_file
from .cn_websites import get_cn_websites_file_image
from .bulk_operations import bulk_update_copyrights_files
from .metadata import (
    get_copyright_types, get_copyright_methods, manage_copyright_types,
    refresh_data_cache, get_plaintiff_registrations
)
from .image_processing import proxy_image, split_copyright_file
from .change_regno import change_regno as change_regno_func
from .qdrant_duplicate_review import get_copyright_duplicates_grouped_with_plaintiff, resolve_copyright_duplicates
from flask import request, jsonify,current_app, g
from backend.extensions import db
from sqlalchemy import func, text
import uuid
# Create the blueprint
copyright_api_bp = Blueprint('copyright_api_bp', __name__)

# --- Core CRUD Operations ---

@copyright_api_bp.route('/api/v1/copyright', methods=['GET'])
def get_copyright_assets_route():
    return get_copyright_assets()

@copyright_api_bp.route('/api/v1/copyright/files/<file_id>', methods=['PATCH'])
def update_copyrights_file_route(file_id):
    return update_copyrights_file(file_id)

# --- Add Image ---

@copyright_api_bp.route('/api/v1/copyright/image/upload/stream', methods=['GET'])
def add_copyright_image_stream_route():
    """
    SSE endpoint for receiving progress updates during image addition.
    """
    session_id = request.args.get('session_id')
    if not session_id:
        return jsonify({"success": False, "error": "'session_id' query parameter is required."}), 400

    # Get current app context for the generator
    app = current_app._get_current_object()
    def generate_with_context():
        from backend.api.copyright.add_image import add_copyright_image_progress_generator
        with app.app_context():
            for item in add_copyright_image_progress_generator(session_id):
                yield item

    return Response(
        generate_with_context(),
        mimetype='text/event-stream',
        headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Cache-Control'
        }
    )

@copyright_api_bp.route('/api/v1/copyright/image/upload/stream', methods=['POST'])
def add_copyright_image_upload_route():
    """
    Endpoint for uploading images and starting the processing with progress tracking.
    """
    from backend.api.copyright.add_image import add_copyright_image_with_progress
    return add_copyright_image_with_progress()


# --- Bulk Operations ---
@copyright_api_bp.route('/api/v1/copyright/files/bulk', methods=['POST'])
def bulk_update_copyrights_files_route():
    return bulk_update_copyrights_files()

@copyright_api_bp.route('/api/v1/copyright/bulk/set_prod/stream', methods=['GET'])
def bulk_set_prod_stream_route():
    asset_ids_str = request.args.get('asset_ids')
    if not asset_ids_str:
        return jsonify({"success": False, "error": "'asset_ids' query parameter is required."}), 400

    asset_ids = asset_ids_str.split(',')
# Get current app context for the generator
    app = current_app._get_current_object()
    def generate_with_context():
        from backend.api.copyright.bulk_operations import bulk_set_prod_progress_generator
        with app.app_context():
            for item in bulk_set_prod_progress_generator(asset_ids, production_value=True):
                yield item

    return Response(
        generate_with_context(),
        mimetype='text/event-stream',
        headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Cache-Control'
        }
    )

@copyright_api_bp.route('/api/v1/copyright/bulk/unset_prod/stream', methods=['GET'])
def bulk_unset_prod_stream_route():
    asset_ids_str = request.args.get('asset_ids')
    if not asset_ids_str:
        return jsonify({"success": False, "error": "'asset_ids' query parameter is required."}), 400

    asset_ids = asset_ids_str.split(',')

    # Get current app context for the generator
    app = current_app._get_current_object()
    def generate_with_context():
        from backend.api.copyright.bulk_operations import bulk_set_prod_progress_generator
        with app.app_context():
            for item in bulk_set_prod_progress_generator(asset_ids, production_value=False):
                yield item

    return Response(
        generate_with_context(),
        mimetype='text/event-stream',
        headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Cache-Control'
        }
    )


# --- Image Processing ---

@copyright_api_bp.route('/api/v1/copyright/image-proxy')
def proxy_image_route():
    return proxy_image()

@copyright_api_bp.route('/api/v1/copyright/files/<file_id>/split', methods=['POST'])
def split_copyright_file_route(file_id):
    return split_copyright_file(file_id)


# --- Change reg No ---

@copyright_api_bp.route('/api/v1/copyright/files/<uuid:file_id>/change-regno', methods=['POST'])
def change_regno_route(file_id):
    """
    Changes the registration number for a copyright file.
    """
    return change_regno_func(file_id) 


# --- CN Websites Operations ---

@copyright_api_bp.route('/api/v1/copyright/cn-websites-files/<int:file_id>/image', methods=['GET'])
def get_cn_websites_file_image_route(file_id):
    return get_cn_websites_file_image(file_id)


@copyright_api_bp.route('/api/v1/copyright/cn-websites-files/move/stream', methods=['GET'])
def move_cn_websites_files_stream_route():
    asset_ids_str = request.args.get('asset_ids')
    if not asset_ids_str:
        return jsonify({"success": False, "error": "'asset_ids' query parameter is required."}), 400
    
    asset_ids = asset_ids_str.split(',')
    
    app = current_app._get_current_object()
    def generate_with_context():
        from backend.api.copyright.cn_websites import move_cn_websites_files_stream_generator
        with app.app_context():
            for item in move_cn_websites_files_stream_generator(asset_ids):
                yield item

    return Response(
        generate_with_context(),
        mimetype='text/event-stream',
        headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Cache-Control'
        }
    )

# --- Metadata and Cache ---

@copyright_api_bp.route('/api/v1/copyright/types', methods=['GET'])
def get_copyright_types_route():
    return get_copyright_types()

@copyright_api_bp.route('/api/v1/copyright/methods', methods=['GET'])
def get_copyright_methods_route():
    return get_copyright_methods()

@copyright_api_bp.route('/api/v1/copyright/types', methods=['POST'])
def manage_copyright_types_route():
    return manage_copyright_types()

@copyright_api_bp.route('/api/v1/copyright/cache/refresh', methods=['POST'])
def refresh_data_cache_route():
    return refresh_data_cache()

@copyright_api_bp.route('/api/v1/copyright/plaintiffs/<int:plaintiff_id>/registrations', methods=['GET'])
def get_plaintiff_registrations_route(plaintiff_id):
    return get_plaintiff_registrations(plaintiff_id)

# --- Production Flag ---
@copyright_api_bp.route('/api/v1/copyright/files/<uuid:file_id>/set-prod', methods=['PATCH'])
def set_copyright_production(file_id: uuid.UUID):
    """
    Sets the production status for a copyright file synchronously.
    """
    try:
        data = request.get_json()
        if 'production' not in data or not isinstance(data['production'], bool):
            return jsonify({
                "success": False,
                "error": "Invalid request body. 'production' (boolean) is required."
            }), 400

        production = data['production']

        # Validate that the record exists
        engine = db.get_engine(bind='maidalv_db')
        with engine.connect() as conn:
            result = conn.execute(text("SELECT id, registration_number, filename FROM copyrights_files WHERE id = :id"), {"id": file_id}).first()
            if not result:
                return jsonify({"success": False, "error": f"Copyright file with id '{file_id}' not found."}), 404

            file_id_db, reg_no, filename = result

        # Process synchronously
        from backend.api.copyright.handle_prod import process_copyright_production
        success, message, updated_asset = process_copyright_production(file_id_db, reg_no, production, filename)

        if success:
            return jsonify({"success": True, "data": updated_asset}), 200
        else:
            return jsonify({"success": False, "error": message}), 500

    except Exception as e:
        current_app.logger.error(f"Error setting production status for file_id={file_id}: {e}")
        return jsonify({"success": False, "error": "An unexpected error occurred."}), 500
   
   
    
@copyright_api_bp.route('/api/v1/copyright/duplicates', methods=['GET','POST'])
def get_copyright_duplicates_grouped_with_plaintiff_route():
    return get_copyright_duplicates_grouped_with_plaintiff()

@copyright_api_bp.route('/api/v1/copyright/duplicates/resolve', methods=['POST'])
def resolve_copyright_duplicates_route():
    return resolve_copyright_duplicates()

@copyright_api_bp.route('/api/v1/copyright/orphans/<category>', methods=['GET'])
def get_orphan_copyrights_route(category):
    # Get pagination parameters with defaults
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 50, type=int)

    # Validate pagination parameters
    if page < 1:
        page = 1
    if per_page < 1 or per_page > 200:
        per_page = 50

    return get_orphan_copyrights(category, page=page, per_page=per_page)

@copyright_api_bp.route('/api/v1/copyright/orphans/fix', methods=['POST'])
def fix_orphan_copyrights_route():
    """
    Fix orphan copyright assets based on category and action.
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"success": False, "error": "Request body is required"}), 400

        category = data.get('category')
        action = data.get('action')
        ids = data.get('ids', [])

        if not category or not action or not ids:
            return jsonify({
                "success": False,
                "error": "Missing required fields: category, action, and ids are required"
            }), 400

        return fix_copyright_asset(category, action, ids)

    except Exception as e:
        current_app.logger.error(f"Error in fix_orphan_copyrights_route: {e}")
        return jsonify({"success": False, "error": "An unexpected error occurred"}), 500
