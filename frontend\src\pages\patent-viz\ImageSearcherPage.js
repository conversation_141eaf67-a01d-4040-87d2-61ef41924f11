import React, { useState, useCallback } from 'react';
import {
  Con<PERSON>er,
  Typo<PERSON>,
  TextField,
  Button,
  CircularProgress,
  Alert,
  Box,
  Grid,
  Card,
  CardContent,
  CardMedia,
  Pagination,
} from '@mui/material';
import { searchImages } from '../../services/api_image_search';
import FullScreenImageModal from '../../components/patent-viz/FullScreenImageModal';

// Placeholder for ResultCard component - will be implemented later
const ResultCard = ({ result, onImageClick, isMatchedImage }) => {
  const matchedImagePath = result.matched_image_path;
  const allFigPaths = result.fig_file_paths || [];

  return (
    <Card sx={{ mb: 3, border: isMatchedImage ? '2px solid blue' : '1px solid #ddd' }}>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Score: {result.score ? result.score.toFixed(4) : 'N/A'}
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          Reg No: {result.reg_no}
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          Patent Title: {result.patent_title}
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          Date Published: {result.date_published}
        </Typography>

        <Box sx={{ mt: 2 }}>
          <Typography variant="subtitle2" gutterBottom>
            Matched Image:
          </Typography>
          {matchedImagePath && (
            <CardMedia
              component="img"
              image={matchedImagePath}
              alt="Matched Patent"
              sx={{
                width: 150,
                height: 150,
                objectFit: 'contain',
                border: `2px solid ${isMatchedImage ? 'blue' : 'transparent'}`,
                cursor: 'pointer',
              }}
              onClick={() => onImageClick(matchedImagePath)}
            />
          )}
        </Box>

        <Box sx={{ mt: 2 }}>
          <Typography variant="subtitle2" gutterBottom>
            All Patent Images:
          </Typography>
          <Grid container spacing={1}>
            {allFigPaths.map((imgPath, index) => (
              <Grid item key={index}>
                <CardMedia
                  component="img"
                  image={imgPath}
                  alt={`Patent Figure ${index + 1}`}
                  sx={{
                    width: 100,
                    height: 100,
                    objectFit: 'contain',
                    border: `2px solid ${imgPath === matchedImagePath ? 'blue' : 'transparent'}`,
                    cursor: 'pointer',
                  }}
                  onClick={() => onImageClick(imgPath)}
                />
              </Grid>
            ))}
          </Grid>
        </Box>
      </CardContent>
    </Card>
  );
};


function ImageSearcherPage() {
  const [selectedImage, setSelectedImage] = useState(null);
  const [uploadedImagePreview, setUploadedImagePreview] = useState(null); // New state for image preview
  const [numClose, setNumClose] = useState(10);
  const [regNoConstraint, setRegNoConstraint] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [searchTimings, setSearchTimings] = useState(null); // New state for timings
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10); // Fixed items per page for pagination

  const [selectedImageForModal, setSelectedImageForModal] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const openModal = useCallback((imageUrl) => {
    setSelectedImageForModal(imageUrl);
    setIsModalOpen(true);
  }, []);

  const handleCloseModal = useCallback(() => {
    setIsModalOpen(false);
    setSelectedImageForModal(null);
  }, []);

  const handleImageChange = useCallback((event) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];
      setSelectedImage(file);
      setUploadedImagePreview(URL.createObjectURL(file)); // Create a URL for the selected image
    }
  }, []);

  const handleNumCloseChange = useCallback((event) => {
    const value = parseInt(event.target.value, 10);
    setNumClose(isNaN(value) || value <= 0 ? 10 : value); // Ensure positive integer
  }, []);

  const handleRegNoConstraintChange = useCallback((event) => {
    setRegNoConstraint(event.target.value);
  }, []);

  const handleSearch = useCallback(async () => {
    if (!selectedImage) {
      setError('Please upload an image to search.');
      return;
    }

    setIsLoading(true);
    setError(null);
    setSearchResults([]);
    setCurrentPage(1); // Reset to first page on new search

    try {
      const response = await searchImages(
        selectedImage,
        numClose,
        regNoConstraint ? parseInt(regNoConstraint, 10) : undefined
      );
      setSearchResults(response.similar_images_info);
      setSearchTimings(response.timings);
    } catch (apiError) {
      setError(apiError.message || 'Failed to perform image search.');
      setSearchTimings(null); // Clear timings on error
    } finally {
      setIsLoading(false);
    }
  }, [selectedImage, numClose, regNoConstraint]);

  const handlePageChange = useCallback((event, value) => {
    setCurrentPage(value);
  }, []);

  // Calculate current items to display based on pagination
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = searchResults.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(searchResults.length / itemsPerPage);

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Image Searcher
      </Typography>

      {/* Input Section */}
      <Box sx={{ mb: 4, p: 3, border: '1px solid #ccc', borderRadius: '8px' }}>
        <Typography variant="h6" gutterBottom>
          Search Input
        </Typography>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={4}>
            <Button
              variant="contained"
              component="label"
              fullWidth
            >
              Upload Image (Required)
              <input type="file" hidden accept="image/*" onChange={handleImageChange} />
            </Button>
            {selectedImage && (
              <Typography variant="body2" sx={{ mt: 1 }}>
                Selected: {selectedImage.name}
              </Typography>
            )}
            {uploadedImagePreview && (
              <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>
                <CardMedia
                  component="img"
                  image={uploadedImagePreview}
                  alt="Uploaded Preview"
                  sx={{
                    maxWidth: '100%',
                    maxHeight: 200,
                    objectFit: 'contain',
                    border: '1px solid #ddd',
                  }}
                />
              </Box>
            )}
          </Grid>
          <Grid item xs={12} md={4}>
            <TextField
              label="Number of Close Images to Return"
              type="number"
              value={numClose}
              onChange={handleNumCloseChange}
              fullWidth
              inputProps={{ min: 1 }}
              helperText="Default: 10"
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <TextField
              label="Registration Number Constraint"
              type="text"
              value={regNoConstraint}
              onChange={handleRegNoConstraintChange}
              fullWidth
              helperText="Optional: Enter an integer registration number"
            />
          </Grid>
          <Grid item xs={12}>
            <Button
              variant="contained"
              color="primary"
              onClick={handleSearch}
              disabled={isLoading}
              fullWidth
            >
              {isLoading ? <CircularProgress size={24} /> : 'Search'}
            </Button>
          </Grid>
        </Grid>
      </Box>

      {/* Performance Timings Display Section */}
      {searchTimings && (
        <Box sx={{ mb: 4, p: 3, border: '1px solid #ccc', borderRadius: '8px', backgroundColor: '#f9f9f9' }}>
          <Typography variant="h6" gutterBottom>
            Performance Timings
          </Typography>
          <Typography variant="body1">
            Embedding Conversion: {searchTimings.embedding_conversion_sec?.toFixed(4) || 'N/A'} seconds
          </Typography>
          <Typography variant="body1">
            Qdrant Retrieval: {searchTimings.qdrant_retrieval_sec?.toFixed(4) || 'N/A'} seconds
          </Typography>
          <Typography variant="body1">
            Database Retrieval: {searchTimings.db_retrieval_sec?.toFixed(4) || 'N/A'} seconds
          </Typography>
        </Box>
      )}

      {/* Results Display Section */}
      <Box sx={{ mt: 4 }}>
        <Typography variant="h5" component="h2" gutterBottom>
          Search Results
        </Typography>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {isLoading && (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <CircularProgress />
          </Box>
        )}

        {!isLoading && searchResults.length === 0 && !error && (
          <Alert severity="info">No results found. Please try a different search.</Alert>
        )}

        {!isLoading && searchResults.length > 0 && (
          <>
            <Grid container spacing={3}>
              {currentItems.map((result, index) => (
                <Grid item xs={12} key={index}>
                  <ResultCard result={result} onImageClick={openModal} />
                </Grid>
              ))}
            </Grid>
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
              <Pagination
                count={totalPages}
                page={currentPage}
                onChange={handlePageChange}
                color="primary"
                size="large"
              />
            </Box>
          </>
        )}
      </Box>

      <FullScreenImageModal
        imageUrl={selectedImageForModal}
        open={isModalOpen}
        onClose={handleCloseModal}
      />
    </Container>
  );
}

export default ImageSearcherPage;