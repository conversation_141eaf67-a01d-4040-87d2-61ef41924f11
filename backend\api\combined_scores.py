# backend/api/combined_scores.py
import uuid
import json
from datetime import datetime
from flask import Blueprint, request, jsonify, current_app
from backend.extensions import db
from backend.database.models import ModelTestsCombinedScoresConfig, ModelTestsModel
from sqlalchemy.exc import <PERSON>teg<PERSON><PERSON><PERSON><PERSON>, DataError
from sqlalchemy import func

combined_scores_bp = Blueprint('combined_scores', __name__, url_prefix='/api/combined-scores')

VALID_IP_CATEGORIES = ['trademark', 'copyright', 'patent']

def validate_model_weights(model_weights_dict, ip_category):
    """
    Validates the model_weights dictionary.
    - Checks if it's a dictionary.
    - Checks if keys are valid UUIDs corresponding to active models applicable to the ip_category.
    - Checks if values are numbers (int or float).
    - Checks if weights sum approximately to 1.0.
    Returns: (is_valid, error_message)
    """
    if not isinstance(model_weights_dict, dict):
        return False, "model_weights must be a JSON object."

    if not model_weights_dict:
         return False, "model_weights cannot be empty."

    total_weight = 0.0
    valid_model_ids = set()

    # Get active models applicable to the category
    try:
        applicable_models = db.session.query(ModelTestsModel.model_id).filter(
            ModelTestsModel.is_active == True,
            # Check if the applicable_ip_category array contains the ip_category or 'all'
            (ModelTestsModel.applicable_ip_category.contains([ip_category]) | ModelTestsModel.applicable_ip_category.contains(['all']))
        ).all()
        valid_model_ids = {str(model_id[0]) for model_id in applicable_models}
    except Exception as e:
        current_app.logger.error(f"Error fetching applicable models for validation: {e}", exc_info=True)
        return False, "Internal error validating model IDs."

    for model_id_str, weight in model_weights_dict.items():
        # Validate model ID format
        try:
            uuid.UUID(model_id_str) # Check if it's a valid UUID string
        except ValueError:
            return False, f"Invalid model ID format: '{model_id_str}'. Must be a valid UUID."

        # Validate model ID exists and is applicable
        if model_id_str not in valid_model_ids:
            return False, f"Model ID '{model_id_str}' is not a valid, active model for IP category '{ip_category}'."

        # Validate weight type and value
        if not isinstance(weight, (int, float)):
            return False, f"Weight for model '{model_id_str}' must be a number (int or float)."
        if not (0 <= weight <= 1):
             return False, f"Weight for model '{model_id_str}' must be between 0 and 1 (inclusive)."

        total_weight += weight

    # Check if weights sum to approximately 1
    # Use a tolerance for floating point comparisons
    if not (1.0 - 1e-9 <= total_weight <= 1.0 + 1e-9):
        return False, f"Model weights must sum to 1.0 (current sum: {total_weight:.4f})."

    return True, None


@combined_scores_bp.route('', methods=['GET'])
def list_combined_score_configs():
    """
    Lists combined score configurations.
    Query Parameters:
        - ip_category (string): Filter by 'trademark', 'copyright', or 'patent'.
        - is_active (boolean string e.g., 'true'): Filter by active status.
    """
    ip_category = request.args.get('ip_category', type=str)
    is_active_str = request.args.get('is_active', type=str)

    query = ModelTestsCombinedScoresConfig.query

    if ip_category:
        if ip_category not in VALID_IP_CATEGORIES:
            return jsonify({"error": f"Invalid ip_category. Must be one of: {VALID_IP_CATEGORIES}"}), 400
        query = query.filter(ModelTestsCombinedScoresConfig.ip_category == ip_category)

    if is_active_str is not None:
        is_active = is_active_str.lower() == 'true'
        query = query.filter(ModelTestsCombinedScoresConfig.is_active == is_active)

    query = query.order_by(ModelTestsCombinedScoresConfig.ip_category, ModelTestsCombinedScoresConfig.config_name)

    try:
        configs = query.all()
        config_list = [
            {
                "config_id": str(config.config_id),
                "config_name": config.config_name,
                "ip_category": config.ip_category,
                "model_weights": config.model_weights, # Already JSON in DB
                "is_active": config.is_active,
                "created_at": config.created_at.isoformat() if config.created_at else None,
                "updated_at": config.updated_at.isoformat() if config.updated_at else None,
            } for config in configs
        ]
        return jsonify(config_list), 200
    except Exception as e:
        current_app.logger.error(f"Error retrieving combined score configs: {e}", exc_info=True)
        return jsonify({"error": "An internal error occurred while retrieving configurations."}), 500


@combined_scores_bp.route('', methods=['POST'])
def create_combined_score_config():
    """
    Creates a new combined score configuration.
    Expects JSON body:
    {
        "config_name": "string",
        "ip_category": "trademark|copyright|patent",
        "model_weights": {"model_uuid_1": weight1, "model_uuid_2": weight2, ...}
    }
    """
    data = request.get_json()
    if not data:
        return jsonify({"error": "Request body must be JSON."}), 400

    config_name = data.get('config_name')
    ip_category = data.get('ip_category')
    model_weights = data.get('model_weights') # Expecting a dict

    # --- Input Validation ---
    errors = {}
    if not config_name or not isinstance(config_name, str) or not config_name.strip():
        errors['config_name'] = "Config name is required and must be a non-empty string."
    if not ip_category or ip_category not in VALID_IP_CATEGORIES:
        errors['ip_category'] = f"IP category is required and must be one of: {VALID_IP_CATEGORIES}."
    if model_weights is None: # Check for presence before type checking
         errors['model_weights'] = "Model weights dictionary is required."
    else:
        # Validate model_weights structure, content, and sum only if ip_category is valid
        if ip_category in VALID_IP_CATEGORIES:
            is_valid, weight_error = validate_model_weights(model_weights, ip_category)
            if not is_valid:
                errors['model_weights'] = weight_error
        # else: wait for ip_category error to be reported

    if errors:
        return jsonify({"error": "Validation failed", "details": errors}), 400
    # --- End Validation ---

    # Create new config object
    new_config = ModelTestsCombinedScoresConfig(
        config_id=uuid.uuid4(),
        config_name=config_name.strip(),
        ip_category=ip_category,
        model_weights=model_weights, # Store as JSON
        is_active=True # Default to active on creation
    )

    try:
        db.session.add(new_config)
        db.session.commit()
        current_app.logger.info(f"Created new combined score config '{new_config.config_name}' ({new_config.config_id}) for {new_config.ip_category}.")

        # Return the created object
        return jsonify({
            "config_id": str(new_config.config_id),
            "config_name": new_config.config_name,
            "ip_category": new_config.ip_category,
            "model_weights": new_config.model_weights,
            "is_active": new_config.is_active,
            "created_at": new_config.created_at.isoformat(),
            "updated_at": new_config.updated_at.isoformat(),
        }), 201

    except IntegrityError: # Likely duplicate config_name for the same ip_category
        db.session.rollback()
        current_app.logger.warning(f"Attempted to create duplicate combined score config name '{config_name}' for category '{ip_category}'.")
        return jsonify({"error": f"A configuration with name '{config_name}' already exists for IP category '{ip_category}'."}), 409 # Conflict
    except DataError as de: # e.g., value too long for column
         db.session.rollback()
         current_app.logger.error(f"Data error creating combined score config: {de}", exc_info=True)
         return jsonify({"error": "Data validation error occurred.", "details": str(de)}), 400
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error creating combined score config: {e}", exc_info=True)
        return jsonify({"error": "An internal error occurred while creating the configuration."}), 500


@combined_scores_bp.route('/<uuid:config_id>', methods=['PUT'])
def update_combined_score_config(config_id):
    """
    Updates an existing combined score configuration.
    Can update config_name, model_weights, and is_active status.
    IP category cannot be changed.
    Expects JSON body with fields to update, e.g.:
    {
        "config_name": "Updated Name",
        "model_weights": {"model_uuid_1": new_weight1, ...},
        "is_active": false
    }
    """
    current_app.logger.info(f"Attempting to update config with ID: {config_id}")
    config = db.session.get(ModelTestsCombinedScoresConfig, config_id)
    if not config:
        current_app.logger.warning(f"Configuration with ID {config_id} not found for update.")
        return jsonify({"error": "Configuration not found."}), 404
    current_app.logger.info(f"Found config '{config.config_name}' ({config.config_id}) for update.")

    data = request.get_json()
    if not data:
        return jsonify({"error": "Request body must be JSON."}), 400

    errors = {}
    updated_fields = {}

    # Validate and stage updates
    if 'config_name' in data:
        config_name = data['config_name']
        if not config_name or not isinstance(config_name, str) or not config_name.strip():
            errors['config_name'] = "Config name must be a non-empty string."
        else:
            updated_fields['config_name'] = config_name.strip()

    if 'model_weights' in data:
        model_weights = data['model_weights']
        # Use the existing config's ip_category for validation
        is_valid, weight_error = validate_model_weights(model_weights, config.ip_category)
        if not is_valid:
            errors['model_weights'] = weight_error
        else:
            updated_fields['model_weights'] = model_weights

    if 'is_active' in data:
        is_active = data['is_active']
        if not isinstance(is_active, bool):
            errors['is_active'] = "is_active must be a boolean (true or false)."
        else:
            updated_fields['is_active'] = is_active

    if 'ip_category' in data and data['ip_category'] != config.ip_category:
         errors['ip_category'] = "IP category cannot be changed after creation."

    if errors:
        return jsonify({"error": "Validation failed", "details": errors}), 400

    if not updated_fields:
        return jsonify({"message": "No fields provided for update."}), 400 # Or 200 OK? 400 seems better.

    # Apply updates
    needs_update = False
    for field, value in updated_fields.items():
        if getattr(config, field) != value:
            setattr(config, field, value)
            needs_update = True

    if not needs_update:
         return jsonify({ # Return current state if no actual changes
            "config_id": str(config.config_id),
            "config_name": config.config_name,
            "ip_category": config.ip_category,
            "model_weights": config.model_weights,
            "is_active": config.is_active,
            "created_at": config.created_at.isoformat(),
            "updated_at": config.updated_at.isoformat(),
         }), 200

    try:
        config.updated_at = datetime.utcnow() # Manually update timestamp
        db.session.commit()
        current_app.logger.info(f"Updated combined score config '{config.config_name}' ({config.config_id}). Fields updated: {list(updated_fields.keys())}")

        # Return the updated object
        return jsonify({
            "config_id": str(config.config_id),
            "config_name": config.config_name,
            "ip_category": config.ip_category,
            "model_weights": config.model_weights,
            "is_active": config.is_active,
            "created_at": config.created_at.isoformat(),
            "updated_at": config.updated_at.isoformat(),
        }), 200

    except IntegrityError: # Likely duplicate config_name for the same ip_category
        db.session.rollback()
        current_app.logger.warning(f"Update conflict for combined score config '{config_id}'. Potential duplicate name '{updated_fields.get('config_name')}' for category '{config.ip_category}'.")
        return jsonify({"error": f"Update failed. A configuration with name '{updated_fields.get('config_name')}' may already exist for IP category '{config.ip_category}'."}), 409
    except DataError as de: # e.g., value too long for column
         db.session.rollback()
         current_app.logger.error(f"Data error updating combined score config {config_id}: {de}", exc_info=True)
         return jsonify({"error": "Data validation error occurred.", "details": str(de)}), 400
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error updating combined score config {config_id}: {e}", exc_info=True)
        return jsonify({"error": "An internal error occurred while updating the configuration."}), 500


@combined_scores_bp.route('/<uuid:config_id>', methods=['DELETE'])
def delete_combined_score_config(config_id):
    """
    Deletes a combined score configuration.
    """
    config = db.session.get(ModelTestsCombinedScoresConfig, config_id)
    if not config:
        return jsonify({"error": "Configuration not found."}), 404

    try:
        db.session.delete(config)
        db.session.commit()
        current_app.logger.info(f"Deleted combined score config '{config.config_name}' ({config.config_id}).")
        return jsonify({"message": "Configuration deleted successfully."}), 200 # 204 No Content is also an option, but 200 with message is fine.
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error deleting combined score config {config_id}: {e}", exc_info=True)
        return jsonify({"error": "An internal error occurred while deleting the configuration."}), 500