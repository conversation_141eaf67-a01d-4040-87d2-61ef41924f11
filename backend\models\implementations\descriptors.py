import cv2
import numpy as np
import logging
from backend.models.base import ImageModelBase

logger = logging.getLogger(__name__)

# --- Helper function for Sobel Preprocessing ---
def sobel_preprocess(image: np.ndarray) -> np.ndarray:
    """
    Applies Sobel filter to enhance edges in a grayscale image.
    From POC script.
    """
    if image is None or image.ndim != 2:
        logger.warning("Sobel preprocessing received invalid input.")
        return image # Return original if not grayscale

    # Apply Sobel filter in x and y directions
    sobelx = cv2.Sobel(image, cv2.CV_64F, 1, 0, ksize=3)
    sobely = cv2.Sobel(image, cv2.CV_64F, 0, 1, ksize=3)

    # Compute magnitude and normalize
    magnitude = cv2.magnitude(sobelx, sobely)
    magnitude = cv2.normalize(magnitude, None, 0, 255, cv2.NORM_MINMAX)
    magnitude = np.uint8(magnitude) # Convert back to uint8

    return magnitude

# --- Existing SiftModel Code ---
class SiftModel(ImageModelBase):
    """
    Concrete implementation for the SIFT (Scale-Invariant Feature Transform) model using OpenCV.
    """
    def __init__(self, model_id: str, config: dict):
        """
        Initializes the SiftModel.

        Args:
            model_id: The unique identifier for this model instance.
            config: Dictionary containing model configuration, expecting 'n_features'.
        """
        super().__init__(config) # Correct: Pass only config to base
        self.model_id = model_id # Store model_id in subclass
        self.n_features = config.get('n_features', 0) # 0 means retain all features
        self._sift = None
        logger.info(f"Initializing SiftModel (ID: {self.model_id}) with n_features={self.n_features}")

    def load(self):
        """
        Initializes the OpenCV SIFT detector.
        """
        try:
            self._sift = cv2.SIFT_create(nfeatures=self.n_features)
            logger.info(f"SiftModel (ID: {self.model_id}) loaded successfully.")
        except Exception as e:
            logger.error(f"Error loading SiftModel (ID: {self.model_id}): {e}", exc_info=True)
            raise

    def unload(self):
        """
        Releases the SIFT detector object.
        """
        self._sift = None
        logger.info(f"SiftModel (ID: {self.model_id}) unloaded.")

    def get_model_type(self) -> str:
        """
        Returns the type of the model.
        """
        return 'descriptor'

    def preprocess(self, image_path: str) -> np.ndarray | None:
        """
        Loads an image from the given path and converts it to grayscale.

        Args:
            image_path: Path to the image file.

        Returns:
            Grayscale image as a NumPy array, or None if loading fails.
        """
        try:
            image = cv2.imread(image_path, cv2.IMREAD_COLOR)
            if image is None:
                logger.error(f"Failed to load image: {image_path}")
                return None
            gray_image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            return gray_image
        except Exception as e:
            logger.error(f"Error preprocessing image {image_path} for SiftModel (ID: {self.model_id}): {e}", exc_info=True)
            return None

    def compute_features(self, preprocessed_image: np.ndarray) -> tuple | None:
        """
        Detects keypoints and computes SIFT descriptors for the preprocessed image.

        Args:
            preprocessed_image: Grayscale image as a NumPy array.

        Returns:
            A tuple containing keypoints and descriptors (NumPy array),
            or None if the model is not loaded or no features are found.
        """
        if self._sift is None:
            logger.error(f"SiftModel (ID: {self.model_id}) is not loaded. Cannot compute features.")
            return None
        if preprocessed_image is None:
             logger.error(f"Cannot compute features on None input for SiftModel (ID: {self.model_id}).")
             return None

        try:
            keypoints, descriptors = self._sift.detectAndCompute(preprocessed_image, None)
            if descriptors is None:
                logger.warning(f"No SIFT features found for image in model {self.model_id}.")
                # Return empty tuple or specific format expected by downstream tasks
                return ([], np.array([]))
            logger.debug(f"Computed {len(descriptors)} SIFT features for model {self.model_id}.")
            # Note: keypoints are cv2.KeyPoint objects, descriptors is a numpy array
            return (keypoints, descriptors)
        except Exception as e:
            logger.error(f"Error computing SIFT features for model {self.model_id}: {e}", exc_info=True)
            return None

    def normalize_score(self, raw_score: float) -> float:
        """
        Normalizes a raw comparison score to a similarity value between 0.0 and 1.0.

        Args:
            raw_score: The raw score from a comparison method (e.g., distance, match count).
                       Lower distance or higher match count usually indicates higher similarity.

        Returns:
            Normalized similarity score (0.0 to 1.0).
        """
        # Placeholder normalization. Assumes the raw_score is already somewhat
        # indicative of similarity (e.g., ratio of good matches).
        # Refinement needed based on the specific comparison logic's raw score output.
        # For BFMatcher with NORM_L2, raw_score is distance (lower is better).
        # A possible normalization: score = max(0, 1 - (distance / threshold))
        # For match count: score = num_matches / max_possible (hard to define max_possible)
        # Using a simple clamp for now.
        normalized = max(0.0, min(1.0, float(raw_score)))
        logger.debug(f"Normalizing raw score {raw_score} to {normalized} for SiftModel (ID: {self.model_id})")
        return normalized


# --- New OrbModel ---
class OrbModel(ImageModelBase):
    """
    Concrete implementation for the ORB (Oriented FAST and Rotated BRIEF) model using OpenCV.
    """
    def __init__(self, model_id: str, config: dict):
        """
        Initializes the OrbModel.

        Args:
            model_id: The unique identifier for this model instance.
            config: Dictionary containing model configuration, expecting 'n_features'.
        """
        super().__init__(config) # Correct: Pass only config to base
        self.model_id = model_id # Store model_id in subclass
        self.n_features = config.get('n_features', 500) # Default ORB features
        self._orb = None
        logger.info(f"Initializing OrbModel (ID: {self.model_id}) with n_features={self.n_features}")

    def load(self):
        """
        Initializes the OpenCV ORB detector.
        """
        try:
            self._orb = cv2.ORB_create(nfeatures=self.n_features)
            logger.info(f"OrbModel (ID: {self.model_id}) loaded successfully.")
        except Exception as e:
            logger.error(f"Error loading OrbModel (ID: {self.model_id}): {e}", exc_info=True)
            raise

    def unload(self):
        """
        Releases the ORB detector object.
        """
        self._orb = None
        logger.info(f"OrbModel (ID: {self.model_id}) unloaded.")

    def get_model_type(self) -> str:
        return 'descriptor'

    def preprocess(self, image_path: str) -> np.ndarray | None:
        """
        Loads an image from the given path and converts it to grayscale.
        """
        try:
            image = cv2.imread(image_path, cv2.IMREAD_COLOR)
            if image is None:
                logger.error(f"Failed to load image: {image_path}")
                return None
            gray_image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            return gray_image
        except Exception as e:
            logger.error(f"Error preprocessing image {image_path} for OrbModel (ID: {self.model_id}): {e}", exc_info=True)
            return None

    def compute_features(self, preprocessed_image: np.ndarray) -> tuple | None:
        """
        Detects keypoints and computes ORB descriptors for the preprocessed image.
        """
        if self._orb is None:
            logger.error(f"OrbModel (ID: {self.model_id}) is not loaded. Cannot compute features.")
            return None
        if preprocessed_image is None:
             logger.error(f"Cannot compute features on None input for OrbModel (ID: {self.model_id}).")
             return None

        try:
            keypoints, descriptors = self._orb.detectAndCompute(preprocessed_image, None)
            if descriptors is None:
                logger.warning(f"No ORB features found for image in model {self.model_id}.")
                return ([], np.array([])) # Return empty tuple
            logger.debug(f"Computed {len(descriptors)} ORB features for model {self.model_id}.")
            # Descriptors are uint8
            return (keypoints, descriptors)
        except Exception as e:
            logger.error(f"Error computing ORB features for model {self.model_id}: {e}", exc_info=True)
            return None

    def normalize_score(self, raw_score: float) -> float:
        """
        Normalizes a raw comparison score (e.g., distance from BFMatcher with NORM_HAMMING).
        Lower distance means higher similarity.
        """
        # ORB uses Hamming distance. Max possible Hamming distance for ORB (32 bytes * 8 bits) = 256.
        # Normalize distance to similarity: 1 - (distance / max_distance)
        max_distance = 256.0
        similarity = 1.0 - (float(raw_score) / max_distance)
        normalized = max(0.0, min(1.0, similarity))
        logger.debug(f"Normalizing raw score {raw_score} to {normalized} for OrbModel (ID: {self.model_id})")
        return normalized


# --- New EnhancedSiftModel ---
class EnhancedSiftModel(SiftModel):
    """
    SIFT model with Sobel edge enhancement preprocessing.
    Inherits from SiftModel and overrides preprocess.
    """
    def __init__(self, model_id: str, config: dict):
        super().__init__(model_id, config) # Call parent's __init__ to set n_features etc.
        logger.info(f"Initializing EnhancedSiftModel (ID: {model_id}) with n_features={self.n_features}") # Use model_id from arg

    def preprocess(self, image_path: str) -> np.ndarray | None:
        """
        Loads image, converts to grayscale, and applies Sobel filter.
        """
        gray_image = super().preprocess(image_path) # Use parent's loading and grayscale conversion
        if gray_image is None:
            return None
        try:
            enhanced_image = sobel_preprocess(gray_image)
            logger.debug(f"Applied Sobel preprocessing for EnhancedSiftModel (ID: {self.model_id})")
            return enhanced_image
        except Exception as e:
            logger.error(f"Error during Sobel preprocessing for EnhancedSiftModel (ID: {self.model_id}): {e}", exc_info=True)
            return None

    # compute_features is inherited from SiftModel
    # normalize_score is inherited from SiftModel (may need adjustment if comparison changes)


# --- New EnhancedOrbModel ---
class EnhancedOrbModel(OrbModel):
    """
    ORB model with Sobel edge enhancement preprocessing.
    Inherits from OrbModel and overrides preprocess.
    """
    def __init__(self, model_id: str, config: dict):
        super().__init__(model_id, config) # Call parent's __init__ to set n_features etc.
        logger.info(f"Initializing EnhancedOrbModel (ID: {model_id}) with n_features={self.n_features}") # Use model_id from arg

    def preprocess(self, image_path: str) -> np.ndarray | None:
        """
        Loads image, converts to grayscale, and applies Sobel filter.
        """
        gray_image = super().preprocess(image_path) # Use parent's loading and grayscale conversion
        if gray_image is None:
            return None
        try:
            enhanced_image = sobel_preprocess(gray_image)
            logger.debug(f"Applied Sobel preprocessing for EnhancedOrbModel (ID: {self.model_id})")
            return enhanced_image
        except Exception as e:
            logger.error(f"Error during Sobel preprocessing for EnhancedOrbModel (ID: {self.model_id}): {e}", exc_info=True)
            return None

    # compute_features is inherited from OrbModel
    # normalize_score is inherited from OrbModel
