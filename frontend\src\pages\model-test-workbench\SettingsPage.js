import React, { useState } from 'react';
import { Box, Tabs, Tab, Divider } from '@mui/material';
import ImageUpload from '../../components/model-test-workbench/ImageUpload';
import ImageBrowser from '../../components/model-test-workbench/ImageBrowser';
import ModelManagement from '../../components/model-test-workbench/ModelManagement';
import CombinedScoresConfig from '../../components/model-test-workbench/CombinedScoresConfig';
import FeatureComputation from '../../components/model-test-workbench/FeatureComputation'; // Import the FeatureComputation component
import CollectionManagement from '../../components/model-test-workbench/CollectionManagement'; // Import the new component

// Helper component to display tab content
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`settings-tabpanel-${index}`}
      aria-labelledby={`settings-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

// Helper function for accessibility props
function a11yProps(index) {
  return {
    id: `settings-tab-${index}`,
    'aria-controls': `settings-tabpanel-${index}`,
  };
}

const SettingsPage = () => {
  const [currentTab, setCurrentTab] = useState(0);

  const handleTabChange = (event, newValue) => {
    setCurrentTab(newValue);
  };

  const tabLabels = ["Data Management", "Model Management", "Combined Scores", "Feature Computation", "Qdrant Management"]; // Added new tab label

  return (
    <Box sx={{ width: '100%' }}>
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={currentTab} onChange={handleTabChange} aria-label="Settings Tabs">
          {tabLabels.map((label, index) => (
            <Tab label={label} {...a11yProps(index)} key={label} />
          ))}
        </Tabs>
      </Box>

      {/* Tab Content Panels */}
      <TabPanel value={currentTab} index={0}>
        <ImageUpload />
        <Divider sx={{ my: 4 }} /> {/* Add a visual separator */}
        <ImageBrowser /> {/* Add the ImageBrowser component */}
      </TabPanel>
      <TabPanel value={currentTab} index={1}>
        <ModelManagement /> {/* Add the ModelManagement component */}
      </TabPanel>
      <TabPanel value={currentTab} index={2}>
        <CombinedScoresConfig /> {/* Add the CombinedScoresConfig component */}
      </TabPanel>
      <TabPanel value={currentTab} index={3}>
        <FeatureComputation /> {/* Add the FeatureComputation component */}
      </TabPanel>
      <TabPanel value={currentTab} index={4}> {/* New TabPanel for Qdrant Management */}
        <CollectionManagement />
      </TabPanel>
    </Box>
  );
};

export default SettingsPage;