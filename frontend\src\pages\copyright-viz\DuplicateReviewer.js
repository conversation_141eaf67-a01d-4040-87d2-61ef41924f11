import React, { useState, useEffect, useCallback } from 'react';
// Assuming these are correctly defined to call your Flask endpoints
import { getNextDuplicatePair, resolveDuplicatePair } from '../../services/api_copyright_viz';
import './DuplicateReviewer.css';
// --- MUI Imports ---
// We import the components we need from Material-UI
import Button from '@mui/material/Button';
import Stack from '@mui/material/Stack'; // A great component for simple layouts like a row of buttons
import CircularProgress from '@mui/material/CircularProgress'; // A nicer loading spinner

// --- M<PERSON> Icon Imports ---
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import DoNotDisturbOnIcon from '@mui/icons-material/DoNotDisturbOn';
import HighlightOffIcon from '@mui/icons-material/HighlightOff';


// The AssetCard component remains the same as it's well-designed.
const AssetCard = ({ asset }) => {
    if (!asset) return null;
    return (
        <div className="asset-card">
            <div className="asset-image-container">
                <img src={asset.high_res_path} alt={asset.registration_number} />
            </div>
            <div className="asset-info">
                <h4>{asset.registration_number}</h4>
                <p><strong>Plaintiff:</strong> {asset.plaintiff_name || 'N/A'}</p>
                {/* These details were not in the API response, so they will show N/A */}
                <p><strong>Plaintiff ID:</strong> {asset.plaintiff_id || 'N/A'}</p>
                <p><strong>Copyright Claimant: </strong> {asset.copyright_claimant || 'N/A'}</p>
                <p><strong>Title: </strong> {asset.title || 'N/A'}</p>
                <p><strong>Number of cases:</strong> {asset.number_of_cases || 'N/A'}</p>
                <p><strong>Docket:</strong> {asset.docket}</p>
                <p><strong>Filename:</strong> {asset.filename}</p>
            </div>
        </div>
    );
};

// NEW: Helper function to transform the complex API object into a flatter, more usable format
const transformApiData = (imageDetails) => {
    if (!imageDetails || !imageDetails.qdrant_point) {
        return null;
    }
    return {
        high_res_path: imageDetails.high_res_path,
        registration_number: imageDetails.qdrant_point.payload.reg_no,
        plaintiff_name: imageDetails.qdrant_point.payload.plaintiff_name,
        number_of_cases: imageDetails.qdrant_point.payload.number_of_cases,
        docket: imageDetails.qdrant_point.payload.docket,
        filename: imageDetails.qdrant_point.payload.filename,
        plaintiff_id: imageDetails.qdrant_point.payload.plaintiff_id,
        title: imageDetails.title,
        copyright_claimant: imageDetails.copyright_claimant,
        // We include the original full details object, which is crucial for the resolve API call
        original_details: imageDetails
    };
};

const DuplicateReviewer = () => {
    const [pair, setPair] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [resolving, setResolving] = useState(false);

    const fetchNextPair = useCallback(async () => {
        setLoading(true);
        setError(null);
        try {
            const result = await getNextDuplicatePair();
            // CHANGED: Adapt to the new API response structure
            const pairData = result?.most_similar_pair;

            if (pairData && pairData.image_details_left && pairData.image_details_right) {
                // Use our helper to create a clean state object
                setPair({
                    left: transformApiData(pairData.image_details_left),
                    right: transformApiData(pairData.image_details_right),
                    similarity: pairData.similarity_score
                });
            } else {
                // This handles the "No new duplicate pairs found" message from the backend
                setPair(null);
            }
        } catch (err) {
            setError(err.message || 'An unknown error occurred.');
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchNextPair();
    }, [fetchNextPair]);

    const handleResolve = async (decision) => {
        if (!pair) return;

        let payload;

        // CHANGED: Construct the payload exactly as the backend expects
        switch (decision) {
            case 'keep_left':
                payload = {
                    action: 'delete',
                    image_details: pair.right.original_details // Send the original details of the image to delete
                };
                break;

            case 'keep_right':
                // To "keep right", we must "delete left"
                payload = {
                    action: 'delete',
                    image_details: pair.left.original_details // Send the original details of the image to delete
                };
                break;

            case 'not_duplicate':
                // To mark as "not a duplicate", we use the "keep" action
                payload = {
                    action: 'keep',
                    image_details_left: pair.left.original_details,
                    image_details_right: pair.right.original_details
                };
                break;

            default:
                // Do nothing if the action is unknown
                return;
        }

        setResolving(true);
        setError(null);
        try {
            await resolveDuplicatePair(payload);
            fetchNextPair(); // Fetch the next pair after resolving successfully
        } catch (err) {
            setError(err.message);
        } finally {
            setResolving(false);
        }
    };

    const handleSkip = async () => {
        if (!pair) return;

        setLoading(true);
        setError(null);
        try {
            // Call getNextDuplicatePair with skip flag and filenames
            const result = await getNextDuplicatePair({
                skip: true,
                image_left: pair.left.filename,
                image_right: pair.right.filename
            });
            const pairData = result?.most_similar_pair;
            if (pairData && pairData.image_details_left && pairData.image_details_right) {
                // Use our helper to create a clean state object
                setPair({
                    left: transformApiData(pairData.image_details_left),
                    right: transformApiData(pairData.image_details_right),
                    similarity: pairData.similarity_score
                });
            } else {
                // This handles the "No new duplicate pairs found" message from the backend
                setPair(null);
            }
        } catch (err) {
            setError(err.message || 'An unknown error occurred.');
        } finally {
            setLoading(false);
        }
    };

    const renderContent = () => {
        if (loading) {
            return <div className="loading-spinner"></div>;
        }
        if (error) {
            return <p className="error-message">Error: {error}</p>;
        }
        if (!pair) {
            return (
                <div className="no-pairs-message">
                    <h2>All Clear!</h2>
                    <p>No duplicate pairs to review at the moment.</p>
                    <button onClick={fetchNextPair} disabled={loading}>Check for More</button>
                </div>
            );
        }
        return (
            <div className="reviewer-layout">
                <div className="comparison-area">
                    {/* The AssetCard component now receives the transformed data */}
                    <AssetCard asset={pair.left} />
                    <div className="similarity-score">
                        <h3>Similarity</h3>
                        <p>{(pair.similarity * 100).toFixed(2)}%</p>
                    </div>
                    <AssetCard asset={pair.right} />
                </div>
                <div className="action-bar">
                    <Stack direction="row" spacing={2} justifyContent="center" sx={{ mt: 4, mb: 2 }}>
                        <Button
                            variant="contained"
                            color="success"
                            startIcon={<CheckCircleOutlineIcon />}
                            onClick={() => handleResolve('keep_left')}
                            disabled={resolving}
                        >
                            Keep Left
                        </Button>

                        <Button
                            variant="outlined"
                            color="secondary"
                            startIcon={<DoNotDisturbOnIcon />}
                            onClick={() => handleResolve('not_duplicate')}
                            disabled={resolving}
                        >
                            Not a Duplicate
                        </Button>

                        <Button
                            variant="contained"
                            color="success"
                            startIcon={<CheckCircleOutlineIcon />}
                            onClick={() => handleResolve('keep_right')}
                            disabled={resolving}
                        >
                            Keep Right
                        </Button>
                        <Button
                            variant="contained"
                            color="warning"
                            startIcon={<HighlightOffIcon />}
                            onClick={handleSkip}
                            disabled={resolving}
                        >
                            Skip
                        </Button>
                    </Stack>
                </div>
            </div>
        );
    };

    return (
        <div className="duplicate-reviewer-container">
            <div className="reviewer-header">
                <h1>Duplicate Review</h1>
                <p>Review the asset pairs below and decide which one to keep.</p>
            </div>
            {renderContent()}
        </div>
    );
};

export default DuplicateReviewer;