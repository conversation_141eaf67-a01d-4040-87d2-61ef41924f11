import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Box,
  Typography,
  Button,
  CircularProgress,
  Alert,
  Paper,
  Grid,
  Chip, // To display status
  LinearProgress, // For progress indication
} from '@mui/material';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import { computeFeatures, getTaskStatus } from '../../services/api_model_workbench';

const IP_CATEGORIES = ["trademark", "copyright", "patent"]; // Use lowercase as likely expected by API path

const FeatureComputation = () => {
  // State to hold task info: { taskId: string | null, status: string, error: string, progress: number | null }
  const [tasks, setTasks] = useState(
    IP_CATEGORIES.reduce((acc, category) => {
      acc[category] = { taskId: null, status: 'Idle', error: '', progress: null };
      return acc;
    }, {})
  );
  const [loading, setLoading] = useState(
    IP_CATEGORIES.reduce((acc, category) => {
      acc[category] = false; // Loading state for triggering the task
      return acc;
    }, {})
  );

  const intervalRefs = useRef({}); // Store interval IDs for polling

  // Function to update task state
  const updateTaskState = (category, newState) => {
    setTasks(prevTasks => ({
      ...prevTasks,
      [category]: { ...prevTasks[category], ...newState },
    }));
  };

  // Function to start polling for a task
  const startPolling = useCallback((category, taskId) => {
    // Clear existing interval if any
    if (intervalRefs.current[category]) {
      clearInterval(intervalRefs.current[category]);
    }

    updateTaskState(category, { taskId: taskId, status: 'Pending', error: '', progress: null });

    // Poll immediately first time
    checkStatus(category, taskId);

    // Set up interval for subsequent polls
    intervalRefs.current[category] = setInterval(() => {
      checkStatus(category, taskId);
    }, 5000); // Poll every 5 seconds
  }, []); // No dependencies needed for useCallback here

  // Function to check task status
  const checkStatus = async (category, taskId) => {
    try {
      const response = await getTaskStatus(taskId);
      const taskData = response.data; // { task_id, status, result, error }
      const status = taskData.status;
      const result = taskData.result; // Can be a dict like { 'current': x, 'total': y } or a percentage, or other info
      const taskError = taskData.error; // Specific error message from the task

      let displayStatus = status;
      let progressValue = null;
      let errorMessage = taskError || '';

      if (status === 'PROGRESS') {
        if (result && typeof result === 'object' && result.hasOwnProperty('current') && result.hasOwnProperty('total') && result.total > 0) {
          progressValue = (result.current / result.total) * 100;
          displayStatus = `Computing: ${result.current}/${result.total}`;
        } else if (typeof result === 'number' && result >= 0 && result <= 100) { // If result is a percentage
          progressValue = result;
          displayStatus = `Computing: ${result.toFixed(0)}%`;
        } else if (typeof result === 'string') { // If result is a descriptive string
            displayStatus = `Computing: ${result}`;
        } else {
          displayStatus = 'Computing...'; // Generic progress
        }
      } else if (status === 'SUCCESS') {
        displayStatus = 'Done';
        if (result && typeof result === 'string') {
            displayStatus = `Done: ${result}`; // e.g., "Done: 1500 features computed."
        } else if (result && result.message) {
            displayStatus = `Done: ${result.message}`;
        }
      } else if (status === 'FAILURE') {
        displayStatus = 'Error';
        // errorMessage is already set from taskData.error
        if (!errorMessage && result && typeof result === 'string') {
            errorMessage = result; // Use result as error message if taskData.error is empty
        } else if (!errorMessage && result && result.error) {
            errorMessage = result.error;
        }
        if (!errorMessage) errorMessage = 'Task failed with no specific details.';
      }

      updateTaskState(category, {
        status: displayStatus,
        progress: progressValue,
        error: errorMessage,
      });

      // Stop polling if task is completed or failed
      if (status === 'SUCCESS' || status === 'FAILURE') {
        if (intervalRefs.current[category]) {
          clearInterval(intervalRefs.current[category]);
          intervalRefs.current[category] = null;
        }
      }
    } catch (error) { // This catch is for network errors or issues with getTaskStatus itself
      console.error(`Error fetching status for task ${taskId} (${category}):`, error);
      const apiErrorMessage = error.response?.data?.error || error.response?.data?.detail || error.message;
      updateTaskState(category, {
        status: 'Error',
        error: `Failed to fetch task status: ${apiErrorMessage}`,
      });
      if (intervalRefs.current[category]) {
        clearInterval(intervalRefs.current[category]);
        intervalRefs.current[category] = null;
      }
    }
  };

  // Function to handle button click
  const handleComputeClick = async (category) => {
    setLoading(prevLoading => ({ ...prevLoading, [category]: true }));
    updateTaskState(category, { status: 'Starting...', error: '', progress: null }); // Reset state

    try {
      const response = await computeFeatures(category);
      const taskData = response.data; // { task_id, status, result, error }

      if (taskData?.task_id) {
        // Asynchronous task started, begin polling
        startPolling(category, taskData.task_id);
      } else if (taskData?.status === 'SUCCESS') {
        // Synchronous task completed successfully
        updateTaskState(category, { status: 'Done', error: '', progress: 100 });
        // Optionally show a success message via snackbar if needed
        // showSnackbar(`Feature computation for ${category} completed successfully (synchronous).`, 'success');
      }
       else {
        // Handle cases where task_id is null and status is not SUCCESS (e.g., backend error before task dispatch)
        throw new Error(taskData?.error || "No task ID received and task did not complete synchronously.");
      }
    } catch (error) {
      console.error(`Error starting computation for ${category}:`, error);
      updateTaskState(category, {
          status: 'Error',
          error: `Failed to start task: ${error.response?.data?.detail || error.message}`,
          taskId: null,
      });
    } finally {
      setLoading(prevLoading => ({ ...prevLoading, [category]: false }));
    }
  };

  // Clear intervals on component unmount
  useEffect(() => {
    return () => {
      Object.values(intervalRefs.current).forEach(clearInterval);
    };
  }, []);

  const getStatusColor = (status) => {
    if (status === 'Done') return 'success';
    if (status === 'Error') return 'error';
    if (status === 'Idle') return 'default';
    return 'info'; // Pending, Starting, Computing
  };


  return (
    <Box sx={{ mt: 2 }}>
      <Typography variant="h6" gutterBottom>Trigger Feature Computation</Typography>
      <Typography variant="body2" color="textSecondary" sx={{ mb: 3 }}>
        Recompute feature vectors for all images within a specific category. This may take some time depending on the number of images and models.
      </Typography>

      <Grid container spacing={3}>
        {IP_CATEGORIES.map((category) => (
          // Remove 'item' prop, use responsive props directly
          <Grid xs={12} md={4} key={category}>
            <Paper elevation={2} sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="h6" sx={{ textTransform: 'capitalize', mb: 1 }}>
                {category}
              </Typography>
              <Box sx={{ mb: 2, height: 60 }}> {/* Fixed height for status/error */}
                {tasks[category].error ? (
                  <Alert severity="error" sx={{ textAlign: 'left', fontSize: '0.8rem' }}>{tasks[category].error}</Alert>
                ) : (
                  <>
                    <Chip
                      label={tasks[category].status}
                      color={getStatusColor(tasks[category].status)}
                      sx={{ mb: 1 }}
                    />
                    {tasks[category].status.startsWith('Computing') && tasks[category].progress !== null && (
                       <LinearProgress variant="determinate" value={tasks[category].progress} sx={{ mt: 1 }}/>
                    )}
                     {tasks[category].status.startsWith('Computing') && tasks[category].progress === null && (
                       <LinearProgress sx={{ mt: 1 }}/> // Indeterminate if no progress value
                    )}
                  </>
                )}
              </Box>
              <Button
                variant="contained"
                startIcon={loading[category] ? <CircularProgress size={20} color="inherit" /> : <PlayArrowIcon />}
                onClick={() => handleComputeClick(category)}
                disabled={loading[category] || tasks[category].status.startsWith('Computing') || tasks[category].status === 'Pending' || tasks[category].status === 'Starting...'}
                fullWidth
              >
                Recompute {category.charAt(0).toUpperCase() + category.slice(1)} Features
              </Button>
            </Paper>
          </Grid>
        ))}
      </Grid>
       {/* Optional: Section to list images with missing features - requires backend support */}
       {/* <Box sx={{ mt: 4 }}>
         <Typography variant="h6">Images Missing Features</Typography>
         <Typography>List of images missing features could go here if backend endpoint exists.</Typography>
       </Box> */}
    </Box>
  );
};

export default FeatureComputation;