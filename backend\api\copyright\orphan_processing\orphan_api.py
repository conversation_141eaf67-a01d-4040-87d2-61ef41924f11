"""
Orphan Processing API Endpoints

This module contains the API endpoints for orphan processing operations.
It uses the refactored OrphanDetector and OrphanFixer classes.
"""

from flask import jsonify, current_app
import traceback

from .orphan_detector import OrphanDetector
from .orphan_fixer import OrphanFixer
from .orphan_types import OrphanCategory, FixAction

# Global instances - will be created on first use
_detector = None
_fixer = None

def _get_detector():
    """Get or create detector instance."""
    global _detector
    if _detector is None:
        _detector = OrphanDetector()
    return _detector

def _get_fixer():
    """Get or create fixer instance."""
    global _fixer
    if _fixer is None:
        _fixer = OrphanFixer()
    return _fixer


def get_orphan_copyrights(category, page=1, per_page=50):
    """
    Get orphan copyrights based on the specified category with pagination.

    Args:
        category (str): The orphan category to fetch ('court-only', 'db-only', 'prod-no-qdrant', 'qdrant-only', 'missing-cos', 'duplicate-reg-no')
        page (int): Page number (default: 1)
        per_page (int): Items per page (default: 50, max: 200)

    Returns:
        JSON response with orphan data, pagination info, and error message
    """
    try:
        # Validate category
        if category not in OrphanCategory.VALID_CATEGORIES:
            return jsonify({
                "success": False,
                "error": f"Invalid category '{category}'. Must be one of: {', '.join(OrphanCategory.VALID_CATEGORIES)}"
            }), 400

        # Get detector instance and call the appropriate method
        detector = _get_detector()
        if category == OrphanCategory.COURT_ONLY:
            orphans, pagination_info = detector.get_court_only_orphans(page, per_page)
        elif category == OrphanCategory.DB_ONLY:
            orphans, pagination_info = detector.get_db_only_orphans(page, per_page)
        elif category == OrphanCategory.PROD_NO_QDRANT:
            orphans, pagination_info = detector.get_prod_no_qdrant_orphans(page, per_page)
        elif category == OrphanCategory.QDRANT_ONLY:
            orphans, pagination_info = detector.get_qdrant_only_orphans(page, per_page)
        elif category == OrphanCategory.MISSING_COS:
            orphans, pagination_info = detector.get_missing_cos_orphans(page, per_page)
        elif category == OrphanCategory.DUPLICATE_REG_NO:
            orphans, pagination_info = detector.get_duplicate_reg_no_orphans(page, per_page)
        else:
            orphans = []
            pagination_info = {'total_count': 0}

        return jsonify({
            "success": True,
            "data": orphans,
            "category": category,
            "pagination": {
                "page": page,
                "per_page": per_page,
                **pagination_info
            }
        }), 200

    except Exception as e:
        current_app.logger.error(f"Error fetching orphan copyrights for category '{category}': {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            "success": False,
            "error": f"An error occurred while fetching orphan copyrights: {str(e)}"
        }), 500


def fix_copyright_asset(category, action, ids):
    """
    Fix copyright assets based on category and action.

    Args:
        category (str): The orphan category ('court-only', 'db-only', 'prod-no-qdrant', 'qdrant-only', 'missing-cos', 'duplicate-reg-no')
        action (str): Action to perform (delete, delete_qdrant, add_prod_true, etc.)
        ids (list): List of asset IDs to process

    Returns:
        JSON response with success status and results
    """
    try:
        # Validate category
        if category not in OrphanCategory.VALID_CATEGORIES:
            return jsonify({
                "success": False,
                "error": f"Invalid category '{category}'. Must be one of: {', '.join(OrphanCategory.VALID_CATEGORIES)}"
            }), 400

        # Validate action based on category
        valid_actions = ['']  # Default action for all categories
        if category == OrphanCategory.QDRANT_ONLY:
            valid_actions.extend(FixAction.VALID_ACTIONS.get(OrphanCategory.QDRANT_ONLY,[]))

        if action not in valid_actions:
            return jsonify({
                "success": False,
                "error": f"Invalid action '{action}' for category '{category}'. Valid actions: {', '.join(valid_actions)}"
            }), 400

        # Validate ids
        if not ids or not isinstance(ids, list):
            return jsonify({
                "success": False,
                "error": "ids must be a non-empty list"
            }), 400

        deleted_count = 0
        errors = []

        # Get fixer instance and call the appropriate method
        fixer = _get_fixer()
        if category == OrphanCategory.COURT_ONLY:
            deleted_count, errors = fixer.fix_court_only_assets(action, ids)
        elif category == OrphanCategory.DB_ONLY:
            deleted_count, errors = fixer.fix_db_only_assets(action, ids)
        elif category == OrphanCategory.PROD_NO_QDRANT:
            deleted_count, errors = fixer.fix_prod_no_qdrant_assets(action, ids)
        elif category == OrphanCategory.QDRANT_ONLY:
            payload, errors = fixer.fix_qdrant_only_assets(action, ids)
        elif category == OrphanCategory.MISSING_COS:
            deleted_count, errors = fixer.fix_missing_cos_assets(action, ids)
        elif category == OrphanCategory.DUPLICATE_REG_NO:
            deleted_count, errors = fixer.fix_duplicate_reg_no_assets(action, ids)
        else:
            errors.append(f"Unknown category '{category}'")

        # Prepare response
        response = {
            "success": True,
            "category": category,
            "payload": payload,
            "action": action,
            "total_requested": len(ids),
            "errors": errors
        }
        return jsonify(response), 200

    except Exception as e:
        current_app.logger.error(f"Error in delete_copyright_asset for category '{category}': {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            "success": False,
            "error": f"An error occurred while deleting copyright assets: {str(e)}"
        }), 500
