import React from 'react';
import { Card, CardContent, Typography, Box, Tooltip } from '@mui/material';
import Plot from 'react-plotly.js';

// TDD: TEST: PieChartDisplay renders title
// TDD: TEST: PieChartDisplay renders Plotly chart with correct labels and values from data.segments
// TDD: TEST: PieChartDisplay handles empty or null data gracefully
const PieChartDisplay = ({ title, data }) => {
  if (!data || !data.segments || data.segments.length === 0) {
    return (
      <Card sx={{ width: '100%', minHeight: '500px', display: 'flex', justifyContent: 'center' }}>
        <CardContent sx={{ width: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center' }}>
          <Typography variant="h6" gutterBottom>{title}</Typography>
          <Box display="flex" justifyContent="center" alignItems="center" height="300px">
            <Typography variant="body2" color="textSecondary">
              No data available for {title.toLowerCase()}.
            </Typography>
          </Box>
        </CardContent>
      </Card>
    );
  }

  // Check if this is LOC data with definitions
  const hasDefinitions = data.segments.some(segment =>
    segment.name && typeof segment.name === 'object' && segment.name.code && segment.name.definition
  );

  // Prepare labels and values based on data format
  let plotlyLabels, plotlyValues, hoverTexts, hoverLabels;

  if (hasDefinitions) {
    // For LOC data with definitions
    plotlyLabels = data.segments.map(segment => segment.name.code);
    plotlyValues = data.segments.map(segment => segment.count);
    // Create hover text that includes both code and definition
    hoverTexts = data.segments.map(segment =>
      `${segment.name.code}: ${segment.name.definition}`
    );
  } else {
    // For regular data
    plotlyLabels = data.segments.map(segment => segment.name);
    plotlyValues = data.segments.map(segment => segment.count);
    // Create hover text that includes the full name for hover
    hoverLabels = plotlyLabels;
  }

  const plotDataConfig = [{
    labels: plotlyLabels,
    values: plotlyValues,
    type: 'pie',
    hole: 0.4,
    textinfo: "percent", // Only show percentage in the pie slices
    hoverinfo: hasDefinitions ? "text+percent" : "label+percent",
    hovertext: hasDefinitions ? hoverTexts : undefined,
    insidetextorientation: "radial",
    automargin: true,
  }];

  const plotLayoutConfig = {
    title: {
      text: title,
      font: {
        size: 16,
      },
      x: 0.5,
      xanchor: 'center',
    },
    height: 400, // Adjusted height
    width: 800, // Set a fixed width
    autosize: true, // Allow autosize
    margin: { t: 50, b: 100, l: 20, r: 20 }, // Reduced left/right margins
    legend: {
      orientation: 'h', // Horizontal legend
      yanchor: 'bottom',
      y: -0.2, // Adjusted legend position
      xanchor: 'center',
      x: 0.5
    },
  };

  return (
    <Card sx={{ width: '100%', minHeight: '500px', display: 'flex', justifyContent: 'center' }}>
      <CardContent sx={{ width: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
        {/* Title is now part of the plotLayoutConfig */}
        <Box sx={{ width: '100%', display: 'flex', justifyContent: 'center' }}>
          <Plot
            data={plotDataConfig}
            layout={plotLayoutConfig}
            useResizeHandler={true}
            style={{ width: '100%', maxWidth: '900px' }}
            config={{ responsive: true, displaylogo: false }}
          />
        </Box>
      </CardContent>
    </Card>
  );
};

export default PieChartDisplay;