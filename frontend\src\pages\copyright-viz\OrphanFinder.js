import React, { useState, useEffect, useCallback } from 'react';
import { fixOrphans } from '../../services/api_copyright_viz';
import CourtOnlyPanel from '../../components/copyright-viz/CourtOnlyPanel';
import DbOnlyPanel from '../../components/copyright-viz/DbOnlyPanel';
import ProdNoQdrantPanel from '../../components/copyright-viz/ProdNoQdrantPanel';
import QdrantOnlyPanel from '../../components/copyright-viz/QdrantOnlyPanel';
import MissingCosPanel from '../../components/copyright-viz/MissingCosPanel';
import DuplicateRegNoPanel from '../../components/copyright-viz/DuplicateRegNoPanel';
import './OrphanFinder.css';

const ORPHAN_CATEGORIES = [
    { id: 'court-only', name: 'Court-Only' },
    { id: 'db-only', name: 'DB-Only' },
    { id: 'prod-no-qdrant', name: 'Prod w/o Qdrant' },
    { id: 'qdrant-only', name: 'Qdrant-Only' },
    { id: 'missing-cos', name: 'Missing COS' },
    { id: 'duplicate-reg-no', name: 'Duplicate Reg-No' },
];

// Panel mapping for easy lookup
const PANEL_COMPONENTS = {
    'court-only': CourtOnlyPanel,
    'db-only': DbOnlyPanel,
    'prod-no-qdrant': ProdNoQdrantPanel,
    'qdrant-only': QdrantOnlyPanel,
    'missing-cos': MissingCosPanel,
    'duplicate-reg-no': DuplicateRegNoPanel,
};

const OrphanFinder = () => {
    const [activeTab, setActiveTab] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [refreshKey, setRefreshKey] = useState(0);

    const handleFixOrphans = async (category, action, ids) => {
        if (ids.length === 0) return;
        setLoading(true);
        setError(null);
        try {
            const result = await fixOrphans({ category, action, ids });
            return result;  // Return the API response
        } catch (err) {
            setError(err.message);
            throw err;
        } finally {
            setLoading(false);
        }
    };



    return (
        <div className="orphan-finder-container">
            <div className="orphan-header">
                <h1>Orphan Finder</h1>
                <p>Find and resolve orphaned assets across the system.</p>
            </div>
            {error && <p className="error-message">{error}</p>}
            {loading && <p>Processing fix...</p>}
            <div className="orphan-tabs">
                {ORPHAN_CATEGORIES.map(cat => (
                    <button
                        key={cat.id}
                        className={`tab-button ${activeTab?.id === cat.id ? 'active' : ''}`}
                        onClick={() => setActiveTab(cat)}
                    >
                        {cat.name}
                    </button>
                ))}
            </div>
            <div className="orphan-content">
                {activeTab ? (
                    (() => {
                        const ActivePanelComponent = PANEL_COMPONENTS[activeTab.id];
                        return (
                            <ActivePanelComponent
                                key={`${activeTab.id}-${refreshKey}`}
                                category={activeTab}
                                onFix={handleFixOrphans}
                                onRefresh={() => setRefreshKey(k => k + 1)}
                            />
                        );
                    })()
                ) : (
                    <div className="no-active-tab">
                        <div className="no-tab-message">
                            <h3>Welcome to Orphan Finder</h3>
                            <p>Please select a category from the tabs above to view and manage orphaned assets.</p>
                            <div className="tab-suggestions">
                                <p>Available categories:</p>
                                <ul>
                                    <li><strong>Court-Only:</strong> Items that exist in court records but not in the database</li>
                                    <li><strong>DB-Only:</strong> Items that exist in database but not in court records</li>
                                    <li><strong>Prod w/o Qdrant:</strong> Production items missing from Qdrant vector database</li>
                                    <li><strong>Qdrant-Only:</strong> Items that exist in Qdrant but not in the database</li>
                                    <li><strong>Missing COS:</strong> Items missing from Tencent COS storage</li>
                                    <li><strong>Duplicate Reg-No:</strong> Items with duplicate registration numbers</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default OrphanFinder;
