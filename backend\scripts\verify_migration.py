import sys
import os
sys.path.append(os.getcwd())

import logging
from sqlalchemy import inspect
from backend import create_app
from backend.extensions import db

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def verify_tables_exist():
    """
    Connects to the database and verifies the existence of copyright-related tables.
    """
    expected_tables = [
        'copyright_duplicate_exclusions',
        'copyright_types',
        'copyrights',
        'copyrights_viz',
        'copyright_asset_types',
    ]
    
    logger.info("Starting table verification process...")
    
    flask_app = create_app()
    with flask_app.app_context():
        logger.info("Application context created.")
        try:
            inspector = inspect(db.engine)
            existing_tables = inspector.get_table_names()
            logger.info(f"Found tables: {existing_tables}")

            missing_tables = [table for table in expected_tables if table not in existing_tables]

            if not missing_tables:
                logger.info("SUCCESS: All expected copyright tables were found in the database.")
                return True
            else:
                logger.error(f"FAILURE: The following expected tables are missing: {missing_tables}")
                return False
        except Exception as e:
            logger.error(f"An error occurred while inspecting the database: {e}", exc_info=True)
            return False

if __name__ == "__main__":
    verify_tables_exist()