import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import PatentDashboardPage from './patent-viz/PatentDashboardPage'; // Import the actual component

// TDD: TEST: PatentDashboardPage component is correctly imported and rendered (Now importing actual)

// TDD: TEST: PatentExplorerPage component is correctly imported and rendered
import PatentExplorerPage from './patent-viz/PatentExplorerPage'; // Import the actual component
import ImageSearcherPage from './patent-viz/ImageSearcherPage'; // Import the new Image Searcher Page
const NotFoundPage = () => <div>Platform Page Not Found</div>; // Or a more generic 404

// TDD: TEST: PatentPlatform renders DashboardPage when path is /patent-viz/dashboard
// TDD: TEST: PatentPlatform renders PatentExplorerPage when path is /patent-viz/explorer
// TDD: TEST: PatentPlatform redirects from /patent-viz to /patent-viz/dashboard by default

const PatentPlatformPage = () => {
  return (
    <Routes>
      {/* Default route for /patent-viz, redirects to dashboard */}
      <Route path="/" element={<Navigate to="dashboard" replace />} />

      <Route path="dashboard" element={<PatentDashboardPage />} />
      <Route path="explorer" element={<PatentExplorerPage />} />
      <Route path="image-search" element={<ImageSearcherPage />} />
      {/* 
        Potentially other routes like explorer/:patentId if not handled within PatentExplorerPage
        e.g. <Route path="explorer/:patentId" element={<PatentDetailPage />} /> 
      */}
      
      <Route path="*" element={<NotFoundPage />} />
    </Routes>
  );
};

export default PatentPlatformPage;