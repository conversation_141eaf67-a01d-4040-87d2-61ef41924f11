"""
Image processing operations for copyright assets
"""
import io
from PIL import Image
from flask import request, jsonify, current_app, Response
from sqlalchemy import text
import requests
from datetime import datetime, timezone
from PIL import Image
import uuid

import base64
import pandas as pd
from io import BytesIO
from backend.api.copyright.core_operations import _get_copyrights_files_for_plaintiffs
from backend.api.copyright.move_cn_to_copyright import save_image_ssd_and_cos
from backend.api.copyright.handle_prod import process_copyright_production
from backend.database.copyright_model import Copyrights
from backend.database.copyrights_file_model import CopyrightsFiles
from backend.extensions import db
from backend.utils.Tencent_COS import get_cos_client
from .helpers import generate_md_registration_number, get_certificate_path, get_high_res_path, get_latest_case_for_plaintiff, get_low_res_path

def proxy_image():
    """
    Proxies an image from an external URL (like Tencent COS) to avoid CORS issues
    in the frontend, especially for canvas operations like cropping.
    """
    url = request.args.get('url')
    if not url:
        return jsonify({"success": False, "error": "No URL provided"}), 400
    
    try:
        # Fetch the image from the external URL
        response = requests.get(url, timeout=30)
        if response.status_code == 200:
            # Return the image with appropriate headers
            return Response(
                response.content,
                mimetype=response.headers.get('Content-Type', 'image/jpeg'),
                headers={
                    'Cache-Control': 'public, max-age=3600',
                    'Access-Control-Allow-Origin': '*'
                }
            )
        else:
            current_app.logger.warning(f"Failed to fetch image from {url}: HTTP {response.status_code}")
            return jsonify({"success": False, "error": f"Failed to fetch image: HTTP {response.status_code}"}), response.status_code
            
    except requests.RequestException as e:
        current_app.logger.error(f"Error fetching image from {url}: {e}")
        return jsonify({"success": False, "error": "Could not fetch image from source"}), 502

def serialize_copyright_file(file_record, plaintiff_id, copyright_row):    
    return {
        "id": str(file_record.id),
        "filename": file_record.filename,
        "reg_no": file_record.registration_number,
        "method": file_record.method,
        "production": file_record.production,
        "type": file_record.type,
        "plaintiff_id": plaintiff_id,
        "high_res_path": get_high_res_path(plaintiff_id,file_record.filename,file_record.update_time.isoformat()),
        "low_res_path": get_low_res_path(plaintiff_id,file_record.filename, file_record.update_time.isoformat()),
        "certificate_path": get_certificate_path(str(plaintiff_id),file_record.filename),
        "certificate_status": copyright_row.certificate_status,
        "update_time": file_record.update_time.isoformat(),
        "create_time": file_record.create_time.isoformat(),
    }

def split_copyright_file(asset_id):
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "Invalid JSON payload"}), 400

        parent_asset = CopyrightsFiles.query.filter_by(id=asset_id).one()
        copyright_row = Copyrights.query.filter_by(registration_number=parent_asset.registration_number).one()

        # Load case data and find the latest case for the plaintiff
        latest_case = get_latest_case_for_plaintiff(copyright_row.plaintiff_id)

        if latest_case is None:
            return jsonify({"error": "No case found for the given plaintiff"}), 404

        crops = data.get('crops')
        if not isinstance(crops, list) or not crops:
            return jsonify({"error": "Payload must contain a non-empty 'crops' list"}), 400

        new_db_records = []
        processed_for_response = []
        new_db_records = []
        assets_added_response = []
        assets_updated_response = []
        for crop in crops:
            base64_data = crop.get('image_data')
            reg_id = crop.get('registration_id')
            action = crop.get('action')
            if not base64_data or (not reg_id and not action):
                continue
            new_copyright_record = None
            is_production = False
            if action == 'GENERATE':
                reg_id = generate_md_registration_number(copyright_row.plaintiff_id)
                new_copyright_record = Copyrights(
                    id=uuid.uuid4(),
                    plaintiff_id=copyright_row.plaintiff_id,
                    registration_number=reg_id,
                    tro=copyright_row.tro,
                    create_time=datetime.now(timezone.utc),
                    update_time=datetime.now(timezone.utc),
                    deleted=False,
                )
                db.session.add(new_copyright_record)
                # Need to commit get next update MD Reg no in loop
                db.session.commit()

            try:
                header, encoded = base64_data.split(",", 1)
                original_image_data = base64.b64decode(encoded)

                image = Image.open(BytesIO(original_image_data))

                webp_buffer = BytesIO()
                image.save(webp_buffer, format="WEBP")
                webp_image_data = webp_buffer.getvalue()

            except Exception as e:
                continue

            db_count = CopyrightsFiles.query.filter_by(registration_number=reg_id).count()
            new_count_in_batch = sum(1 for record in new_db_records if record.registration_number == reg_id)
            total_existing_count = db_count + new_count_in_batch
            if total_existing_count > 0:
                # If there are existing files, append length to filename to show multiple versions
                filename = f"{reg_id}_{parent_asset.method}_{str(total_existing_count)}.webp"
            else:
                filename = f"{reg_id}_{parent_asset.method}.webp"
            
            if action != 'REPLACE':

                new_file_record = CopyrightsFiles(
                    id=uuid.uuid4(),
                    filename=filename,
                    registration_number=reg_id,
                    method=parent_asset.method,
                    production=False,
                    type='',
                    create_time=datetime.now(timezone.utc),
                    update_time=datetime.now(timezone.utc)
                )
                new_db_records.append(new_file_record)

                list(save_image_ssd_and_cos(
                    image_data=webp_image_data,
                    filename=filename,
                    plaintiff_id=copyright_row.plaintiff_id,
                    case_id=latest_case['id']
                ))
                processed_for_response.append({
                    "id": str(new_file_record.id),
                    "filename": new_file_record.filename,
                    "registration_number": new_file_record.registration_number
                })
            else:
                if parent_asset.production:
                    # Set production to False before replacing
                    success, message, _ = process_copyright_production(
                        parent_asset.id, parent_asset.registration_number, False, parent_asset.filename
                    )
                list(save_image_ssd_and_cos(
                    image_data=webp_image_data,
                    filename=parent_asset.filename,
                    plaintiff_id=copyright_row.plaintiff_id,
                    case_id=latest_case['id']
                ))
                if parent_asset.production:
                    # Set production to True if asset was previously production
                    success, message, _ = process_copyright_production(
                        parent_asset.id, parent_asset.registration_number, True, parent_asset.filename
                    )
                parent_asset.update_time = datetime.now(timezone.utc)
                db.session.commit()
                updated_asset_data = serialize_copyright_file(parent_asset,copyright_row.plaintiff_id,copyright_row)
                assets_updated_response.append(updated_asset_data)
        try:
            db.session.add_all(new_db_records)
            db.session.commit()
        except Exception as e:
            db.session.rollback() # Important: Rollback on failure
            current_app.logger.error(f"{e}")
            return jsonify({"error": "Failed to save records to the database."}), 500

        for record in new_db_records:
            asset_data = serialize_copyright_file(record,copyright_row.plaintiff_id,copyright_row)
            assets_added_response.append(asset_data)

        return jsonify({
            "message": f"Successfully processed {len(crops)} crops.",
            "assets_added": assets_added_response,
            "assets_updated": assets_updated_response
        }), 201 
    except Exception as e:
        return jsonify({"error": "An internal server error occurred"}), 500