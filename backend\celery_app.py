# backend/celery_app.py
import os
import platform # Import platform module
from celery import Celery
from dotenv import load_dotenv
from flask import Flask # Import Flask

load_dotenv() # Load environment variables from .env file

# Determine OS and select appropriate Redis URL
if os.name == 'nt':
    # For local Windows execution, use 127.0.0.1 to avoid potential eventlet DNS issues
    celery_broker_url = os.getenv('CELERY_BROKER_URL_W11', 'redis://127.0.0.1:6380/0') # Use 127.0.0.1 and port 6380
    celery_result_backend = os.getenv('CELERY_RESULT_BACKEND_W11', 'redis://127.0.0.1:6380/1') # Use 127.0.0.1 and port 6380
    # print(f"Windows OS detected. Using Redis URLs: Broker='{celery_broker_url}', Backend='{celery_result_backend}'")
else:
    # For Docker/Linux, use the standard variables (which likely use the service name 'redis')
    celery_broker_url = os.getenv('CELERY_BROKER_URL')
    celery_result_backend = os.getenv('CELERY_RESULT_BACKEND')
    # print(f"Non-Windows OS detected. Using Redis URLs: Broker='{celery_broker_url}', Backend='{celery_result_backend}'")


# Ensure required environment variables are set after selection
if not celery_broker_url:
    raise ValueError("Celery Broker URL could not be determined. Set CELERY_BROKER_URL or CELERY_BROKER_URL_W11.")
if not celery_result_backend:
    raise ValueError("Celery Result Backend URL could not be determined. Set CELERY_RESULT_BACKEND or CELERY_RESULT_BACKEND_W11.")

# Define the Celery application instance
celery = Celery(
    __name__.split('.')[0], # Use 'backend' as the main name
    broker=celery_broker_url, # Set broker directly
    backend=celery_result_backend, # Set backend directly
    include=['backend.tasks.feature_computation', 'backend.tasks.comparison_execution', 'backend.tasks.combined_scores']
)

# Apply other configurations
celery.conf.update(
    result_expires=3600,
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    broker_connection_retry_on_startup=True
)

# --- Flask App Context for Standalone Celery Worker ---
# Create a minimal Flask app instance here solely for Celery task context
# This ensures the worker uses the ContextTask correctly when run standalone.
def create_flask_app_for_celery():
    app = Flask(__name__.split('.')[0]) # Use 'backend' name
    # Load necessary config for tasks, especially DB access
    # Construct the database URI using individual environment variables for the Celery worker
    db_user = os.getenv('POSTGRES_USER')
    db_password = os.getenv('POSTGRES_PASSWORD')
    db_host = os.getenv('POSTGRES_HOST')
    db_port = os.getenv('POSTGRES_PORT')
    db_name = os.getenv('POSTGRES_DB_MODEL_TEST_WORKBENCH') # Use the specific DB name for the main app

    if not all([db_user, db_password, db_host, db_port, db_name]):
        # This error is specifically for the Celery worker context
        raise ValueError("One or more required PostgreSQL environment variables (POSTGRES_USER, POSTGRES_PASSWORD, POSTGRES_HOST, POSTGRES_PORT, POSTGRES_DB_MODEL_TEST_WORKBENCH) are not set for the Celery worker.")

    app.config['SQLALCHEMY_DATABASE_URI'] = f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['SQLALCHEMY_POOL_RECYCLE'] = 280  # Recycle connections older than 280 seconds
    app.config['SQLALCHEMY_POOL_PRE_PING'] = True # Check connection validity before use
    # Add other essential configs if your ContextTask relies on them.
    return app

flask_app = create_flask_app_for_celery()

# Initialize extensions for the worker's app context
from .extensions import db
db.init_app(flask_app)

# --- Define ContextTask using the minimal Flask app ---
class ContextTask(celery.Task):
    abstract = True
    def __call__(self, *args, **kwargs):
        # Use the locally created flask_app for context
        with flask_app.app_context():
            # DB is initialized above, should be available within context
            return self.run(*args, **kwargs)

celery.Task = ContextTask # Set the custom Task class globally for the worker

# The command to run the worker uses the 'celery' instance defined earlier:
# python -m celery -A backend.celery_app:celery worker --loglevel=info -P solo