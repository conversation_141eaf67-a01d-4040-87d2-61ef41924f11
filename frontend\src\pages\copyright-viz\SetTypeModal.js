import React, { useState } from 'react';
import './SetTypeModal.css';

const SetTypeModal = ({ types, onConfirm, onClose }) => {
    const [selectedTypeIds, setSelectedTypeIds] = useState(new Set());

    const handleTypeChange = (e) => {
        const { value, checked } = e.target;
        const newSelectedTypeIds = new Set(selectedTypeIds);
        if (checked) {
            newSelectedTypeIds.add(value);
        } else {
            newSelectedTypeIds.delete(value);
        }
        setSelectedTypeIds(newSelectedTypeIds);
    };

    const handleConfirm = () => {
        onConfirm(Array.from(selectedTypeIds));
    };

    return (
        <div className="modal-overlay" onClick={onClose}>
            <div className="modal-content" onClick={(e) => e.stopPropagation()}>
                <div className="modal-header">
                    <h3>Set Copyright Type</h3>
                </div>
                <div className="modal-body">
                    <p>Select one or more types to apply to the selected assets.</p>
                    <div className="checkbox-group">
                        {types.map(type => (
                            <div key={type.id}>
                                <input type="checkbox" id={`modal-type-${type.id}`} value={type.id} onChange={handleTypeChange} />
                                <label htmlFor={`modal-type-${type.id}`}>{type.name}</label>
                            </div>
                        ))}
                    </div>
                </div>
                <div className="modal-footer">
                    <button onClick={onClose} className="btn secondary">Cancel</button>
                    <button onClick={handleConfirm} className="btn primary">Confirm</button>
                </div>
            </div>
        </div>
    );
};

export default SetTypeModal;