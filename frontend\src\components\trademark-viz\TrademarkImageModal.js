import React from 'react';
import {
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    Button,
    Box,
    Card,
    CardMedia,
} from '@mui/material';

function TrademarkImageModal({
    is_open,
    on_close,
    image_data,
    trademark_text,
}) {
    return (
        <Dialog open={is_open} onClose={on_close} fullWidth maxWidth="md" scroll="paper">
            <DialogTitle sx={{ borderBottom: '1px solid #ccc' }}>
                Image for: {trademark_text || 'Trademark'}
            </DialogTitle>
            <DialogContent dividers>
                {image_data?.image_path ? (
                    <Box sx={{ p: 2, display: 'flex', justifyContent: 'center' }}>
                        <Card raised sx={{ maxWidth: '100%' }}>
                            <CardMedia
                                component="img"
                                image={image_data.image_path}
                                alt={`Image for ${trademark_text}`}
                                sx={{
                                    objectFit: 'contain',
                                    maxHeight: '70vh',
                                }}
                            />
                        </Card>
                    </Box>
                ) : (
                    <Box sx={{ p: 2, textAlign: 'center' }}>
                        No image available.
                    </Box>
                )}
            </DialogContent>
            <DialogActions sx={{ borderTop: '1px solid #ccc', p: '16px 24px' }}>
                <Button onClick={on_close} variant="outlined">
                    Close
                </Button>
            </DialogActions>
        </Dialog>
    );
}

export default TrademarkImageModal;