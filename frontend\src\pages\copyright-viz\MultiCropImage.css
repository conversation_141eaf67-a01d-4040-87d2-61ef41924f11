/* Styling for the container of each cropped preview item */
.crop-preview-item {
    display: flex;
    align-items: center;
    gap: 15px;
    border: 1px solid #e0e0e0;
    padding: 10px;
    border-radius: 8px;
    position: relative;
    background-color: #f9f9f9;
}

.crop-preview-image {
    max-width: 120px;
    max-height: 120px;
    object-fit: contain;
    border-radius: 4px;
    border: 1px solid #ccc;
}

.crop-details {
    display: flex;
    flex-direction: column;
    gap: 5px;
    flex-grow: 1;
    /* Allows the details section to take available space */
}

.crop-details label {
    font-size: 0.9rem;
    font-weight: 500;
    color: #333;
}

/* 
  The custom dropdown magic happens here! 
*/

/* 1. The Wrapper */
.custom-select-wrapper {
    position: relative;
    display: inline-block;
    width: 100%;
    max-width: 400px;
    /* Adjust as needed */
}

/* 2. Hide the default arrow and style the select box */
.custom-select-wrapper select {
    /* Reset and base styles */
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    border: 1px solid #ccc;
    border-radius: 6px;
    padding: 10px 30px 10px 12px;
    /* Right padding for the new arrow */
    background-color: white;
    cursor: pointer;
    font-size: 1rem;
    width: 100%;
    transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

/* Truncate long text inside the select box */
.custom-select-wrapper select {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

/* Style for when the dropdown is focused */
.custom-select-wrapper select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* Style for disabled state */
.custom-select-wrapper select:disabled {
    background-color: #f0f0f0;
    cursor: not-allowed;
    opacity: 0.7;
}

/* 3. The Custom Arrow */
.custom-select-wrapper::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 12px;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 6px solid #555;
    pointer-events: none;
    /* Let clicks pass through to the select element */
    transition: transform 0.2s ease;
}

/* Optional: Animate arrow on focus */
.custom-select-wrapper select:focus+ ::after {
    transform: translateY(-50%) rotate(180deg);
}

/* Remove Crop Button Styling */
.remove-crop-button {
    position: absolute;
    top: -10px;
    right: -10px;
    background: #e74c3c;
    color: white;
    border: 2px solid white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    line-height: 1;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    transition: background-color 0.2s, transform 0.2s;
}

.remove-crop-button:hover {
    background-color: #c0392b;
    transform: scale(1.1);
}

/* Add to your MultiCropImage.css file */

.toggle-options {
    display: flex;
    gap: 15px;
    margin-top: 10px;
    align-items: center;
}

.toggle-options label {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.9em;
    cursor: pointer;
}