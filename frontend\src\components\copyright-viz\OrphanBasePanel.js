import React, { useState, useEffect, useCallback } from 'react';

// Shared Base Panel Component
const OrphanBasePanel = ({
    category,
    onFix,
    onRefresh,
    CardComponent,
    selected,
    onSelectionChange,
    onBulkAction,
    loading,
    error,
    autoRefreshAfterAction = false
}) => {
    const [items, setItems] = useState([]);
    const [localSelected, setLocalSelected] = useState(new Set());
    const [currentPage, setCurrentPage] = useState(1);
    const [pagination, setPagination] = useState(null);
    const [perPage, setPerPage] = useState(25);
    const [fullscreenImage, setFullscreenImage] = useState(null);
    const [imageLoading, setImageLoading] = useState(false);
    const [pageInputValue, setPageInputValue] = useState(currentPage.toString());
    const [similarImageModal, setSimilarImageModal] = useState(null);
    const [similarImageLoading, setSimilarImageLoading] = useState(false);
    const [processingItems, setProcessingItems] = useState(new Set());
    const [failedItems, setFailedItems] = useState(new Map());
    const [actionFeedback, setActionFeedback] = useState(null);

    const fetchData = useCallback(async (page = currentPage) => {
        // Clear selection when changing pages
        if (onSelectionChange) {
            onSelectionChange(new Set());
        } else {
            setLocalSelected(new Set());
        }
        try {
            const { getOrphans } = await import('../../services/api_copyright_viz');
            const result = await getOrphans(category.id, page, perPage);
            setItems(result.data || []);
            setPagination(result.pagination || null);
            setCurrentPage(page);
        } catch (err) {
            setItems([]);
            setPagination(null);
        }
    }, [category, currentPage, perPage, onSelectionChange]);

    useEffect(() => {
        fetchData();
    }, [fetchData]);

    const currentSelected = selected || localSelected;
    const setCurrentSelected = onSelectionChange || setLocalSelected;

    const handleSelectAll = (e) => {
        if (e.target.checked) {
            setCurrentSelected(new Set(items.map(item => item.id)));
        } else {
            setCurrentSelected(new Set());
        }
    };

    const handleSelectOne = (id) => {
        const newSelected = new Set(currentSelected);
        if (newSelected.has(id)) {
            newSelected.delete(id);
        } else {
            newSelected.add(id);
        }
        setCurrentSelected(newSelected);
    };

    const handleFix = async () => {
        await onFix(category.id, 'delete', Array.from(currentSelected));
        setCurrentSelected(new Set());
        if (autoRefreshAfterAction) {
            onRefresh(); // This will trigger a re-fetch in the parent
        }
    };

    const handleClearSelection = () => {
        setCurrentSelected(new Set());
    };

    const handleCustomAction = async (action) => {
        if (currentSelected.size === 0) return;

        // Set processing state for selected items
        setProcessingItems(new Set(currentSelected));
        setFailedItems(new Map());
        setActionFeedback({ type: 'processing', message: `Processing ${currentSelected.size} item(s)...` });

        // Track if this is a plaintiff_id action for special handling
        let isPlaintiffIdAction = false;

        if (onBulkAction) {
            try {
                await onBulkAction(action);
                setProcessingItems(new Set());
                setActionFeedback({ type: 'success', message: `Successfully processed ${currentSelected.size} item(s)` });
                setCurrentSelected(new Set());
                if (autoRefreshAfterAction) {
                    onRefresh();
                }
            } catch (error) {
                setProcessingItems(new Set());
                setActionFeedback({ type: 'error', message: error.message });
            }
        } else {
            // For qdrant-only category, include similar point data in the IDs list
            let idsToProcess;
            if (category.id === 'qdrant-only' && action === 'add_prod_true_remove_similar') {
                idsToProcess = Array.from(currentSelected).map(selectedId => {
                    const item = items.find(item => item.id === selectedId);
                    if (item && item.data && item.data.similar_point) {
                        return {
                            id: selectedId,
                            similar_point: item.data.similar_point
                        };
                    } else {
                        return {
                            id: selectedId,
                            similar_point: null
                        };
                    }
                });
            } else if (action === 'set_db_plaintiff_id' || action === 'set_qdrant_plaintiff_id') {
                // For plaintiff_id actions, send the complete item data
                idsToProcess = Array.from(currentSelected).map(selectedId => {
                    const item = items.find(item => item.id === selectedId);
                    return item; // Send the complete item object
                });
                // Store that this is a plaintiff_id action for later handling
                isPlaintiffIdAction = true;
            }
            else {
                // For other categories, use the original format
                idsToProcess = Array.from(currentSelected);
            }

            try {
                console.log('About to call onFix with:', { categoryId: category.id, action, idsToProcess });
                const result = await onFix(category.id, action, idsToProcess);
                console.log('Raw API response:', result);

                // Clear processing state
                setProcessingItems(new Set());

                // Determine which actions should remove cards
                const actionsThatRemoveCards = ['delete_qdrant', 'add_prod_true', 'add_prod_true_remove_similar'];
                const shouldRemoveCards = actionsThatRemoveCards.includes(action);

                // Note: plaintiff_id actions (set_db_plaintiff_id, set_qdrant_plaintiff_id) are not in actionsThatRemoveCards
                // so they will not remove cards, which is the correct behavior

                console.log('Action result:', { action, result, shouldRemoveCards, currentSelectedSize: currentSelected.size });

                // Check if we should remove cards based on successful response
                const shouldRemove = shouldRemoveCards && result && (result.success === true || result.success === undefined);

                if (shouldRemove) {
                    console.log('Removing cards after successful action:', action);
                    // Remove successfully processed items from the items list (assume all selected items were processed)
                    setItems(prevItems => {
                        const remainingItems = prevItems.filter(item => !currentSelected.has(item.id));
                        console.log('Items before removal:', prevItems.length, 'Items after removal:', remainingItems.length);
                        return remainingItems;
                    });

                    // Update pagination count (use selected count instead of deleted_count)
                    if (pagination) {
                        setPagination(prev => ({
                            ...prev,
                            total_count: prev.total_count - currentSelected.size
                        }));
                    }

                    setActionFeedback({
                        type: 'success',
                        message: `Successfully processed ${currentSelected.size} item(s)`
                    });
                } else {
                    console.log('Not removing cards:', { shouldRemoveCards, result });

                    // Special handling for plaintiff_id actions - update items with payload data
                    if (isPlaintiffIdAction && result && result.success === true && result.payload) {
                        console.log('Processing plaintiff_id action payload:', result.payload);

                        // Update items with the new payload data
                        setItems(prevItems => {
                            return prevItems.map(item => {
                                // Find matching item in payload
                                const updatedItem = result.payload.find(payloadItem =>
                                    payloadItem.id === item.id ||
                                    (payloadItem.data && item.data &&
                                        payloadItem.data.qdrant_id === item.data.qdrant_id)
                                );

                                if (updatedItem) {
                                    console.log('Updating item with payload data:', {
                                        itemId: item.id,
                                        updatedData: updatedItem.data
                                    });
                                    return {
                                        ...item,
                                        data: {
                                            ...item.data,
                                            ...updatedItem.data
                                        }
                                    };
                                }
                                return item;
                            });
                        });

                        setActionFeedback({
                            type: 'success',
                            message: `Successfully updated ${currentSelected.size} item(s) with plaintiff data`
                        });
                    } else if (result && result.success === true) {
                        setActionFeedback({
                            type: 'success',
                            message: `Action completed successfully`
                        });
                    } else {
                        setActionFeedback({
                            type: 'error',
                            message: result?.error || `Failed to process items`
                        });
                    }
                }

                setCurrentSelected(new Set());

                // Only refresh if autoRefreshAfterAction is true AND we didn't already update locally
                if (autoRefreshAfterAction && (!shouldRemove || !result || result.success !== true)) {
                    onRefresh();
                }

            } catch (error) {
                setProcessingItems(new Set());
                setActionFeedback({ type: 'error', message: error.message });

                // On error, keep items and show error state
                const errorMap = new Map();
                Array.from(currentSelected).forEach(id => {
                    errorMap.set(id, error.message);
                });
                setFailedItems(errorMap);
            }
        }
    };

    // Check if all selected items have similar_point data
    const allSelectedHaveSimilarPoints = () => {
        if (currentSelected.size === 0) return false;

        return Array.from(currentSelected).every(selectedId => {
            const item = items.find(item => item.id === selectedId);
            return item && item.data && item.data.similar_point;
        });
    };

    const allSelectedHaveDbDetails = () => {
        if (currentSelected.size === 0) return false;

        return Array.from(currentSelected).every(selectedId => {
            const item = items.find(item => item.id === selectedId);
            return item && item.data && item.data.db_plaintiff_name && item.data.db_plaintiff_id;
        });
    };

    const handlePageChange = (newPage) => {
        if (newPage >= 1 && newPage <= (pagination?.total_pages || 1)) {
            fetchData(newPage);
        }
    };

    const handlePerPageChange = (newPerPage) => {
        setPerPage(newPerPage);
        setCurrentPage(1); // Reset to first page when changing per page
        fetchData(1, newPerPage);
    };

    const handleImageClick = (imageData) => {
        const highResUrl = imageData.high_res_path;
        if (highResUrl) {
            const proxiedUrl = `/api/v1/copyright/image-proxy?url=${encodeURIComponent(highResUrl)}`;
            setImageLoading(true);
            setFullscreenImage({
                high_res: proxiedUrl,
                filename: imageData.filename
            });
        } else {
            console.error('No high resolution path available');
        }
    };

    const handleSimilarImageClick = (similarImageData) => {
        const highResUrl = similarImageData.high_res_path;
        if (highResUrl) {
            const proxiedUrl = `/api/v1/copyright/image-proxy?url=${encodeURIComponent(highResUrl)}`;
            setSimilarImageLoading(true);
            setSimilarImageModal({
                high_res: proxiedUrl,
                filename: similarImageData.filename,
                plaintiff_id: similarImageData.plaintiff_id,
                plaintiff_name: similarImageData.plaintiff_name,
                registration_number: similarImageData.registration_number
            });
        } else {
            console.error('No high resolution path available for similar image');
        }
    };

    if (loading) return <div className="loading-spinner"></div>;

    // Determine if this is the qdrant-only category to show custom actions
    const isQdrantOnly = category.id === 'qdrant-only';

    return (
        <div className="orphan-panel">
            {error && <p className="error-message">{error}</p>}

            {/* Selection Controls */}
            <div className="orphan-selection-controls" style={{
                display: 'flex',
                gap: '10px',
                alignItems: 'center',
                marginBottom: '15px',
                padding: '10px',
                backgroundColor: '#f8f9fa',
                borderRadius: '4px',
                border: '1px solid #dee2e6'
            }}>
                <div className="select-all-container" style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <input
                        type="checkbox"
                        id="select-all"
                        onChange={handleSelectAll}
                        checked={items.length > 0 && currentSelected.size === items.length}
                        disabled={items.length === 0}
                        style={{ margin: 0 }}
                    />
                    <label htmlFor="select-all" style={{
                        margin: 0,
                        fontSize: '14px',
                        color: '#495057',
                        cursor: items.length === 0 ? 'not-allowed' : 'pointer'
                    }}>
                        Select All ({items.length})
                    </label>
                </div>
                <button
                    onClick={handleClearSelection}
                    disabled={currentSelected.size === 0}
                    style={{
                        padding: '6px 12px',
                        fontSize: '14px',
                        border: '1px solid #ced4da',
                        borderRadius: '4px',
                        backgroundColor: currentSelected.size === 0 ? '#e9ecef' : '#ffffff',
                        color: currentSelected.size === 0 ? '#6c757d' : '#495057',
                        cursor: currentSelected.size === 0 ? 'not-allowed' : 'pointer',
                        transition: 'all 0.15s ease-in-out'
                    }}
                    onMouseOver={(e) => {
                        if (currentSelected.size > 0) {
                            e.target.style.backgroundColor = '#e9ecef';
                            e.target.style.borderColor = '#adb5bd';
                        }
                    }}
                    onMouseOut={(e) => {
                        if (currentSelected.size > 0) {
                            e.target.style.backgroundColor = '#ffffff';
                            e.target.style.borderColor = '#ced4da';
                        }
                    }}
                >
                    Clear Selection ({currentSelected.size})
                </button>
            </div>

            {/* Action Feedback */}
            {actionFeedback && (
                <div className={`action-feedback ${actionFeedback.type}`} style={{
                    padding: '10px',
                    margin: '10px 0',
                    borderRadius: '4px',
                    border: '1px solid',
                    backgroundColor: actionFeedback.type === 'success' ? '#d4edda' : actionFeedback.type === 'error' ? '#f8d7da' : '#fff3cd',
                    borderColor: actionFeedback.type === 'success' ? '#c3e6cb' : actionFeedback.type === 'error' ? '#f5c6cb' : '#ffeaa7',
                    color: actionFeedback.type === 'success' ? '#155724' : actionFeedback.type === 'error' ? '#721c24' : '#856404'
                }}>
                    {actionFeedback.type === 'processing' && <span>⏳ </span>}
                    {actionFeedback.type === 'success' && <span>✅ </span>}
                    {actionFeedback.type === 'error' && <span>❌ </span>}
                    {actionFeedback.message}
                    {actionFeedback.type !== 'processing' && (
                        <button
                            onClick={() => setActionFeedback(null)}
                            style={{
                                float: 'right',
                                background: 'none',
                                border: 'none',
                                fontSize: '16px',
                                cursor: 'pointer',
                                color: 'inherit'
                            }}
                        >
                            ×
                        </button>
                    )}
                </div>
            )}

            {/* Action Buttons */}
            <div className="orphan-actions">
                {isQdrantOnly ? (
                    <>
                        <button
                            onClick={() => handleCustomAction('delete_qdrant')}
                            disabled={currentSelected.size === 0 || processingItems.size > 0}
                        >
                            Delete from QDrant ({currentSelected.size})
                            {processingItems.size > 0 && <span style={{ marginLeft: '5px' }}>⏳</span>}
                        </button>
                        <button
                            onClick={() => handleCustomAction('add_prod_true')}
                            disabled={currentSelected.size === 0 || processingItems.size > 0}
                        >
                            Add w/ Prod True ({currentSelected.size})
                            {processingItems.size > 0 && <span style={{ marginLeft: '5px' }}>⏳</span>}
                        </button>
                        <button
                            onClick={() => handleCustomAction('add_prod_true_remove_similar')}
                            disabled={currentSelected.size === 0 || !allSelectedHaveSimilarPoints() || processingItems.size > 0}
                            title={currentSelected.size > 0 && !allSelectedHaveSimilarPoints() ? 'All selected items must have similar images' : ''}
                        >
                            Add w/ Prod True Remove similar Image ({currentSelected.size})
                            {processingItems.size > 0 && <span style={{ marginLeft: '5px' }}>⏳</span>}
                        </button>
                        <button
                            onClick={() => handleCustomAction('set_db_plaintiff_id')}
                            disabled={currentSelected.size === 0 || !allSelectedHaveDbDetails() || processingItems.size > 0}
                            title={currentSelected.size > 0 && !allSelectedHaveDbDetails() ? 'All selected items must have DB Plaintiff details' : ''}
                        >
                            Set DB plaintiff_id to Qdrant ({currentSelected.size})
                            {processingItems.size > 0 && <span style={{ marginLeft: '5px' }}>⏳</span>}
                        </button>
                        <button
                            onClick={() => handleCustomAction('set_qdrant_plaintiff_id')}
                            disabled={currentSelected.size === 0 || processingItems.size > 0}
                        >
                            Set Qdrant plaintiff_id to DB ({currentSelected.size})
                            {processingItems.size > 0 && <span style={{ marginLeft: '5px' }}>⏳</span>}
                        </button>
                    </>
                ) : (
                    <button
                        onClick={handleFix}
                        disabled={currentSelected.size === 0 || processingItems.size > 0}
                    >
                        Delete Selected ({currentSelected.size})
                        {processingItems.size > 0 && <span style={{ marginLeft: '5px' }}>⏳</span>}
                    </button>
                )}
            </div>

            {/* Fullscreen Image Modal */}
            {fullscreenImage && (
                <div className="fullscreen-modal" onClick={() => setFullscreenImage(null)}>
                    <div className="fullscreen-content" onClick={(e) => e.stopPropagation()}>
                        {imageLoading && (
                            <div className="image-loading">
                                <div className="loading-spinner"></div>
                                <p>Loading high-resolution image...</p>
                            </div>
                        )}
                        <img
                            src={fullscreenImage.high_res}
                            alt={fullscreenImage.filename}
                            className="fullscreen-image"
                            onLoad={() => setImageLoading(false)}
                            onError={() => setImageLoading(false)}
                            style={{ display: imageLoading ? 'none' : 'block' }}
                        />
                        <button
                            className="close-modal"
                            onClick={() => setFullscreenImage(null)}
                        >
                            ✕
                        </button>
                    </div>
                </div>
            )}

            {/* Similar Image Modal */}
            {similarImageModal && (
                <div className="similar-image-modal" onClick={() => setSimilarImageModal(null)}>
                    <div className="similar-image-content" onClick={(e) => e.stopPropagation()}>
                        <div className="similar-image-header">
                            <h3>Similar Image Details</h3>
                            <button
                                className="close-modal"
                                onClick={() => setSimilarImageModal(null)}
                            >
                                ✕
                            </button>
                        </div>
                        <div className="similar-image-body">
                            {similarImageLoading && (
                                <div className="image-loading">
                                    <div className="loading-spinner"></div>
                                    <p>Loading similar image...</p>
                                </div>
                            )}
                            <img
                                src={similarImageModal.high_res}
                                alt={similarImageModal.filename}
                                className="similar-image-display"
                                onLoad={() => setSimilarImageLoading(false)}
                                onError={() => setSimilarImageLoading(false)}
                                style={{ display: similarImageLoading ? 'none' : 'block' }}
                            />
                            <div className="similar-image-info">
                                <div className="info-row">
                                    <label>Filename:</label>
                                    <span>{similarImageModal.filename || 'N/A'}</span>
                                </div>
                                <div className="info-row">
                                    <label>Registration:</label>
                                    <span>{similarImageModal.registration_number || 'N/A'}</span>
                                </div>
                                <div className="info-row">
                                    <label>Plaintiff ID:</label>
                                    <span>{similarImageModal.plaintiff_id || 'N/A'}</span>
                                </div>
                                <div className="info-row">
                                    <label>Plaintiff Name:</label>
                                    <span>{similarImageModal.plaintiff_name || 'N/A'}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Items Grid */}
            <div className="orphan-items-grid">
                {items.length > 0 ? items.map(item => (
                    <CardComponent
                        key={item.id}
                        item={item}
                        isSelected={currentSelected.has(item.id)}
                        onToggleSelect={handleSelectOne}
                        onImageClick={handleImageClick}
                        onSimilarImageClick={isQdrantOnly ? handleSimilarImageClick : undefined}
                        isProcessing={processingItems.has(item.id)}
                        error={failedItems.get(item.id)}
                    />
                )) : (
                    <div className="no-orphans">
                        <p>No orphans found in this category.</p>
                    </div>
                )}
            </div>

            {/* Pagination Controls */}
            {pagination && pagination.total_pages > 1 && (
                <div className="orphan-pagination">
                    <div className="pagination-info">
                        <span>
                            Showing {items.length} items (Page {pagination.page} of {pagination.total_pages}, Total: {pagination.total_count})
                        </span>
                    </div>

                    <div className="pagination-controls">
                        <button
                            onClick={() => handlePageChange(currentPage - 1)}
                            disabled={!pagination.has_prev}
                            className="pagination-btn"
                        >
                            Previous
                        </button>

                        <div className="page-input-container">
                            <label>Go to page:</label>
                            <input
                                type="number"
                                min="1"
                                max={pagination.total_pages}
                                value={pageInputValue}
                                onChange={(e) => setPageInputValue(e.target.value)}
                                onKeyDown={(e) => {
                                    if (e.key === 'Enter') {
                                        const page = parseInt(pageInputValue);
                                        if (page >= 1 && page <= pagination.total_pages) {
                                            handlePageChange(page);
                                        }
                                    }
                                }}
                                className="page-input"
                            />
                            <button
                                onClick={() => {
                                    const page = parseInt(pageInputValue);
                                    if (page >= 1 && page <= pagination.total_pages) {
                                        handlePageChange(page);
                                    }
                                }}
                                className="go-btn"
                            >
                                Go
                            </button>
                        </div>

                        <button
                            onClick={() => handlePageChange(currentPage + 1)}
                            disabled={!pagination.has_next}
                            className="pagination-btn"
                        >
                            Next
                        </button>
                    </div>

                    <div className="per-page-selector">
                        <label>Items per page:</label>
                        <select
                            value={perPage}
                            onChange={(e) => handlePerPageChange(Number(e.target.value))}
                            className="per-page-select"
                        >
                            <option value={10}>10</option>
                            <option value={25}>25</option>
                            <option value={50}>50</option>
                            <option value={100}>100</option>
                            <option value={200}>200</option>
                        </select>
                    </div>
                </div>
            )}

            {/* Show pagination info even for single page */}
            {pagination && (
                <div className="orphan-count-info">
                    <small>
                        Category: {category.name} | Total items: {pagination.total_count} | Page {pagination.page} of {pagination.total_pages}
                    </small>
                </div>
            )}
        </div>
    );
};

export default OrphanBasePanel;
