/* Qdrant<PERSON>n<PERSON> Card Styling */
.qdrant-only-card {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 4px;
    margin: 4px;
    background: white;
    transition: all 0.2s ease;
}

.qdrant-only-card:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.qdrant-only-card.mismatch-highlight {
    background: #fffde7;
    /* Very light yellow background */
    border-color: #fdd835;
    /* Light yellow border */
    box-shadow: 0 2px 8px rgba(253, 216, 53, 0.15);
}

.qdrant-only-card .card-image-container {
    margin-bottom: 4px;
}

.qdrant-only-card .card-thumbnail {
    max-width: 100%;
    height: 120px;
    object-fit: cover;
    border-radius: 4px;
    cursor: pointer;
}

.qdrant-only-card .no-image {
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f5f5f5;
    border-radius: 4px;
    color: #999;
}

.qdrant-only-card .card-info {
    font-size: 14px;
}

.qdrant-only-card .info-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 6px;
    padding: 2px 0;
}

.qdrant-only-card .info-row.mismatch-field {
    background: rgba(253, 216, 53, 0.2);
    border-radius: 3px;
    padding: 4px;
    margin: 2px 0;
}

.qdrant-only-card .info-row label {
    font-weight: 600;
    color: #666;
    min-width: 120px;
}

.qdrant-only-card .info-row span {
    color: #333;
    flex: 1;
    text-align: right;
}

.qdrant-only-card .info-section {
    border-top: 1px solid #eee;
    padding-top: 8px;
    margin-top: 8px;
}

.qdrant-only-card .info-section h4 {
    margin: 0 0 8px 0;
    font-size: 14px;
    color: #555;
    font-weight: 600;
}

/* Selected state */
.qdrant-only-card.selected {
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.qdrant-only-card.selected.mismatch-highlight {
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.4), 0 2px 8px rgba(0, 123, 255, 0.2);
    background: #f8f9ff;
}

/* Similar Image Link */
.similar-image-link-container {
    position: absolute;
    top: 8px;
    right: 8px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 4px;
    padding: 4px 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.similar-image-link {
    color: #007bff;
    text-decoration: none;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    border: none;
    background: none;
    padding: 0;
    transition: color 0.2s ease;
}

.similar-image-link:hover {
    color: #0056b3;
    text-decoration: underline;
}

.qdrant-only-card .card-image-container {
    position: relative;
}

/* Similar Image Modal */
.similar-image-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.similar-image-content {
    background: white;
    border-radius: 8px;
    max-width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.similar-image-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #eee;
}

.similar-image-header h3 {
    margin: 0;
    color: #333;
    font-size: 18px;
    font-weight: 600;
}

.similar-image-body {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.similar-image-display {
    max-width: 100%;
    max-height: 400px;
    object-fit: contain;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.similar-image-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.similar-image-info .info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 4px;
}

.similar-image-info .info-row label {
    font-weight: 600;
    color: #495057;
    font-size: 14px;
}

.similar-image-info .info-row span {
    color: #212529;
    font-size: 14px;
    text-align: right;
}