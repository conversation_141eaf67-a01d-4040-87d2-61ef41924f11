import React from 'react';
import { render, screen } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import BoundingBoxPictureManagementPage from '../PictureManagementPage';

// Mock the API service used by PictureManagementPage
jest.mock('../../services/api_bounding_box', () => ({
  getBbPictures: jest.fn(() => Promise.resolve({ pictures: [], totalPages: 0, totalPictures: 0 })),
  uploadBbPicture: jest.fn(() => Promise.resolve({})),
  updateBbPictureName: jest.fn(() => Promise.resolve({})),
  deleteBbPicture: jest.fn(() => Promise.resolve({})),
}));

describe('BoundingBoxPictureManagementPage', () => {
  test('renders the page title', () => {
    render(
      <MemoryRouter>
        <BoundingBoxPictureManagementPage />
      </MemoryRouter>
    );
    expect(screen.getByText(/Picture Management \(Bounding Box\)/i)).toBeInTheDocument();
  });

  test('renders Upload New Picture section title', () => {
    render(
      <MemoryRouter>
        <BoundingBoxPictureManagementPage />
      </MemoryRouter>
    );
    expect(screen.getByText(/Upload New Picture/i)).toBeInTheDocument();
  });

  test('renders Image Browser section title', () => {
    render(
      <MemoryRouter>
        <BoundingBoxPictureManagementPage />
      </MemoryRouter>
    );
    expect(screen.getByText(/Image Browser/i)).toBeInTheDocument();
  });

  test('renders search by filename field', () => {
    render(
      <MemoryRouter>
        <BoundingBoxPictureManagementPage />
      </MemoryRouter>
    );
    expect(screen.getByLabelText(/Search by filename/i)).toBeInTheDocument();
  });
});
