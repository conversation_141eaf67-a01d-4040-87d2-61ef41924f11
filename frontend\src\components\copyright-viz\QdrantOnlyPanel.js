import React, { useState } from 'react';
import OrphanBasePanel from './OrphanBasePanel';
import { QdrantOnlyCard } from './cards';

const QdrantOnlyPanel = ({ category, onFix, onRefresh }) => {
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [selected, setSelected] = useState(new Set());

    return (
        <OrphanBasePanel
            category={category}
            onFix={onFix}
            onRefresh={onRefresh}
            CardComponent={QdrantOnlyCard}
            selected={selected}
            onSelectionChange={setSelected}
            loading={loading}
            error={error}
            autoRefreshAfterAction={false} // Disable automatic refresh for qdrant-only
        />
    );
};

export default QdrantOnlyPanel;
