import torch
from transformers import AutoModel, AutoProcessor
from PIL import Image

# load the model and processor
ckpt = "google/siglip2-giant-opt-patch16-384"
model = AutoModel.from_pretrained(ckpt).eval().to("cpu")
processor = AutoProcessor.from_pretrained(ckpt)

# load the image
image_path = "/home/<USER>/Desktop/localModelTestData/ModelTestsWorkbenchData/pictures/copyright/product/67_A_<PERSON><PERSON>.jpg"
image = Image.open(image_path)

# process the image and run inference
inputs = processor(images=[image], return_tensors="pt").to("cpu")
with torch.no_grad():
    image_embeddings = model.get_image_features(**inputs)

# print the dimension of the embedding
print(f"Embedding shape: {image_embeddings.shape}")