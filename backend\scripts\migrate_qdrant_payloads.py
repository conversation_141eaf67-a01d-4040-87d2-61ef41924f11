# backend/scripts/migrate_qdrant_payloads.py
import os
import sys
sys.path.append(os.getcwd())
import logging
import uuid
from sqlalchemy import create_engine, select
from sqlalchemy.orm import sessionmaker
from dotenv import load_dotenv

from qdrant_client import QdrantClient, models as qdrant_models

# Assuming your models and utility functions are accessible via these paths
# Adjust imports based on your project structure if necessary
from backend.database.models import ModelTestsImage # For fetching image_type
from backend.utils.vector_store import get_qdrant_client # For Qdrant client
from backend.utils.retry_utils import execute_with_retry, QDRANT_RETRYABLE_EXCEPTIONS, DB_RETRYABLE_EXCEPTIONS

# Load environment variables from .env file
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# --- Configuration ---
DATABASE_URL = os.environ.get("DATABASE_URL")
QDRANT_BATCH_SIZE = int(os.environ.get("QDRANT_MIGRATION_BATCH_SIZE", 1000)) # Batch size for Qdrant updates

def get_db_session():
    """Creates and returns a new SQLAlchemy session."""
    if not DATABASE_URL:
        logger.error("DATABASE_URL environment variable is not set.")
        raise ValueError("DATABASE_URL environment variable is not set.")
    try:
        engine = create_engine(DATABASE_URL)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        logger.info("Database session configured.")
        return SessionLocal()
    except Exception as e:
        logger.error(f"Failed to create database session: {e}", exc_info=True)
        raise

def fetch_image_type_lookup(db_session):
    """Fetches all image_id and image_type from ModelTestsImage for quick lookup."""
    logger.info("Fetching image_id to image_type lookup from database...")
    try:
        stmt = select(ModelTestsImage.image_id, ModelTestsImage.image_type)
        results = execute_with_retry(lambda: db_session.execute(stmt).all(), DB_RETRYABLE_EXCEPTIONS, session=db_session, operation_name="fetch all image types")
        
        lookup = {str(row.image_id): row.image_type for row in results if row.image_id and row.image_type}
        logger.info(f"Successfully fetched {len(lookup)} image_id-image_type pairs.")
        return lookup
    except Exception as e:
        logger.error(f"Error fetching image_type lookup: {e}", exc_info=True)
        db_session.rollback() # Rollback in case of error during fetch
        raise
    finally:
        db_session.close() # Ensure session is closed after use if created here

def migrate_qdrant_collections():
    """
    Main migration function.
    1. Ensures payload indexes for 'image_id' and 'image_type' on all collections.
    2. Updates payloads of existing points to include 'image_type' from the database.
    """
    qdrant_cli = get_qdrant_client()
    if not qdrant_cli:
        logger.error("Failed to get Qdrant client. Aborting migration.")
        return

    db_session_for_lookup = get_db_session() # Separate session for the initial lookup
    image_id_to_type_lookup = fetch_image_type_lookup(db_session_for_lookup)
    db_session_for_lookup.close() # Close it as it's no longer needed

    if not image_id_to_type_lookup:
        logger.warning("Image ID to Image Type lookup is empty. Payload updates might not be effective.")
        # Decide if to proceed or abort. For now, proceed to at least create indexes.

    try:
        collections_response = execute_with_retry(
            lambda: qdrant_cli.get_collections(),
            QDRANT_RETRYABLE_EXCEPTIONS,
            operation_name="list all collections"
        )
        if not collections_response or not collections_response.collections:
            logger.info("No Qdrant collections found to migrate.")
            return

        collection_names = [col.name for col in collections_response.collections]
        logger.info(f"Found {len(collection_names)} collections: {collection_names}")

        for collection_name in collection_names:
            logger.info(f"\n--- Processing collection: {collection_name} ---")

            # Step 1: Ensure payload indexes exist
            logger.info(f"Ensuring payload indexes for 'image_id' and 'image_type' in '{collection_name}'...")
            try:
                # Index for image_id
                execute_with_retry(
                    lambda: qdrant_cli.create_payload_index(
                        collection_name=collection_name,
                        field_name="image_id",
                        field_schema=qdrant_models.PayloadSchemaType.KEYWORD,
                        wait=True
                    ),
                    QDRANT_RETRYABLE_EXCEPTIONS,
                    operation_name=f"create index for 'image_id' on '{collection_name}'"
                )
                logger.info(f"Payload index for 'image_id' ensured for '{collection_name}'.")

                # Index for image_type
                execute_with_retry(
                    lambda: qdrant_cli.create_payload_index(
                        collection_name=collection_name,
                        field_name="image_type",
                        field_schema=qdrant_models.PayloadSchemaType.KEYWORD,
                        wait=True
                    ),
                    QDRANT_RETRYABLE_EXCEPTIONS,
                    operation_name=f"create index for 'image_type' on '{collection_name}'"
                )
                logger.info(f"Payload index for 'image_type' ensured for '{collection_name}'.")
            except Exception as idx_e:
                logger.error(f"Error creating payload indexes for '{collection_name}': {idx_e}", exc_info=True)
                # Continue to next collection or abort? For now, log and continue.

            # Step 2: Update payloads for existing points
            logger.info(f"Updating point payloads in '{collection_name}' to include 'image_type'...")
            points_to_update_in_qdrant = []
            processed_points_count = 0
            updated_points_count = 0
            offset = None  # For scrolling

            while True: # Loop for scrolling through all points
                try:
                    scrolled_points, new_offset = execute_with_retry(
                        lambda: qdrant_cli.scroll(
                            collection_name=collection_name,
                            limit=QDRANT_BATCH_SIZE, # Use a reasonable limit for scrolling
                            offset=offset,
                            with_payload=True,
                            with_vectors=False # We don't need vectors to update payload
                        ),
                        QDRANT_RETRYABLE_EXCEPTIONS,
                        operation_name=f"scroll points in '{collection_name}'"
                    )
                except Exception as scroll_e:
                    logger.error(f"Error scrolling points in '{collection_name}': {scroll_e}. Skipping payload update for this collection.", exc_info=True)
                    break # Break from while loop for this collection

                if not scrolled_points:
                    logger.debug(f"No more points to scroll in '{collection_name}'.")
                    break # No more points
                
                for point in scrolled_points:
                    processed_points_count += 1
                    qdrant_point_id_str = str(point.id) # Qdrant ID is already string or int, ensure string for lookup
                    current_payload = point.payload if point.payload is not None else {}
                    
                    # Determine the correct image_type from our DB lookup
                    image_type_from_db = image_id_to_type_lookup.get(qdrant_point_id_str)

                    # Check if update is needed
                    needs_update = False
                    if image_type_from_db:
                        if 'image_type' not in current_payload or current_payload.get('image_type') != image_type_from_db:
                            needs_update = True
                    elif 'image_type' in current_payload: 
                        # image_type in payload but not in DB? Potentially remove or log. For now, we only add/update.
                        logger.debug(f"Point {qdrant_point_id_str} in '{collection_name}' has image_type '{current_payload.get('image_type')}' but no corresponding DB entry. Skipping update for this field.")
                    
                    # Also ensure 'image_id' field is in payload (it should be if point ID is UUID string)
                    if 'image_id' not in current_payload or current_payload.get('image_id') != qdrant_point_id_str:
                        needs_update = True


                    if needs_update:
                        new_payload_for_point = current_payload.copy()
                        if image_type_from_db:
                            new_payload_for_point['image_type'] = image_type_from_db
                        new_payload_for_point['image_id'] = qdrant_point_id_str # Ensure image_id is in payload

                        points_to_update_in_qdrant.append(
                            qdrant_models.SetPayloadOperation(
                                set_payload=qdrant_models.SetPayload(
                                    payload=new_payload_for_point,
                                    points=[point.id] # Target specific point ID
                                )
                            )
                        )
                        updated_points_count += 1
                        logger.debug(f"Marked point {point.id} for payload update in '{collection_name}'. New payload: {new_payload_for_point}")

                try:
                    logger.info(f"Batch updating payloads for {len(points_to_update_in_qdrant)} points in '{collection_name}'...")
                    execute_with_retry(
                        lambda: qdrant_cli.batch_update_points(
                            collection_name=collection_name,
                            update_operations=points_to_update_in_qdrant,
                            wait=True
                        ),
                        QDRANT_RETRYABLE_EXCEPTIONS,
                        operation_name=f"batch set payloads in '{collection_name}'"
                    )
                    logger.info(f"Successfully batch updated payloads in '{collection_name}'.")
                    points_to_update_in_qdrant.clear()
                except Exception as upsert_e:
                    logger.error(f"Error batch upserting payloads to '{collection_name}': {upsert_e}", exc_info=True)
                    # Decide: clear and continue, or abort for this collection? For now, clear and continue.
                    points_to_update_in_qdrant.clear()
                
                offset = new_offset
                if not offset:
                    logger.debug(f"Reached end of points for '{collection_name}'.")
                    break # End of scrolling for this collection
                logger.info(f"Processed {processed_points_count} points so far in '{collection_name}'. Next offset: {offset}")

            logger.info(f"Finished payload updates for '{collection_name}'. Processed: {processed_points_count}, Updated: {updated_points_count}.")

        logger.info("\nMigration script completed.")

    except Exception as e:
        logger.error(f"An unexpected error occurred during migration: {e}", exc_info=True)
    finally:
        if qdrant_cli:
            try:
                qdrant_cli.close()
                logger.info("Qdrant client closed.")
            except Exception as e_close:
                logger.error(f"Error closing Qdrant client: {e_close}", exc_info=True)

if __name__ == "__main__":
    logger.info("Starting Qdrant payload migration script...")
    migrate_qdrant_collections()
    logger.info("Qdrant payload migration script finished.")