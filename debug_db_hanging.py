#!/usr/bin/env python3
"""
Debug script to diagnose the database hanging issue.
This script tests the specific INSERT operation that's causing problems.
"""

import os
import sys
import time
import threading
import signal
from contextlib import contextmanager

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from dotenv import load_dotenv
load_dotenv()

from sqlalchemy import text, create_engine
from sqlalchemy.exc import SQLAlchemyError
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s %(levelname)s %(name)s : %(message)s')
logger = logging.getLogger(__name__)

def timeout_handler(signum, frame):
    """Handle timeout signal"""
    raise TimeoutError("Operation timed out")

@contextmanager
def timeout_context(seconds):
    """Context manager for timeout handling"""
    def timeout_handler(signum, frame):
        raise TimeoutError(f"Operation timed out after {seconds} seconds")

    old_handler = signal.signal(signal.SIGALRM, timeout_handler)
    signal.alarm(seconds)
    try:
        yield
    finally:
        signal.signal(signal.SIGALRM, old_handler)
        signal.alarm(0)

def test_connection_health():
    """Test basic database connection and health"""
    print("=== Testing Database Connection Health ===")

    # Construct database URI from environment variables
    db_user = os.getenv('POSTGRES_USER')
    db_password = os.getenv('POSTGRES_PASSWORD')
    db_host = os.getenv('POSTGRES_HOST')
    db_port = os.getenv('POSTGRES_PORT')
    db_name = os.getenv('POSTGRES_DB_MAIDALV')

    if not all([db_user, db_password, db_host, db_port, db_name]):
        print("❌ Missing required environment variables for MAIDALV database")
        return False

    db_uri = f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"
    print(f"Connecting to: {db_host}:{db_port}/{db_name}")

    try:
        engine = create_engine(db_uri, echo=True)
        with engine.connect() as conn:
            print("✅ Connection established successfully")

            # Test basic query
            start_time = time.time()
            result = conn.execute(text("SELECT 1 as test"))
            row = result.fetchone()
            end_time = time.time()

            print(f"✅ Basic query executed successfully in {end_time - start_time:.3f}s")
            print(f"   Result: {row}")

            # Test table existence
            result = conn.execute(text("SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'copyrights')"))
            exists = result.fetchone()[0]
            print(f"✅ Copyrights table exists: {exists}")

            return True

    except Exception as e:
        print(f"❌ Connection test failed: {e}")
        return False

def test_insert_operation():
    """Test the specific INSERT operation that's hanging"""
    print("\n=== Testing INSERT Operation ===")

    # Construct database URI
    db_user = os.getenv('POSTGRES_USER')
    db_password = os.getenv('POSTGRES_PASSWORD')
    db_host = os.getenv('POSTGRES_HOST')
    db_port = os.getenv('POSTGRES_PORT')
    db_name = os.getenv('POSTGRES_DB_MAIDALV')

    db_uri = f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"

    try:
        engine = create_engine(db_uri, echo=True)

        # Test parameters (use test values)
        test_params = {
            "reg_no": "TEST_MD9999999999",
            "tro": True,
            "plaintiff_id": 999999,
            "certificate_status": "pending"
        }

        print(f"Testing INSERT with parameters: {test_params}")

        # Test with timeout
        try:
            with timeout_context(30):  # 30 second timeout
                start_time = time.time()
                with engine.connect() as conn:
                    with conn.begin():
                        result = conn.execute(text("""
                            INSERT INTO copyrights (registration_number, tro, plaintiff_id, certificate_status)
                            VALUES (:reg_no, :tro, :plaintiff_id, :certificate_status)
                        """), test_params)

                        print(f"✅ INSERT completed successfully in {time.time() - start_time:.3f}s")

                        # Clean up test data
                        conn.execute(text("DELETE FROM copyrights WHERE registration_number = :reg_no"), {"reg_no": test_params["reg_no"]})
                        print("✅ Test data cleaned up")

        except TimeoutError as e:
            print(f"❌ INSERT operation timed out: {e}")
            return False
        except Exception as e:
            print(f"❌ INSERT operation failed: {e}")
            return False

        return True

    except Exception as e:
        print(f"❌ INSERT test setup failed: {e}")
        return False

def test_transaction_isolation():
    """Test transaction isolation and potential blocking"""
    print("\n=== Testing Transaction Isolation ===")

    # Construct database URI
    db_user = os.getenv('POSTGRES_USER')
    db_password = os.getenv('POSTGRES_PASSWORD')
    db_host = os.getenv('POSTGRES_HOST')
    db_port = os.getenv('POSTGRES_PORT')
    db_name = os.getenv('POSTGRES_DB_MAIDALV')

    db_uri = f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"

    try:
        engine = create_engine(db_uri, echo=True)

        # Check for long-running transactions
        with engine.connect() as conn:
            print("Checking for long-running transactions...")

            # Query to find long-running transactions
            result = conn.execute(text("""
                SELECT
                    pid,
                    usename,
                    application_name,
                    client_addr,
                    state,
                    now() - xact_start as duration,
                    query
                FROM pg_stat_activity
                WHERE state = 'active'
                AND now() - xact_start > interval '30 seconds'
                ORDER BY duration DESC
            """))

            long_running = result.fetchall()
            if long_running:
                print(f"⚠️  Found {len(long_running)} long-running transactions:")
                for row in long_running:
                    print(f"   PID: {row[0]}, User: {row[1]}, Duration: {row[5]}, Query: {row[6][:100]}...")
            else:
                print("✅ No long-running transactions found")

            # Check for locks
            result = conn.execute(text("""
                SELECT
                    blocked_locks.pid as blocked_pid,
                    blocked_activity.usename as blocked_user,
                    blocking_locks.pid as blocking_pid,
                    blocking_activity.usename as blocking_user,
                    blocked_activity.query as blocked_query
                FROM pg_locks blocked_locks
                JOIN pg_stat_activity blocked_activity ON blocked_activity.pid = blocked_locks.pid
                JOIN pg_locks blocking_locks
                    ON blocking_locks.locktype = blocked_locks.locktype
                    AND blocking_locks.database IS NOT DISTINCT FROM blocked_locks.database
                    AND blocking_locks.relation IS NOT DISTINCT FROM blocked_locks.relation
                    AND blocking_locks.page IS NOT DISTINCT FROM blocked_locks.page
                    AND blocking_locks.tuple IS NOT DISTINCT FROM blocked_locks.tuple
                    AND blocking_locks.virtualxid IS NOT DISTINCT FROM blocked_locks.virtualxid
                    AND blocking_locks.transactionid IS NOT DISTINCT FROM blocked_locks.transactionid
                    AND blocking_locks.classid IS NOT DISTINCT FROM blocked_locks.classid
                    AND blocking_locks.objid IS NOT DISTINCT FROM blocked_locks.objid
                    AND blocking_locks.objsubid IS NOT DISTINCT FROM blocked_locks.objsubid
                    AND blocking_locks.pid != blocked_locks.pid
                JOIN pg_stat_activity blocking_activity ON blocking_activity.pid = blocking_locks.pid
                WHERE blocked_locks.granted = false
            """))

            locks = result.fetchall()
            if locks:
                print(f"⚠️  Found {len(locks)} blocked queries:")
                for row in locks:
                    print(f"   Blocked PID: {row[0]} ({row[1]})")
                    print(f"   Blocking PID: {row[2]} ({row[3]})")
                    print(f"   Blocked Query: {row[4][:100]}...")
                    print()
            else:
                print("✅ No blocking locks found")

        return True

    except Exception as e:
        print(f"❌ Transaction isolation test failed: {e}")
        return False

def test_table_constraints():
    """Test table constraints and indexes"""
    print("\n=== Testing Table Constraints and Indexes ===")

    # Construct database URI
    db_user = os.getenv('POSTGRES_USER')
    db_password = os.getenv('POSTGRES_PASSWORD')
    db_host = os.getenv('POSTGRES_HOST')
    db_port = os.getenv('POSTGRES_PORT')
    db_name = os.getenv('POSTGRES_DB_MAIDALV')

    db_uri = f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"

    try:
        engine = create_engine(db_uri, echo=False)

        with engine.connect() as conn:
            # Get table constraints
            result = conn.execute(text("""
                SELECT
                    tc.constraint_name,
                    tc.constraint_type,
                    tc.table_name,
                    kcu.column_name,
                    tc.is_deferrable,
                    tc.initially_deferred
                FROM information_schema.table_constraints AS tc
                JOIN information_schema.key_column_usage AS kcu
                    ON tc.constraint_name = kcu.constraint_name
                    AND tc.table_schema = kcu.table_schema
                WHERE tc.table_name = 'copyrights'
                ORDER BY tc.constraint_name, kcu.ordinal_position
            """))

            constraints = result.fetchall()
            if constraints:
                print("Table constraints:")
                for constraint in constraints:
                    print(f"   {constraint[0]} ({constraint[1]}): {constraint[3]}")
            else:
                print("No constraints found")

            # Get indexes
            result = conn.execute(text("""
                SELECT
                    indexname,
                    indexdef
                FROM pg_indexes
                WHERE tablename = 'copyrights'
                ORDER BY indexname
            """))

            indexes = result.fetchall()
            if indexes:
                print("\nTable indexes:")
                for index in indexes:
                    print(f"   {index[0]}: {index[1]}")
            else:
                print("\nNo indexes found")

        return True

    except Exception as e:
        print(f"❌ Table constraints test failed: {e}")
        return False

def main():
    """Main diagnostic function"""
    print("🔍 Database Hanging Issue Diagnostic Tool")
    print("=" * 50)

    # Run all diagnostic tests
    tests = [
        test_connection_health,
        test_transaction_isolation,
        test_table_constraints,
        test_insert_operation
    ]

    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            results.append(False)
        print()

    # Summary
    print("📊 Diagnostic Summary:")
    print("=" * 50)
    passed = sum(results)
    total = len(results)
    print(f"Tests passed: {passed}/{total}")

    if passed == total:
        print("✅ All tests passed - issue may be intermittent or environmental")
    else:
        print("❌ Some tests failed - investigate the failures above")

    print("\nRecommendations:")
    print("1. Check network connectivity to the PostgreSQL server")
    print("2. Monitor database performance and long-running queries")
    print("3. Consider adding statement timeouts to prevent hanging")
    print("4. Review database indexes and constraints")
    print("5. Check for database deadlocks and blocking queries")

if __name__ == "__main__":
    # Handle timeout gracefully on Windows (signal doesn't work)
    if os.name == 'nt':
        print("⚠️  Running on Windows - timeout handling may not work properly")
        main()
    else:
        main()