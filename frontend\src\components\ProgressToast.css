.progress-toast {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 350px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.progress-toast-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #eee;
  background: #f8f9fa;
  border-radius: 8px 8px 0 0;
}

.progress-toast-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.progress-toast-actions {
  display: flex;
  gap: 8px;
}

.toggle-details-btn,
.close-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  color: #666;
  font-size: 12px;
  line-height: 1;
}

.toggle-details-btn:hover,
.close-btn:hover {
  background: #e9ecef;
  color: #333;
}

.close-btn {
  font-size: 16px;
  font-weight: bold;
}

.progress-bar-container {
  padding: 16px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #007bff, #0056b3);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-bar-fill.upload-progress {
  background: linear-gradient(90deg, #17a2b8, #138496);
}

.progress-text {
  font-size: 12px;
  color: #666;
  text-align: center;
}

.progress-stats {
  display: flex;
  justify-content: space-between;
  padding: 0 16px 16px;
  font-size: 12px;
}

.success-count {
  color: #28a745;
  font-weight: 500;
}

.error-count {
  color: #dc3545;
  font-weight: 500;
}

.progress-details {
  border-top: 1px solid #eee;
  max-height: 200px;
  overflow-y: auto;
}

.progress-log {
  padding: 8px;
}

.log-entry {
  display: flex;
  gap: 8px;
  padding: 4px 8px;
  border-radius: 4px;
  margin-bottom: 2px;
  font-size: 11px;
}

.log-entry.success {
  background: #d4edda;
  color: #155724;
}

.log-entry.error {
  background: #f8d7da;
  color: #721c24;
}

.log-entry.progress {
  background: #d1ecf1;
  color: #0c5460;
}

.log-time {
  font-weight: 500;
  min-width: 60px;
}

.log-message {
  flex: 1;
}

/* Animation for toast appearance */
.progress-toast {
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
