from sqlalchemy import (
    Column, String, Text, Boolean, TIMESTAMP, Integer, Date
)
from sqlalchemy.dialects.postgresql import UUID as PG_UUID, JSONB, ARRAY, BIGINT
from backend.extensions import db

class CopyrightsFiles(db.Model):
    __tablename__ = 'copyrights_files'
    __bind_key__ = 'maidalv_db'

    id = Column(PG_UUID(as_uuid=True), nullable=False, primary_key=True)
    filename = Column(Text)
    registration_number = Column(Text)
    method = Column(Text)
    production = Column(Boolean)
    type = Column(String)
    create_time = Column(TIMESTAMP, nullable=False)
    update_time = Column(TIMESTAMP, nullable=False)

    query = db.session.query_property()

    def to_dict(self):
        """Helper method to convert model object to a dictionary."""
        return {
            'id': str(self.id),
            'filename': self.filename,
            'registration_number': self.registration_number,
            'method': self.method,
            'production': self.production,
            'type': self.type,
            'create_time': self.create_time.isoformat() if self.create_time else None,
            'update_time': self.update_time.isoformat() if self.update_time else None,
        }
    
    def __repr__(self):
        return f'<CopyrightsFiles {self.id}>'
