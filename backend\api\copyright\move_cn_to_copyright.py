"""
File processing operations for copyright assets
"""
import os
import tempfile
from flask import current_app

from backend.utils.image_resize import create_resized_image
from backend.utils.Tencent_COS import get_cos_client
from backend.utils.cache_utils import get_case_df
from .helpers import sanitize_name
from backend.utils.db_utils import insert_and_update_df_to_GZ_batch
import asyncio
import mimetypes
from backend.AI.GC_VertexAI import vertex_genai_image_gen_async

def process_cn_website_file_move(source_filename, source_website, docket_formatted, reg_no, plaintiff_id, case_id):
    """
    Process moving a CN website file to copyrights_files.
    This includes file copying, resizing, COS upload, and image search.
    Yields log messages and the new filename.
    """
    try:
        # Use config for base path
        ip_base_path = current_app.config.get('IP_BASE_PATH', 'D:\\Documents\\Programing\\TRO\\Documents\\IP' if os.name == 'nt' else '/Documents/IP')
        source_path = os.path.join(ip_base_path, source_website, sanitize_name(docket_formatted))
        
        source_file_path = os.path.join(source_path, source_filename)

        if not os.path.exists(source_file_path):
            raise FileNotFoundError(f"Source file {source_filename} not found in {source_path}")

        # Perform image search and processing to get the best version of the image
        with tempfile.TemporaryDirectory(prefix="copyright_search_") as temp_dir:
            yield {'type': 'log', 'message': 'Starting image search...'}
            
            search_generator = search_image(source_file_path, reg_no, temp_dir)
            processed_image_path = None
            method = None
            for result in search_generator:
                if isinstance(result, dict) and 'type' in result:
                    yield result
                elif isinstance(result, tuple):
                    processed_image_path, method = result

            if processed_image_path and method:
                yield {'type': 'log', 'message': f"Image search completed, method: {method}"}
                new_filename = f"{reg_no}_{method}.webp"
                with open(processed_image_path, 'rb') as f:
                    image_data = f.read()
            else:
                yield {'type': 'log', 'message': "Image search yielded no results, using original file."}
                new_filename = f"{reg_no}_CnWebRaw.webp"
                with open(source_file_path, 'rb') as f:
                    image_data = f.read()

            # Save the final image to SSD and upload to COS
            yield {'type': 'log', 'message': "Saving image to SSD and uploading to COS..."}
            save_generator = save_image_ssd_and_cos(image_data, new_filename, plaintiff_id, case_id)
            for item in save_generator:
                yield item
        
        # Update case images metadata using the resized filename
        yield {'type': 'log', 'message': "Updating tb_case images metadata..."}
        update_tb_case_images_metadata(case_id, reg_no, new_filename)
        
        current_app.logger.info(f"Successfully processed CN website file move: {source_filename} -> {new_filename}")
        yield {'type': 'result', 'filename': new_filename}
        
    except Exception as e:
        current_app.logger.error(f"Error processing CN website file move: {e}")
        raise


def search_image(image_path, reg_no, temp_folder):
    """
    Perform image search using GoogleImage, TinEye, and GenAI.
    Yields log messages and returns the final image path and method.
    """
    try:
        current_app.logger.info(f"Starting image search for {reg_no} at {image_path}")

        # 1. Search on Google Image
        try:
            current_app.logger.info(f"Searching Google Images for {reg_no}")
            yield {'type': 'log', 'message': 'Searching Image on Google...'}
            from backend.ImageSearch.ChineseWebsites_C_copyright_google_vision import reverse_search_with_google_vision
            google_results = reverse_search_with_google_vision(image_path, output_folder=temp_folder, final_folder=temp_folder)
            if google_results:
                current_app.logger.info(f"Found results on Google Images for {reg_no}")
                yield {'type': 'log', 'message': 'Image found on Google'}
                yield google_results[0].get("path"), "GoogleVision"
                return
        except ImportError as e:
            current_app.logger.warning(f"Google Vision module not available: {e}")
        except Exception as e:
            current_app.logger.error(f"Error in Google Images search: {e}")

        # 2. If not found, search on TinEye
        try:
            current_app.logger.info(f"No results on Google Images, trying TinEye for {reg_no}")
            yield {'type': 'log', 'message': 'Searching Image on TinEye...'}
            from backend.ImageSearch.ChineseWebsites_C_copyright_tineye import reverse_search_on_tineye
            tineye_results = reverse_search_on_tineye(image_path, output_folder=temp_folder, final_folder=temp_folder)
            if tineye_results:
                current_app.logger.info(f"Found results on TinEye for {reg_no}")
                yield {'type': 'log', 'message': 'Image found on TinEye'}
                yield tineye_results[0].get("path"), "TinEye"
                return
        except ImportError as e:
            current_app.logger.warning(f"TinEye module not available: {e}")
        except Exception as e:
            current_app.logger.error(f"Error in TinEye search: {e}")

        # 3. If still not found, use GenAI
        try:
            current_app.logger.info(f"No results on TinEye, using GenAI for {reg_no}")
            yield {'type': 'log', 'message': 'Image not found => Using GenAI...'}

            watermark_removal_prompt = f"Your job is to generate a picture exactly the same but remove the watermark"
            
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            inline_data = loop.run_until_complete(vertex_genai_image_gen_async(
                [("text", watermark_removal_prompt), ("image_path", image_path)]
            ))
            loop.close()

            if inline_data and not isinstance(inline_data, str):
                processed_image_bytes = inline_data.data
                mime_type = inline_data.mime_type
                genai_extension = mimetypes.guess_extension(mime_type) or f".{mime_type.split('/')[-1]}"
                
                final_image_path_genai = os.path.join(temp_folder, f"{reg_no}_GenAI{genai_extension}")

                with open(final_image_path_genai, "wb") as f:
                    f.write(processed_image_bytes)
                
                current_app.logger.info(f"GenAI processing successful for {reg_no}")
                yield {'type': 'log', 'message': 'Image generated using GenAI'}
                yield final_image_path_genai, "GenAI"
                return
            else:
                current_app.logger.error(f"GenAI processing failed for {reg_no}")

        except ImportError as e:
            current_app.logger.warning(f"GenAI module not available: {e}")
        except Exception as e:
            current_app.logger.error(f"Error in GenAI processing: {e}")

        current_app.logger.info(f"Completed image search for {reg_no}, no results found.")
        yield None, None

    except Exception as e:
        current_app.logger.error(f"Error in image search for {reg_no}: {e}")
        yield None, None


def save_image_ssd_and_cos(image_data, filename, plaintiff_id, case_id):
    """
    Saves an image to the local hard drive, creates resized versions, and uploads to COS.
    Yields log messages.
    """
    try:
        # Get case data for directory structure
        case_df = get_case_df()
        case_row = case_df[case_df['id'] == case_id]
        if case_row.empty:
            raise ValueError(f"Case {case_id} not found")

        date_filed = case_row.iloc[0].get('date_filed')
        docket = case_row.iloc[0].get('docket')

        if date_filed:
            if isinstance(date_filed, str):
                date_filed_str = date_filed
            else:
                date_filed_str = date_filed.strftime('%Y-%m-%d')
        else:
            date_filed_str = "unknown-date"

        # Destination directory structure from config
        case_files_base_path = current_app.config.get('CASE_FILES_BASE_PATH', 'D:\\Documents\\Programing\\TRO\\Documents\\Case Files' if os.name == 'nt' else '/Documents/Case Files')
        dest_dir = os.path.join(case_files_base_path, sanitize_name(f'{date_filed_str} - {docket}'), 'images')
        os.makedirs(dest_dir, exist_ok=True)

        # Save the original image
        yield {'type': 'log', 'message': f"Saving original image to {dest_dir}..."}
        with open(os.path.join(dest_dir, filename), "wb") as f:
            f.write(image_data)

        # Create resized versions (high and low)
        output_path = dest_dir.replace('/images', '')
        yield {'type': 'log', 'message': "Creating resized images..."}
        resized_filename = create_resized_image(dest_dir, filename, output_path)

        if not resized_filename:
            current_app.logger.warning(f"Failed to create resized images for {filename}")
            resized_filename = filename

        # Upload to COS
        yield {'type': 'log', 'message': "Uploading images to COS..."}
        for item in upload_to_cos(plaintiff_id, resized_filename, output_path):
            yield item

        current_app.logger.info(f"Successfully saved and uploaded {filename}")

    except Exception as e:
        current_app.logger.error(f"Error in save_image_ssd_and_cos: {e}")
        raise


def upload_to_cos(plaintiff_id, filename, local_base_dir):
    """Upload files to Tencent COS."""
    try:
        client, bucket = get_cos_client()

        # Upload high resolution image
        high_path = os.path.join(local_base_dir, "images", "high", filename)
        if os.path.exists(high_path):
            cos_key_high = f"plaintiff_images/{plaintiff_id}/high/{filename}"
            yield {'type': 'log', 'message': f"Uploading high resolution image to {cos_key_high}..."}
            with open(high_path, 'rb') as f:
                client.put_object(
                    Bucket=bucket,
                    Key=cos_key_high,
                    Body=f.read()
                )
            current_app.logger.info(f"Uploaded to COS: {cos_key_high}")

        # Upload low resolution image
        low_path = os.path.join(local_base_dir, "images", "low", filename)
        if os.path.exists(low_path):
            cos_key_low = f"plaintiff_images/{plaintiff_id}/low/{filename}"
            yield {'type': 'log', 'message': f"Uploading low resolution image to {cos_key_low}..."}
            with open(low_path, 'rb') as f:
                client.put_object(
                    Bucket=bucket,
                    Key=cos_key_low,
                    Body=f.read()
                )
            current_app.logger.info(f"Uploaded to COS: {cos_key_low}")

        # Upload certificate if exists
        cert_filename = filename.replace('.webp', '_full.webp')
        cert_path = os.path.join(local_base_dir, "images", "high", cert_filename)
        if os.path.exists(cert_path):
            cos_key_cert = f"plaintiff_images/{plaintiff_id}/high/{cert_filename}"
            yield {'type': 'log', 'message': f"Uploading certificate to {cos_key_cert}..."}
            with open(cert_path, 'rb') as f:
                client.put_object(
                    Bucket=bucket,
                    Key=cos_key_cert,
                    Body=f.read()
                )
            current_app.logger.info(f"Uploaded certificate to COS: {cos_key_cert}")

    except Exception as e:
        current_app.logger.error(f"Error uploading to COS: {e}")
        raise
    

def update_tb_case_images_metadata(case_id, reg_no, filename):
    """Update case images metadata in tb_case."""
    try:
        case_df = get_case_df()
        case_index = case_df[case_df['id'] == case_id].index
        
        if not case_index.empty:
            idx = case_index[0]
            
            # Ensure 'images' and 'copyrights' columns exist and are dicts
            if 'images' not in case_df.columns:
                case_df['images'] = None
            if not isinstance(case_df.at[idx, 'images'], dict):
                 case_df.at[idx, 'images'] = {}
            
            images_data = case_df.at[idx, 'images']
            if 'copyrights' not in images_data:
                images_data['copyrights'] = {}

            # Update the metadata
            images_data['copyrights'][filename] = {
                "reg_no": reg_no,
                "full_filename": filename.replace(".webp", "_full.webp")
            }
            
            # Update DataFrame
            row_to_update = case_df.loc[case_index].copy()
            insert_and_update_df_to_GZ_batch(row_to_update, "tb_case", "id")

            current_app.logger.info(f"Updated case {case_id} in tb_case for reg_no: {reg_no}, and filename: {filename}")
        else:
            current_app.logger.warning(f"Case {case_id} not found in case_df for metadata update.")

    except Exception as e:
        current_app.logger.error(f"Error updating case images metadata: {e}")
        raise