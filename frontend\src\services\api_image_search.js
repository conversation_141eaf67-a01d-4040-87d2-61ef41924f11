import axios from 'axios';

// Generic image search function that can be used for both patents and trademarks
export const searchImages = async (imageData, numClose, regNoConstraint, endpoint = '/api/v1/patents/image-search') => {
    const formData = new FormData();
    formData.append('image', imageData);
    if (numClose) {
        formData.append('num_close', numClose);
    }
    if (regNoConstraint) {
        formData.append('reg_no_constraint', regNoConstraint);
    }

    try {
        const response = await axios.post(endpoint, formData, {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        });
        return response.data;
    } catch (error) {
        console.error('Error searching images:', error);
        throw error;
    }
};

// Convenience function for patent image search
export const searchPatentImages = async (imageData, numClose, regNoConstraint) => {
    return searchImages(imageData, numClose, regNoConstraint, '/api/v1/patents/image-search');
};

// Convenience function for trademark image search
export const searchTrademarkImages = async (imageData, numClose, regNoConstraint) => {
    return searchImages(imageData, numClose, regNoConstraint, '/api/v1/trademarks/image-search');
};