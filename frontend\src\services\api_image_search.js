import axios from 'axios';


export const searchImages = async (imageData, numClose, regNoConstraint) => {
    const formData = new FormData();
    formData.append('image', imageData);
    if (numClose) {
        formData.append('num_close', numClose);
    }
    if (regNoConstraint) {
        formData.append('reg_no_constraint', regNoConstraint);
    }

    try {
        const response = await axios.post(`/api/v1/patents/image-search`, formData, {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        });
        return response.data;
    } catch (error) {
        console.error('Error searching images:', error);
        throw error;
    }
};