.duplicate-reviewer-container {
    padding: 2rem;
    font-family: sans-serif;
    background-color: #f4f6f8;
    min-height: 100vh;
}

.reviewer-header {
    text-align: center;
    margin-bottom: 2rem;
}

.reviewer-header h1 {
    font-size: 2.5rem;
    color: #333;
}

.reviewer-header p {
    font-size: 1.1rem;
    color: #666;
}

.reviewer-layout {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2rem;
}

.comparison-area {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    gap: 2rem;
    width: 100%;
}

.asset-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    width: 400px;
    overflow: hidden;
    transition: transform 0.2s;
}

.asset-card:hover {
    transform: translateY(-5px);
}

.asset-image-container {
    width: 100%;
    height: 300px;
    background-color: #eee;
}

.asset-image-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.asset-info {
    padding: 1rem;
}

.asset-info h4 {
    margin: 0 0 0.5rem 0;
    font-size: 1.2rem;
}

.asset-info p {
    margin: 0.25rem 0;
    color: #555;
}

.similarity-score {
    text-align: center;
    padding-top: 100px;
}

.similarity-score h3 {
    color: #333;
}

.similarity-score p {
    font-size: 2rem;
    font-weight: bold;
    color: #007bff;
}

.action-bar {
    display: flex;
    gap: 1rem;
    padding: 1rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.no-pairs-message {
    text-align: center;
    padding: 4rem;
    background: white;
    border-radius: 8px;
}

.error-message {
    color: #dc3545;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    padding: 1rem;
    border-radius: 4px;
    text-align: center;
}

.loading-spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 4rem auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}