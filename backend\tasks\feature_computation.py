# backend/tasks/feature_computation.py
import os, logging, uuid, json, importlib # Removed time, httpx
from datetime import datetime, timezone
import numpy as np
import pickle # Added import for pickling descriptors
from pathlib import Path # Added import
from celery import shared_task
from sqlalchemy import select,  and_, update, insert, bindparam, or_, delete 
from qdrant_client import  models as qdrant_models 
from typing import Optional 
from tqdm import tqdm 

from backend.extensions import db # Access db via the application context provided by Celery task
from backend.database.models import ModelTestsModel, ModelTestsImage, ModelTestsFeatureStatus, ModelTestsFeatureStorage
# Import necessary functions from file_utils
from backend.utils.file_utils import allowed_file, get_image_folder_path, get_master_folder
from backend.utils.vector_store import get_qdrant_client, ensure_collection_exists 
from backend.utils.retry_utils import execute_with_retry, DB_RETRYABLE_EXCEPTIONS, QDRANT_RETRYABLE_EXCEPTIONS 
from backend.models.base import ImageModelBase # Import the correct base model interface
# Removed direct imports of EfficientNetModel and DHashModel
from .comparison_execution import comparison_task # Import for chaining

# Configure logging
logger = logging.getLogger(__name__)
# ❌⚠️📥🔥✅☑️✔️💯💧⛏🔨🗝🔑 🔒💡⛔🚫❗
# --- Constants ---
# Assuming MASTERFOLDER is set in the environment or Flask config
# MASTERFOLDER is now determined dynamically using get_master_folder() from file_utils

# --- Helper Functions ---

def _get_image_metadata(image_path_str: str) -> Optional[dict]:
    """Safely gets file metadata (size, mod time), returning None on error."""
    try:
        if not os.path.exists(image_path_str):
            logger.warning(f"Image path does not exist: {image_path_str}")
            return None
        
        mod_time_timestamp = os.path.getmtime(image_path_str)
        mod_time_dt = datetime.fromtimestamp(mod_time_timestamp, tz=timezone.utc)
        return {
            'file_last_modified': mod_time_dt
        }
    except Exception as e:
        logger.error(f"Error getting metadata for {image_path_str}: {e}", exc_info=True)
        return None

def _sync_images_to_db(ip_category, image_type):
    """
    Efficiently scans filesystem and syncs image records to the database using bulk operations.
    Adds new images, updates modified ones, and deletes records for missing files.
    (FR2.2.3, step 0)

    Args:
        ip_category (str): The IP category (e.g., 'trademark', 'copyright').
        image_type (str): 'product' or 'ip'.

    Returns:
        tuple: (added_count, updated_count, deleted_count)
    """
    logger.info(f"Starting efficient filesystem sync for {ip_category}/{image_type} images...")
    added_count = 0
    updated_count = 0
    deleted_count = 0

    try:
        # --- 1. Determine Scan Path ---
        scan_path_obj = get_image_folder_path(ip_category, image_type)
        scan_path = str(scan_path_obj)
        master_folder_path = get_master_folder()
        master_folder_str = str(master_folder_path)

        if not os.path.isdir(scan_path):
            logger.warning(f"Scan directory does not exist: {scan_path}")
            return 0, 0, 0

        # --- 2. Fetch Existing DB Records ---
        logger.info(f"Fetching existing DB records for {ip_category}/{image_type}...")
        db_conditions = [ModelTestsImage.image_type == image_type]
        db_conditions.append(ModelTestsImage.ip_category == ip_category)

        stmt_existing = select(ModelTestsImage.image_id, ModelTestsImage.relative_path, ModelTestsImage.file_last_modified).where(and_(*db_conditions))
        existing_db_images_raw = execute_with_retry(lambda: db.session.execute(stmt_existing).all(), DB_RETRYABLE_EXCEPTIONS, session=db.session, operation_name="fetch existing images")
        # Convert to dict: {relative_path: {'image_id': uuid, 'file_last_modified': datetime}}
        existing_db_images = {
            row.relative_path: {'image_id': row.image_id, 'file_last_modified': row.file_last_modified}
            for row in existing_db_images_raw
        }
        logger.info(f"Found {len(existing_db_images)} existing records in DB.")

        # --- 3. Scan Filesystem ---
        logger.info(f"Scanning filesystem directory: {scan_path}")
        filesystem_images = {} # {relative_path: {'file_last_modified': datetime, 'original_filename': str}}
        found_files_count = 0
        for entry in os.scandir(scan_path):
            if entry.is_file() and allowed_file(entry.name):
                found_files_count += 1
                image_path_str = entry.path
                relative_path = os.path.relpath(image_path_str, master_folder_str).replace('\\', '/')
                metadata = _get_image_metadata(image_path_str)

                if metadata:
                    filesystem_images[relative_path] = {
                        'file_last_modified': metadata['file_last_modified'],
                        'original_filename': entry.name
                    }
                else:
                    logger.warning(f"Could not get metadata for {image_path_str}, skipping.")
        logger.info(f"Found {len(filesystem_images)} valid image files on filesystem (scanned {found_files_count} potential files).")

        # --- 4. Compare FS and DB, Prepare Bulk Operations ---
        inserts_list = []
        updates_list = [] # List of dicts for bulk_update_mappings or Core update
        db_paths_found_on_fs = set()

        logger.info("Comparing filesystem and DB records...")
        for fs_rel_path, fs_data in filesystem_images.items():
            db_entry = existing_db_images.get(fs_rel_path)

            if db_entry:
                # Exists in DB, check for modification
                db_paths_found_on_fs.add(fs_rel_path)
                if fs_data['file_last_modified'] != db_entry['file_last_modified']:
                    logger.debug(f"Marking for update: {fs_rel_path}")
                    updates_list.append({
                        'image_id': db_entry['image_id'], # Match column name for PK
                        'file_last_modified': fs_data['file_last_modified']
                    })
                    updated_count += 1
            else:
                # New file, mark for insertion
                logger.debug(f"Marking for insert: {fs_rel_path}")
                inserts_list.append({
                    'image_id': uuid.uuid4(),
                    'original_filename': fs_data['original_filename'],
                    'relative_path': fs_rel_path,
                    'ip_category': ip_category, # Always assign the category passed to the function
                    'image_type': image_type,
                    'file_last_modified': fs_data['file_last_modified'],
                })
                added_count += 1

        # --- 5. Identify Deletions ---
        db_paths_to_delete = set(existing_db_images.keys()) - db_paths_found_on_fs
        delete_ids_list = [existing_db_images[path]['image_id'] for path in db_paths_to_delete]
        deleted_count = len(delete_ids_list)
        if deleted_count > 0:
             logger.warning(f"Identified {deleted_count} records in DB missing from filesystem. Marking for deletion.")
             for path in db_paths_to_delete:
                 logger.warning(f"  - To delete: {path} (ID: {existing_db_images[path]['image_id']})")


        # --- 6. Execute Bulk Operations ---
        if not inserts_list and not updates_list and not delete_ids_list:
            logger.info(f"No database changes required for {ip_category}/{image_type}.")
            return 0, 0, 0

        logger.info(f"Preparing to execute bulk operations: Inserts={added_count}, Updates={updated_count}, Deletes={deleted_count}")

        # Use a single transaction
        with db.session.begin_nested(): # Use nested transaction or manage manually
            if inserts_list:
                logger.info(f"Executing bulk insert for {len(inserts_list)} records...")
                # Using Core insert for potential performance benefits
                execute_with_retry(lambda: db.session.execute(insert(ModelTestsImage), inserts_list), DB_RETRYABLE_EXCEPTIONS, session=db.session, operation_name="bulk insert images")
                logger.info("Bulk insert successful.")

            if updates_list:
                logger.info(f"Executing bulk update for {len(updates_list)} records...")
                # Using ORM bulk update. The statement should not have a WHERE clause;
                # SQLAlchemy generates it from the primary key in each dictionary.
                update_stmt = update(ModelTestsImage)
                execute_with_retry(lambda: db.session.execute(update_stmt, updates_list), DB_RETRYABLE_EXCEPTIONS, session=db.session, operation_name="bulk update images")
                logger.info("Bulk update successful.")

            if delete_ids_list:
                logger.info(f"Executing bulk delete for {len(delete_ids_list)} records...")
                delete_stmt = delete(ModelTestsImage).where(ModelTestsImage.image_id.in_(delete_ids_list))
                result = execute_with_retry(lambda: db.session.execute(delete_stmt), DB_RETRYABLE_EXCEPTIONS, session=db.session, operation_name="bulk delete images")
                # result.rowcount might not be perfectly accurate depending on DB driver/config
                logger.info(f"Bulk delete successful (affected rows reported: {result.rowcount if result else 'N/A'}).") # Added check for None result

        db.session.commit() # Commit the main transaction
        logger.info(f"Committed all DB changes for {ip_category}/{image_type}.")

    except (ValueError, OSError, Exception) as e:
        logger.error(f"Error during efficient image sync for {ip_category}/{image_type}: {e}", exc_info=True)
        db.session.rollback() # Rollback on any error during the process
        # Re-raise to allow Celery task to handle failure state
        raise

    logger.info(f"Efficient filesystem sync completed for {ip_category}/{image_type}. Added: {added_count}, Updated: {updated_count}, Deleted: {deleted_count}")
    return added_count, updated_count, deleted_count


# --- Celery Task Definition ---

@shared_task(bind=True)
def compute_features_task(self, ip_category):
    """
    Celery task to compute features for images in a given IP category.
    (FR2.2.3)
    """
    task_id = self.request.id
    logger.info(f"Starting feature computation task {task_id} for IP category: {ip_category}")
    if os.getenv('RUN_TASKS_IN_FLASK', 'false').lower() != 'true':
        self.update_state(state='PROGRESS', meta={'current_step': 'Initializing', 'ip_category': ip_category})

    try:
        # --- Step 0: Filesystem Scan and Metadata Creation ---
        if os.getenv('RUN_TASKS_IN_FLASK', 'false').lower() != 'true':
            self.update_state(state='PROGRESS', meta={'current_step': 'Scanning product images', 'ip_category': ip_category})
        added_prod, updated_prod, deleted_prod = _sync_images_to_db(ip_category, 'product')
        logger.info(f"Product image sync result - Added: {added_prod}, Updated: {updated_prod}, Deleted: {deleted_prod}")

        if os.getenv('RUN_TASKS_IN_FLASK', 'false').lower() != 'true':
            self.update_state(state='PROGRESS', meta={'current_step': 'Scanning IP images', 'ip_category': ip_category})
        added_ip, updated_ip, deleted_ip = _sync_images_to_db(ip_category, 'ip')
        logger.info(f"IP image sync result - Added: {added_ip}, Updated: {updated_ip}, Deleted: {deleted_ip}")

        # --- Identify Applicable Models ---
        if os.getenv('RUN_TASKS_IN_FLASK', 'false').lower() != 'true':
            self.update_state(state='PROGRESS', meta={'current_step': 'Identifying applicable models', 'ip_category': ip_category})
        # Query models applicable to this ip_category or 'all'
        stmt_models = select(ModelTestsModel).where(
            ModelTestsModel.is_active == True,
            or_(
                ModelTestsModel.applicable_ip_category.any(ip_category), # Check if category is in array
                ModelTestsModel.applicable_ip_category.any('all')      # Check if 'all' is in array
            )
        )
        applicable_models = execute_with_retry(lambda: db.session.execute(stmt_models).scalars().all(), DB_RETRYABLE_EXCEPTIONS, session=db.session, operation_name="fetch applicable models")
        if not applicable_models:
            logger.warning(f"No active models found applicable to IP category: {ip_category}")
            if os.getenv('RUN_TASKS_IN_FLASK', 'false').lower() != 'true':
                self.update_state(state='SUCCESS', meta={'current_step': 'Completed', 'status': 'No applicable models found.'})
            # Chain should still proceed even if no features computed? Yes, comparison task should handle no features.
            logger.info(f"Triggering comparison_task for IP category: {ip_category} (no features computed)")
            # Check the environment variable to decide execution mode
            if os.getenv('RUN_TASKS_IN_FLASK', 'false').lower() == 'true':
                # Run the task synchronously in the current process
                # Optional: Log synchronous execution
                # from flask import current_app # Import if using logger
                # current_app.logger.info(f"Running task {comparison_task.name} synchronously (no features).")
                comparison_task.run(ip_category=ip_category) # Run synchronously
                logger.info(f"Executed comparison_task synchronously for '{ip_category}' (no features computed).")
            else:
                # Queue the task asynchronously with Celery (original behavior)
                comparison_task.delay(ip_category=ip_category)
                logger.info(f"Dispatched comparison_task for '{ip_category}' (no features computed).")
            return {'status': 'Completed', 'message': 'No applicable models found.'}

        logger.info(f"Found {len(applicable_models)} applicable models for {ip_category}.")

        total_models = len(applicable_models)
        processed_models_count = 0
        master_folder_path = get_master_folder() # Get master folder path once before the loop

        # --- Load Model Configuration ---
        config_path = Path('models/config.json')
        if not config_path.exists():
            logger.error(f"Model configuration file not found at {config_path}")
            # Fail the task if config is missing
            raise FileNotFoundError(f"Model configuration file not found at {config_path}")
        try:
            with open(config_path, 'r') as f:
                all_model_configs_list = json.load(f)
            # Create a lookup dictionary by model_id (but the model_id in the database is a UUID, so we need to convert it)
            model_configs_dict = {str(uuid.uuid5(uuid.NAMESPACE_DNS, cfg['model_id'])): cfg for cfg in all_model_configs_list}
            logger.info(f"Loaded {len(model_configs_dict)} model configurations from {config_path}")
        except (json.JSONDecodeError, IOError, KeyError) as e:
            logger.error(f"Error loading or parsing model configuration file {config_path}: {e}", exc_info=True)
            raise # Re-raise to fail the task

        # --- Step 2: Iterate Through Applicable Models ---
        for model in applicable_models:
            processed_models_count += 1
            model_name = model.model_name
            model_id = model.model_id # This is UUID
            logger.info(f"\n\n🔨 Processing model {processed_models_count}/{total_models}: {model_name} (ID: {model_id})")
            if os.getenv('RUN_TASKS_IN_FLASK', 'false').lower() != 'true':
                self.update_state(state='PROGRESS', meta={
                    'current_step': f'Processing Model {processed_models_count}/{total_models}: {model_name}',
                    'total_models': total_models,
                    'current_model_index': processed_models_count,
                })

            # --- Step 171 & Incrementality: Identify Images Needing Computation ---
            # Get all images for the current ip_category (both product and ip)
            stmt_images = select(ModelTestsImage).where(
                ModelTestsImage.ip_category == ip_category # Directly filter by the stored category
            ).order_by(ModelTestsImage.image_type.desc()) # Sort by image_type ('product' then 'ip')
            all_images_for_category = execute_with_retry(lambda: db.session.execute(stmt_images).scalars().all(), DB_RETRYABLE_EXCEPTIONS, session=db.session, operation_name="fetch all images for category")
            images_to_process = []

            logger.info(f"Identifying images needing feature computation for model {model_name}...")

            # Fetch existing statuses for this model in bulk for efficiency
            stmt_all_statuses = select(ModelTestsFeatureStatus).where(ModelTestsFeatureStatus.model_id == model_id)
            existing_statuses = {status.image_id: status for status in execute_with_retry(lambda: db.session.execute(stmt_all_statuses).scalars().all(), DB_RETRYABLE_EXCEPTIONS, session=db.session, operation_name="fetch existing feature statuses")}

            for image in all_images_for_category:
                image_id = image.image_id
                relative_path = image.relative_path # Use relative_path now
                image_last_modified = image.file_last_modified # Already timezone-aware UTC

                if not image_last_modified:
                     logger.warning(f"Image {relative_path} (ID: {image_id}) is missing file_last_modified timestamp. Skipping computation for model {model_name}.")
                     continue

                # Check feature status for this image/model pair using the prefetched dict
                feature_status = existing_statuses.get(image_id)

                needs_computation = False
                if not feature_status:
                    # Case 1: No status record exists - New image for this model or newly registered model
                    needs_computation = True
                    logger.debug(f"Image ID {image_id} needs computation for model {model_id} (no status record).")
                # elif feature_status.status != 'computed': # Assuming status column exists
                #     # Case 2: Status is not 'computed' (e.g., 'pending', 'failed')
                #     needs_computation = True
                #     logger.debug(f"Image ID {image_id} needs computation for model {model_id} (status: {feature_status.status}).")
                elif not feature_status.last_computed_at:
                     # Case 2b: Status exists but computation never succeeded
                     needs_computation = True
                     logger.debug(f"Image ID {image_id} needs computation for model {model_id} (last_computed_at is NULL).")
                elif feature_status.last_computed_at < image_last_modified:
                    # Case 3: Image file was modified after last computation
                    needs_computation = True
                    logger.debug(f"Image ID {image_id} needs computation for model {model_id} (image modified). Last computed: {feature_status.last_computed_at}, Image modified: {image_last_modified}")
                # Optional Case 4: Model version changed (if tracking model versions)
                # elif feature_status.model_version != model.version:
                #    needs_computation = True

                if needs_computation:
                    images_to_process.append(image)

            total_images_for_model = len(images_to_process)
            logger.info(f"Found {total_images_for_model} images requiring feature computation for model {model_name}.")

            if not images_to_process:
                logger.info(f"No images require processing for model {model_name} based on DB status and file modification times.")
                # For embedding models, perform an additional Qdrant count verification
                if model.model_type == 'embedding':
                    model_name_cleaned = model.model_name.replace(" ", "_").lower()
                    collection_name = f"workbench_{ip_category}_{model_name_cleaned}"
                    qdrant_client = get_qdrant_client()
                    total_images_in_db = len(all_images_for_category) # Total images in DB for this category
                    
                    try:
                        collection_info = execute_with_retry(
                            lambda: qdrant_client.get_collection(collection_name=collection_name),
                            QDRANT_RETRYABLE_EXCEPTIONS
                        )
                        qdrant_points_count = collection_info.points_count
                        
                        if qdrant_points_count != total_images_in_db:
                            logger.warning(f"Qdrant collection '{collection_name}' for model '{model_name}' has {qdrant_points_count} points, but {total_images_in_db} images are in the DB. This indicates an inconsistency. Forcing re-computation for all images for this model.")
                            # Force re-computation for all images in this category for this model
                            images_to_process = list(all_images_for_category)
                            total_images_for_model = len(images_to_process) # Update the count for the forced re-computation
                            logger.info(f"Re-initialized images_to_process with {total_images_for_model} images for model {model_name} due to Qdrant count mismatch.")
                        else:
                            logger.info(f"Qdrant count verified for collection '{collection_name}'. Found {qdrant_points_count} points as expected. Skipping computation for this model as it appears fully synced.")
                            continue # Skip to the next model if counts match and no images needed re-processing
                    except Exception as qdrant_check_err:
                        logger.warning(f"Could not verify Qdrant collection count for '{collection_name}' (model: {model_name}): {qdrant_check_err}. Proceeding with computation as a precaution to ensure consistency.")
                        # If Qdrant check fails, we should proceed with computation to be safe.
                        # No 'continue' here.
                else: # Not an embedding model, and no images need processing
                    continue # Move to the next model

            # --- Step 172c: Load Model ---
            model_instance: Optional[ImageModelBase] = None # Type hint
            try:
                logger.info(f"Loading model: {model_name}")

                # --- Get implementation details from loaded config ---
                model_id_str = str(model.model_id) # Convert DB UUID to string for dict lookup
                model_config = model_configs_dict.get(model_id_str)
                if not model_config:
                     # This case should ideally not happen if registration ensures consistency
                     logger.error(f"Configuration not found for model ID {model_id_str} ({model_name}) in {config_path}. Skipping model.")
                     continue # Skip to the next model

                implementation_details = model_config.get('implementation')
                # --- End Get implementation details ---

                if not implementation_details or not isinstance(implementation_details, dict):
                    # Error message adjusted to refer to config file
                    raise ValueError(f"Model {model_name} (ID: {model_id_str}) has invalid or missing 'implementation' section in config file {config_path}.")

                module_path = implementation_details.get('module')
                class_name = implementation_details.get('class')
                parameters = implementation_details.get('parameters', {})
                # Also need vector_size later for embedding models, get it here too (used around line 355, 456)
                # Safely access nested dictionary
                vector_size = implementation_details.get('parameters', {}).get('vector_size')

                if not module_path or not class_name:
                     # Error message adjusted to refer to config file
                     raise ValueError(f"Model {model_name} (ID: {model_id_str}) implementation details missing 'module' or 'class' in config file {config_path}.")

                ModelClassModule = importlib.import_module(module_path)
                ModelClass = getattr(ModelClassModule, class_name)

                # Instantiate the model using the consistent signature found in tests
                # Pass model_id (as string) and config (parameters from json)
                model_instance = ModelClass(model_id=model_id_str, config=parameters)
                model_instance.load() # Call the model's load method
                logger.info(f"Model {model_name} loaded successfully.")

                # --- Iterate Through Images for the Current Model ---
                processed_images_count = 0
                # Prepare bulk update/insert lists for status
                status_updates = []
                status_inserts = []
                feature_storage_inserts = []
                qdrant_points_to_upsert = []

                # Wrap the loop with tqdm for progress bar in sync mode
                image_iterator = tqdm(
                    images_to_process,
                    desc=f"Processing {model_name}",
                    disable=os.getenv('RUN_TASKS_IN_FLASK', 'false').lower() != 'true' # Disable if running async
                )
                for image in image_iterator:
                    processed_images_count += 1
                    image_id = image.image_id # This is UUID
                    relative_path = image.relative_path # Use relative_path now
                    # Construct full path using the dynamically determined master folder path and pathlib
                    image_path_full = master_folder_path / relative_path
                    image_path_full_str = str(image_path_full) # Convert to string for functions needing it
                    logger.debug(f"Processing image {processed_images_count}/{total_images_for_model}: {relative_path} (ID: {image_id}) for model {model_name}")

                    # Update state with image progress if running asynchronously
                    if os.getenv('RUN_TASKS_IN_FLASK', 'false').lower() != 'true':
                        self.update_state(state='PROGRESS', meta={
                            'current_step': f'Processing Model {processed_models_count}/{total_models}: {model_name}',
                            'total_models': total_models,
                            'current_model_index': processed_models_count,
                            'current_image': processed_images_count,
                            'total_images_for_model': total_images_for_model,
                            'image_path': relative_path # Add image path for context
                        })

                    try:
                        # --- Preprocess Image ---
                        logger.debug(f"Preprocessing image: {image_path_full_str}")
                        if not image_path_full.exists(): # Use pathlib's exists()
                            raise FileNotFoundError(f"Image file not found: {image_path_full_str}")
                        preprocessed_image = model_instance.preprocess(image_path_full_str) # Pass string path
                        logger.debug(f"Image preprocessed successfully.")

                        # --- Compute Features ---
                        logger.debug(f"Computing features for image: {image_path_full_str}")
                        features = model_instance.compute_features(preprocessed_image)
                        logger.debug(f"Features computed successfully.")

                        # --- Prepare Feature Storage Data ---
                        model_type = model_instance.get_model_type()
                        logger.debug(f"Preparing feature storage for image {image_id}, model {model_id} (type: {model_type})")

                        computation_time = datetime.now(timezone.utc)
                        feature_data_bytes = None
                        qdrant_vector_data = None
                        data_for_storage = None # Variable to hold the data to be serialized/sent to Qdrant

                        if model_type == 'embedding':
                            # Prepare Qdrant data
                            if not vector_size or not isinstance(vector_size, int) or vector_size <= 0:
                                 # Adjusted error message to refer to config file
                                 raise ValueError(f"Model {model_name} (ID: {model_id_str}) has invalid or missing 'vector_size' in implementation parameters in config file.")

                            # Ensure features is a list of floats for Qdrant
                            if isinstance(features, np.ndarray):
                                qdrant_vector_data = features.flatten().tolist()
                            elif isinstance(features, list): # Assuming list of floats/ints
                                qdrant_vector_data = features
                            elif features is None:
                                logger.warning(f"Model {model_name} returned None features for image {image_id}. Skipping storage.")
                            else: # Catch other types
                                raise TypeError(f"Unsupported feature type for Qdrant storage: {type(features)}")

                            if qdrant_vector_data:
                                qdrant_point_id = str(image_id)
                                qdrant_points_to_upsert.append(
                                    qdrant_models.PointStruct(
                                        id=qdrant_point_id,
                                        vector=qdrant_vector_data,
                                        payload={
                                            "image_id": qdrant_point_id, # Store UUID string in payload
                                            "image_type": image.image_type # Add image_type to payload
                                        }
                                    )
                                )

                        elif model_type == 'descriptor':
                            # Descriptors return (keypoints, descriptors) tuple
                            if isinstance(features, tuple) and len(features) == 2 and isinstance(features[1], np.ndarray):
                                data_for_storage = features[1] # Extract the descriptors (numpy array)
                                logger.debug(f"Extracted descriptors (shape: {data_for_storage.shape}, dtype: {data_for_storage.dtype}) for descriptor model.")
                            elif features is None:
                                 logger.warning(f"Model {model_name} returned None features for image {image_id}. Skipping storage.")
                            else:
                                 logger.error(f"Unexpected feature format {type(features)} for descriptor model {model_name}, image {image_id}. Expected tuple (keypoints, descriptors). Cannot serialize.")
                                 # Optionally raise TypeError

                            if data_for_storage is not None:
                                # Store descriptors as pickled numpy arrays to preserve shape/dtype
                                feature_data_bytes = pickle.dumps(data_for_storage)

                        elif model_type == 'hash':
                            # Prepare PostgreSQL data
                            # Hashes should be directly serializable (bytes, str, or np.ndarray)
                            if isinstance(features, np.ndarray):
                                data_for_storage = features
                            elif isinstance(features, (str, bytes)):
                                data_for_storage = features
                            elif features is None:
                                logger.warning(f"Model {model_name} returned None features for image {image_id}. Skipping storage.")
                            else:
                                logger.error(f"Unsupported feature type {type(features)} for hash model {model_name}, image {image_id}. Expected np.ndarray, str, or bytes.")
                                # Optionally raise TypeError

                            if data_for_storage is not None:
                                # Hashes might be bytes or strings depending on implementation
                                feature_data_bytes = data_for_storage.encode('utf-8') if isinstance(data_for_storage, str) else data_for_storage

                        # Add to feature_storage_inserts list if feature_data_bytes was prepared
                        if feature_data_bytes is not None:
                                feature_storage_inserts.append({
                                    'image_id': image_id,
                                    'model_id': model_id,
                                    'features': feature_data_bytes,
                                    'computed_at': computation_time
                                })

                        # If feature_data_bytes is None, it's only a potential issue if the model_type
                        # was *supposed* to produce them (i.e., not 'embedding') AND
                        # features were actually computed (i.e., features is not None).
                        # If features were None, that specific case is logged earlier within the model_type blocks.
                        elif model_type != 'embedding' and features is not None: # feature_data_bytes is implicitly None here
                            logger.warning(f"For {model_type} model {model_name}, image {image_id}: "
                                           f"Features were computed but could not be prepared/serialized for DB storage.")
                        # No warning is needed if model_type == 'embedding' (feature_data_bytes is expected to be None for PG storage).
                        # No warning is needed if features was None (as that's logged within the specific model_type processing block).
                        # --- Prepare Feature Status Update/Insert ---
                        status_record = existing_statuses.get(image_id)
                        status_payload = {
                            'image_id': image_id,
                            'model_id': model_id,
                            'last_computed_at': computation_time,
                            'status': 'completed', # Set status on success
                            'error_message': None # Clear previous errors
                        }
                        if status_record:
                            status_payload['status_id'] = status_record.status_id # Include primary key for update
                            status_updates.append(status_payload)
                        else:
                            status_inserts.append(status_payload)

                    except Exception as e:
                        # --- Step 183: Error Handling for Image Processing ---
                        logger.error(f"❌ Error processing image {relative_path} (ID: {image_id}) for model {model_name}: {e}", exc_info=True)
                        # Prepare status update/insert for failure
                        error_message = str(e)[:255] # Truncate error message if needed
                        failure_time = datetime.now(timezone.utc)
                        status_record = existing_statuses.get(image_id)
                        status_payload = {
                            'image_id': image_id,
                            'model_id': model_id,
                            'last_computed_at': failure_time, # Record failure time, might be null if error before time is set
                            'status': 'failed', # Set status on failure
                            'error_message': error_message
                        }
                        if status_record:
                            # Avoid adding duplicates if already processed in this run
                            status_payload['status_id'] = status_record.status_id # Include primary key for update
                            if not any(s['image_id'] == image_id and s['model_id'] == model_id for s in status_updates):
                                status_updates.append(status_payload)
                        else:
                             if not any(s['image_id'] == image_id and s['model_id'] == model_id for s in status_inserts):
                                status_inserts.append(status_payload)
                        # Continue processing the next image within the same model

                # --- Bulk Operations After Processing All Images for Model ---
                try:
                    # 1. Upsert Qdrant points
                    if qdrant_points_to_upsert:
                        logger.info(f"Upserting {len(qdrant_points_to_upsert)} points to Qdrant collection for model {model_name}...")
                        qdrant_client = get_qdrant_client()
                        # Ensure model_name is accessible here (it should be from the 'model' object)
                        model_name_cleaned = model.model_name.replace(" ", "_").lower()
                        collection_name = f"workbench_{ip_category}_{model_name_cleaned}"
                        # Ensure collection exists (might be redundant if checked earlier, but safe)
                        # vector_size is loaded earlier from config
                        if model.model_type == 'embedding': # Only check/use vector_size for embedding models
                            if not vector_size: # Check if it was loaded correctly
                                logger.error(f"Cannot ensure Qdrant collection {collection_name} exists without vector_size for embedding model {model_name} (ID: {model_id_str}). Check config file. Skipping Qdrant upsert.")
                            else:
                                # Use correct function name
                                ensure_collection_exists(qdrant_client, collection_name, vector_size)
                                # --- Batch Upsert with Centralized Retry ---
                                batch_size = 500 # Qdrant batch size
                                logger.info(f"Using batch size: {batch_size} for Qdrant upsert.")
                                for i in range(0, len(qdrant_points_to_upsert), batch_size):
                                    batch = qdrant_points_to_upsert[i:i+batch_size]
                                    logger.info(f"Upserting batch {i // batch_size + 1} (size: {len(batch)}) to Qdrant collection '{collection_name}' using execute_with_retry...")
                                    # Wrap the upsert call with execute_with_retry using a lambda
                                    execute_with_retry(
                                        lambda: qdrant_client.upsert(collection_name=collection_name, points=batch, wait=True),
                                        QDRANT_RETRYABLE_EXCEPTIONS
                                    )
                                    logger.info(f"Batch {i // batch_size + 1} upsert successful.")
                        logger.info("Qdrant upsert successful.")
                        qdrant_points_to_upsert.clear() # Clear list after upsert

                    # 2. Bulk Insert/Update Feature Storage (PostgreSQL)
                    # Using Core UPSERT (ON CONFLICT DO UPDATE) is highly recommended here for efficiency and atomicity
                    if feature_storage_inserts:
                        logger.info(f"Upserting {len(feature_storage_inserts)} feature storage records in PostgreSQL for model {model_name}...")
                        from sqlalchemy.dialects.postgresql import insert as pg_insert
                        
                        # Define batch size for PostgreSQL operations
                        pg_batch_size = 500 # Adjust as needed, can be different from Qdrant's
                        logger.info(f"Using batch size: {pg_batch_size} for PostgreSQL feature storage upsert.")

                        for i in range(0, len(feature_storage_inserts), pg_batch_size):
                            batch = feature_storage_inserts[i:i+pg_batch_size]
                            logger.info(f"Upserting PostgreSQL feature storage batch {i // pg_batch_size + 1} (size: {len(batch)}) for model {model_name}...")

                            insert_stmt = pg_insert(ModelTestsFeatureStorage).values(batch)
                            upsert_stmt = insert_stmt.on_conflict_do_update(
                                index_elements=['image_id', 'model_id'], # Constraint name or columns
                                set_=dict(
                                    features=insert_stmt.excluded.features,
                                    computed_at=insert_stmt.excluded.computed_at
                                )
                             )
                            try:
                                execute_with_retry(
                                    lambda: db.session.execute(
                                        upsert_stmt,
                                        execution_options={"statement_timeout": 180000} # 180 seconds
                                    ),
                                    DB_RETRYABLE_EXCEPTIONS, session=db.session,
                                    operation_name=f"upsert feature storage batch {i // pg_batch_size + 1}")
                                db.session.commit() # Commit this batch
                                logger.info(f"PostgreSQL feature storage batch {i // pg_batch_size + 1} upsert and commit successful.")
                            except Exception as batch_db_err:
                                db.session.rollback()
                                logger.error(f"Error processing DB batch for feature_storage model {model_name}, batch {i // pg_batch_size + 1}: {batch_db_err}", exc_info=True)
                                raise # Re-raise to be caught by the outer 'bulk_err' handler

                        logger.info(f"All PostgreSQL feature storage upserts completed for model {model_name}.")
                        feature_storage_inserts.clear()

                    # 3. Bulk Update/Insert Feature Status (PostgreSQL)
                    if status_updates:
                        # Define batch size for PostgreSQL status operations, can be same as feature storage
                        pg_status_batch_size = 500 # Adjust as needed
                        logger.info(f"Using batch size: {pg_status_batch_size} for PostgreSQL feature status updates.")

                        logger.info(f"Bulk updating {len(status_updates)} feature status records...")
                        # Use bindparam for efficient bulk update
                        stmt_update = update(ModelTestsFeatureStatus).where(
                            ModelTestsFeatureStatus.status_id == bindparam('b_status_id') # Use primary key for update
                        ).values(
                            last_computed_at=bindparam('b_last_computed_at'),
                            status=bindparam('b_status'),
                            error_message=bindparam('b_error_message')
                        )
                        # Map keys for bindparam
                        mapped_updates = [
                            {
                                'b_status_id': s['status_id'], # Include primary key
                                'b_last_computed_at': s['last_computed_at'],
                                'b_status': s.get('status'), # Get status ('completed' or 'failed')
                                'b_error_message': s.get('error_message') # Get error message (None or str)
                            } for s in status_updates
                        ]
                        for i in range(0, len(mapped_updates), pg_status_batch_size):
                            batch = mapped_updates[i:i+pg_status_batch_size]
                            logger.info(f"Updating PostgreSQL feature status batch {i // pg_status_batch_size + 1} (size: {len(batch)}) for model {model_name}...")                            
                            try:
                                execute_with_retry(
                                    lambda: db.session.execute(
                                        stmt_update, batch,
                                        execution_options={"statement_timeout": 60000, "synchronize_session": False} # 60 seconds
                                    ),
                                    DB_RETRYABLE_EXCEPTIONS, session=db.session,
                                    operation_name=f"bulk update feature status batch {i // pg_status_batch_size + 1}")
                                db.session.commit() # Commit this batch
                                logger.info(f"PostgreSQL feature status update batch {i // pg_status_batch_size + 1} and commit successful.")
                            except Exception as batch_db_err:
                                db.session.rollback()
                                logger.error(f"Error processing DB batch for status_updates model {model_name}, batch {i // pg_status_batch_size + 1}: {batch_db_err}", exc_info=True)
                                raise # Re-raise
                                
                        logger.info("Feature status bulk update successful.")
                        status_updates.clear()

                    if status_inserts:
                        # Define batch size for PostgreSQL status operations, can be same as feature storage
                        pg_status_batch_size = 500 # Adjust as needed
                        logger.info(f"Using batch size: {pg_status_batch_size} for PostgreSQL feature status inserts.")

                        logger.info(f"Bulk inserting {len(status_inserts)} feature status records...")
                        # Define the insert statement once before the loop
                        insert_stmt_feature_status = insert(ModelTestsFeatureStatus)
                        for i in range(0, len(status_inserts), pg_status_batch_size):
                            batch = status_inserts[i:i+pg_status_batch_size]
                            logger.info(f"Inserting PostgreSQL feature status batch {i // pg_status_batch_size + 1} (size: {len(batch)}) for model {model_name}...")                            
                            try:
                                execute_with_retry(
                                    lambda: db.session.execute(
                                        insert_stmt_feature_status, batch,
                                        execution_options={"statement_timeout": 60000} # 60 seconds
                                    ),
                                    DB_RETRYABLE_EXCEPTIONS, session=db.session, operation_name=f"bulk insert feature status batch {i // pg_status_batch_size + 1}")
                                db.session.commit() # Commit this batch
                                logger.info(f"PostgreSQL feature status insert batch {i // pg_status_batch_size + 1} and commit successful.")
                            except Exception as batch_db_err:
                                db.session.rollback()
                                logger.error(f"Error processing DB batch for status_inserts model {model_name}, batch {i // pg_status_batch_size + 1}: {batch_db_err}", exc_info=True)
                                raise # Re-raise
                                
                        logger.info("Feature status bulk insert successful.")
                        status_inserts.clear()

                except Exception as bulk_err:
                    logger.error(f"❌ Error during bulk database/Qdrant operations for model {model_name}: {bulk_err}", exc_info=True)
                    db.session.rollback() # Rollback any uncommitted state from the failed operation
                    # How to handle? Mark all statuses as failed? Log and continue?
                    # For now, log and continue to next model, but statuses might be inconsistent.

            # --- Model Load/Process Error Handling & Unload (Finally Block) ---
            except Exception as model_load_err:
                # Handle errors during model loading itself
                logger.error(f"❌ Failed to load or process model {model_name} (ID: {model_id}): {model_load_err}", exc_info=True)
                db.session.rollback() # Rollback any partial commits for this model
            finally:
                if model_instance:
                    try:
                        logger.info(f"Unloading model: {model_name}")
                        model_instance.unload()
                        logger.info(f"Model {model_name} unloaded.")
                    except Exception as unload_err:
                        logger.error(f"Error unloading model {model_name}: {unload_err}", exc_info=True)
                model_instance = None # Ensure cleanup

        # --- Task Completion ---
        logger.info(f"\n✅ Feature computation task {task_id} completed successfully for IP category: {ip_category}\n\n")

        # Trigger the next task in the chain
        logger.info(f"Triggering comparison_task for IP category: {ip_category}")
        # Check the environment variable to decide execution mode
        if os.getenv('RUN_TASKS_IN_FLASK', 'false').lower() == 'true':
            # Run the task synchronously in the current process
            # Optional: Log synchronous execution
            # from flask import current_app # Import if using logger
            # current_app.logger.info(f"Running task {comparison_task.name} synchronously.")
            comparison_task.run(ip_category=ip_category) # Run synchronously
            logger.info(f"Executed comparison_task synchronously for '{ip_category}'.")
        else:
            # Queue the task asynchronously with Celery (original behavior)
            comparison_task.delay(ip_category=ip_category)
            logger.info(f"Dispatched comparison_task for '{ip_category}'.")

        if os.getenv('RUN_TASKS_IN_FLASK', 'false').lower() != 'true':
            self.update_state(state='SUCCESS', meta={'current_step': 'Completed', 'status': 'Success'})
        return {'status': 'Completed', 'message': f'Feature computation finished for {ip_category}. Comparison task triggered.'}

    except Exception as e:
        logger.error(f"❌ Feature computation task {task_id} failed for IP category: {ip_category}: {e}", exc_info=True)
        # Mark task as failed
        if os.getenv('RUN_TASKS_IN_FLASK', 'false').lower() != 'true':
            self.update_state(state='FAILURE', meta={
                'current_step': 'Failed',
                'status': 'Task failed',
                'error': str(e)
            })
        # Optional: Raise an exception to trigger Celery's retry mechanisms if configured
        # from celery.exceptions import Ignore
        # raise Ignore() # Or just let it fail
        # Consider more specific exception handling or cleanup if needed
        return {'status': 'Failed', 'message': str(e)}