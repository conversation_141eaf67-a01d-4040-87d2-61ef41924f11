services:
  frontend:
    build:
      context: .
      dockerfile: frontend/Dockerfile
    ports:
      - "9080:80"
    depends_on:
      - backend

  backend:
    build:
      context: .
      dockerfile: backend/Dockerfile
    ports:
      - "5900:5000"
    volumes:
      - ./pictures:/app/Data/pictures
      - /docker/.vscode-server:/root/.vscode-server
      - /mnt/4tb/maidalv_data/IP TRO Data/Case Files:/Documents/Case Files
      - /mnt/4tb/maidalv_data/IP_Data:/Documents/IP
    environment:
      - FLASK_APP=backend:create_app()
      - FLASK_ENV=production
    depends_on:
      - redis
    env_file:
      - .env

  worker:
    build:
      context: .
      dockerfile: backend/Dockerfile
    volumes:
      - ./pictures:/app/Data/pictures
    command: celery -A backend.celery_app worker --loglevel=info -P eventlet
    depends_on:
      - redis
      - backend
    env_file:
      - .env

  redis:
    image: "redis:alpine"