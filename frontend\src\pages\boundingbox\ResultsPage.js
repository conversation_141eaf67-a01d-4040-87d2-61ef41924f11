import React, { useState, useEffect, useCallback } from 'react';
import {
  Container,
  Typo<PERSON>,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Button,
  CircularProgress,
  Alert,
  Grid,
  Card,
  CardContent,
  TextField,
  Pagination,
  Tooltip,
  IconButton,
  Modal, // Added for New Experiment Modal
  Paper, // Added for Modal content styling
  Stack, // Added for Modal form layout
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControlLabel,
  Checkbox
} from '@mui/material';
import { getBbPictures, getBbExperiments, updateBbResultScore, getBbExperimentConfigurations, createBbExperiment, deleteBbExperiment } from '../../services/api_bounding_box';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import RefreshIcon from '@mui/icons-material/Refresh';
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline'; // For New Experiment button
import DeleteIcon from '@mui/icons-material/Delete';

// Modal style
const modalStyle = {
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: '90%',
  maxWidth: 600,
  bgcolor: 'background.paper',
  border: '2px solid #000',
  boxShadow: 24,
  p: 4,
};

const OUTPUT_TYPES = ["Bounding Box", "Bounding Box + Segmentation Task"];

// Function to extract image dimensions from base64 data
const getImageDimensions = (base64Data) => {
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = () => {
      resolve({ width: img.width, height: img.height });
    };
    img.onerror = () => {
      resolve({ width: 0, height: 0 });
    };
    img.src = base64Data;
  });
};

const ResultsPage = () => {
  const [pictures, setPictures] = useState([]);
  const [selectedPictureId, setSelectedPictureId] = useState('');
  const [experiments, setExperiments] = useState([]);
  const [isLoadingPictures, setIsLoadingPictures] = useState(false);
  const [isLoadingExperiments, setIsLoadingExperiments] = useState(false);
  const [error, setError] = useState(null);

  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [totalExperiments, setTotalExperiments] = useState(0);

  const [experimentScores, setExperimentScores] = useState({});
  const [scoreErrors, setScoreErrors] = useState({});

  // State for New Experiment Modal
  const [isNewExperimentModalOpen, setIsNewExperimentModalOpen] = useState(false);
  const [experimentConfigurations, setExperimentConfigurations] = useState([]);
  const [selectedConfiguration, setSelectedConfiguration] = useState(null); // Store the whole config object

  const [newExperimentPrompt, setNewExperimentPrompt] = useState('');
  const [newExperimentResizeHeight, setNewExperimentResizeHeight] = useState(512);
  const [newExperimentResizeWidth, setNewExperimentResizeWidth] = useState(512);
  const [newExperimentOutputType, setNewExperimentOutputType] = useState(OUTPUT_TYPES[0]);
  const [isCreatingExperiment, setIsCreatingExperiment] = useState(false);
  const [newExperimentError, setNewExperimentError] = useState('');

  // Aspect ratio preservation state
  const [preserveAspectRatio, setPreserveAspectRatio] = useState(true);
  const [originalImageDimensions, setOriginalImageDimensions] = useState({ width: 0, height: 0 });
  const [isUpdatingDimensions, setIsUpdatingDimensions] = useState(false);

  // Delete experiment state
  const [deleteExperimentId, setDeleteExperimentId] = useState(null);
  const [isDeletingExperiment, setIsDeletingExperiment] = useState(false);
  const [isPolling, setIsPolling] = useState(false);

  // Image modal state
  const [imageModalOpen, setImageModalOpen] = useState(false);
  const [selectedImage, setSelectedImage] = useState('');

  // Constants
  const EXPERIMENTS_PER_PAGE = 5;


  const fetchPictures = useCallback(() => {
    setIsLoadingPictures(true);
    setError(null);
    // Fetch all pictures by setting a high per_page value and page 1
    getBbPictures(1, '', 1000) // Fetch up to 1000 images to show all available
      .then(data => {
        // getBbPictures returns an object with a 'pictures' property
        if (data && Array.isArray(data.pictures)) {
          setPictures(data.pictures);
        } else if (Array.isArray(data)) {
          // Fallback in case the API returns a direct array
          setPictures(data);
        } else {
          console.warn('getBbPictures returned unexpected data format:', data);
          setPictures([]);
        }
      })
      .catch(err => {
        console.error('Failed to load pictures:', err);
        setError('Failed to load pictures.');
        setPictures([]); // Ensure pictures is always an array
      })
      .finally(() => setIsLoadingPictures(false));
  }, []);

  useEffect(() => {
    fetchPictures();
  }, [fetchPictures]);

  const fetchExperiments = useCallback((pictureId, page) => {
    if (!pictureId) {
        setExperiments([]); setTotalPages(0); setTotalExperiments(0); return;
    };
    setIsLoadingExperiments(true); setError(null);
    getBbExperiments(pictureId, page, EXPERIMENTS_PER_PAGE)
      .then(data => {
        setExperiments(data.experiments || []);
        setTotalPages(data.totalPages || 0);
        setCurrentPage(data.currentPage || 1);
        setTotalExperiments(data.totalExperiments || 0);
        const initialScores = {};
        (data.experiments || []).forEach(exp => (exp.results || []).forEach(res => {
          if (res.score !== null && res.score !== undefined) initialScores[res.id] = res.score;
        }));
        setExperimentScores(prev => ({...prev, ...initialScores}));
      })
      .catch(err => {
        setError(`Failed to load experiments.`);
        setExperiments([]); setTotalPages(0); setTotalExperiments(0);
      })
      .finally(() => setIsLoadingExperiments(false));
  }, []);

  useEffect(() => {
    if (selectedPictureId) fetchExperiments(selectedPictureId, currentPage);
    else { setExperiments([]); setTotalPages(0); setTotalExperiments(0); }
  }, [selectedPictureId, currentPage, fetchExperiments]);

  // Auto-polling for pending/processing results
  useEffect(() => {
    let pollInterval;

    // Check if any experiments have pending or processing results
    const hasPendingResults = experiments.some(exp =>
      exp.results && exp.results.some(result =>
        result.status === 'pending' || result.status === 'processing'
      )
    );

    if (hasPendingResults && selectedPictureId) {
      console.log('Starting auto-polling for pending results...');
      setIsPolling(true);
      pollInterval = setInterval(() => {
        console.log('Polling for experiment updates...');
        fetchExperiments(selectedPictureId, currentPage);
      }, 10000); // Poll every 10 seconds
    } else {
      setIsPolling(false);
    }

    return () => {
      if (pollInterval) {
        console.log('Stopping auto-polling');
        clearInterval(pollInterval);
        setIsPolling(false);
      }
    };
  }, [experiments, selectedPictureId, currentPage, fetchExperiments]);

  const fetchExpConfigs = useCallback(async () => {
    try {
        const configs = await getBbExperimentConfigurations();
        setExperimentConfigurations(configs || []);
    } catch (err) {
        console.error("Failed to fetch experiment configurations:", err);
        // Optionally set an error state for config loading
    }
  }, []);

  useEffect(() => {
    // Fetch configurations when the page loads or modal is about to open
    // For simplicity, fetching on page load for now.
    fetchExpConfigs();
  }, [fetchExpConfigs]);


  const handlePictureChange = async (event) => {
    const pictureId = event.target.value;
    setSelectedPictureId(pictureId);
    setCurrentPage(1); setError(null); setExperimentScores({}); setScoreErrors({});

    // Extract image dimensions for aspect ratio preservation
    if (pictureId) {
      const selectedPicture = pictures.find(pic => pic.id === parseInt(pictureId));
      if (selectedPicture && selectedPicture.image_data) {
        try {
          const dimensions = await getImageDimensions(selectedPicture.image_data);
          setOriginalImageDimensions(dimensions);
        } catch (error) {
          console.error('Failed to extract image dimensions:', error);
          setOriginalImageDimensions({ width: 0, height: 0 });
        }
      }
    } else {
      setOriginalImageDimensions({ width: 0, height: 0 });
    }
  };

  const handlePageChange = (event, value) => setCurrentPage(value);

  const handleScoreChange = async (resultId, value) => {
    const numValue = value === '' ? '' : Number(value);
    setExperimentScores(prev => ({ ...prev, [resultId]: numValue }));

    if (numValue === '' || (Number.isInteger(numValue) && numValue >= 0 && numValue <= 10)) {
      setScoreErrors(prev => ({ ...prev, [resultId]: '' }));

      // Auto-save if the score is valid and not empty
      if (numValue !== '' && Number.isInteger(numValue) && numValue >= 0 && numValue <= 10) {
        try {
          await updateBbResultScore(resultId, Number(numValue));
          // Silent save - no alert needed for auto-save
        } catch (error) {
          setScoreErrors(prev => ({ ...prev, [resultId]: 'Failed to save score' }));
        }
      }
    } else {
      setScoreErrors(prev => ({ ...prev, [resultId]: 'Score must be 0-10 (integer)' }));
    }
  };



  const formatDate = (isoString) => isoString ? new Date(isoString).toLocaleString() : 'N/A';

  // New Experiment Modal Handlers
  const handleOpenNewExperimentModal = () => {
    setNewExperimentError('');
    setSelectedConfiguration(null); // Reset selected config
    // Reset form fields to default or last selected (if desired)
    setNewExperimentPrompt('');
    setNewExperimentResizeHeight(512);
    setNewExperimentResizeWidth(512);
    setNewExperimentOutputType(OUTPUT_TYPES[0]);
    setPreserveAspectRatio(true); // Reset to preserve aspect ratio by default
    setIsNewExperimentModalOpen(true);
    if(experimentConfigurations.length === 0) fetchExpConfigs(); // Fetch if not already loaded
  };

  const handleCloseNewExperimentModal = () => setIsNewExperimentModalOpen(false);

  const handleSelectedConfigurationChange = (event) => {
    const configIndex = event.target.value; // This will be the index
    if (configIndex === "" || configIndex === undefined) {
        setSelectedConfiguration(null);
        setNewExperimentPrompt('');
        setNewExperimentResizeHeight(512);
        setNewExperimentResizeWidth(512);
        setNewExperimentOutputType(OUTPUT_TYPES[0]);
        setPreserveAspectRatio(true); // Reset to preserve aspect ratio
    } else {
        const config = experimentConfigurations[configIndex];
        setSelectedConfiguration(config); // Store the whole config object
        setNewExperimentPrompt(config.prompt);
        setNewExperimentResizeHeight(config.resize_height);
        setNewExperimentResizeWidth(config.resize_width);
        setNewExperimentOutputType(config.output_type);
        // When loading a configuration, temporarily disable aspect ratio preservation
        // since the user explicitly chose these dimensions
        setPreserveAspectRatio(false);
    }
  };

  // Handle dimension changes with aspect ratio preservation
  const handleWidthChange = (newWidth) => {
    setIsUpdatingDimensions(true);
    const width = Number(newWidth);
    setNewExperimentResizeWidth(width);

    if (preserveAspectRatio && originalImageDimensions.width > 0 && originalImageDimensions.height > 0) {
      const aspectRatio = originalImageDimensions.height / originalImageDimensions.width;
      const newHeight = Math.round(width * aspectRatio);
      setNewExperimentResizeHeight(newHeight);
    }
    setIsUpdatingDimensions(false);
  };

  const handleHeightChange = (newHeight) => {
    setIsUpdatingDimensions(true);
    const height = Number(newHeight);
    setNewExperimentResizeHeight(height);

    if (preserveAspectRatio && originalImageDimensions.width > 0 && originalImageDimensions.height > 0) {
      const aspectRatio = originalImageDimensions.width / originalImageDimensions.height;
      const newWidth = Math.round(height * aspectRatio);
      setNewExperimentResizeWidth(newWidth);
    }
    setIsUpdatingDimensions(false);
  };

  const handleCreateNewExperiment = async () => {
    if (!newExperimentPrompt.trim()) {
      setNewExperimentError("Prompt cannot be empty."); return;
    }
    if (newExperimentResizeHeight <= 0 || newExperimentResizeWidth <= 0) {
      setNewExperimentError("Resize dimensions must be positive."); return;
    }
    setIsCreatingExperiment(true); setNewExperimentError('');
    try {
      const experimentData = {
        picture_id: selectedPictureId,
        prompt: newExperimentPrompt,
        resize_height: parseInt(newExperimentResizeHeight, 10),
        resize_width: parseInt(newExperimentResizeWidth, 10),
        output_type: newExperimentOutputType,
      };
      const newExp = await createBbExperiment(experimentData);
      // Add to the start of the experiments list for immediate feedback
      setExperiments(prev => [newExp, ...prev]);
      setTotalExperiments(prev => prev + 1);
      // If new experiment pushes list to new page, this won't show it unless we adjust current page.
      // For simplicity, we'll just add it. A full refresh might be better.
      // Or, if on page 1 and list is not full, it appears.
      // fetchExperiments(selectedPictureId, currentPage); // Or refetch current page.
      handleCloseNewExperimentModal();
    } catch (err) {
      setNewExperimentError(`Failed to create experiment: ${err.message || 'Unknown error'}`);
    } finally {
      setIsCreatingExperiment(false);
    }
  };

  const handleDeleteExperiment = async (experimentId) => {
    setIsDeletingExperiment(true);
    setError(null);
    try {
      await deleteBbExperiment(experimentId);
      // Remove the experiment from the current list
      setExperiments(prev => prev.filter(exp => exp.id !== experimentId));
      setTotalExperiments(prev => prev - 1);
      setDeleteExperimentId(null);
      // If this was the last experiment on the current page and we're not on page 1, go back a page
      if (experiments.length === 1 && currentPage > 1) {
        setCurrentPage(prev => prev - 1);
      }
    } catch (error) {
      setError(`Failed to delete experiment: ${error.message}`);
    } finally {
      setIsDeletingExperiment(false);
    }
  };

  // Image modal handlers
  const handleImageClick = (imageUrl) => {
    setSelectedImage(imageUrl);
    setImageModalOpen(true);
  };

  const handleCloseImageModal = () => {
    setImageModalOpen(false);
    setSelectedImage('');
  };

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" gutterBottom component="h1">Results Analysis by Product Pictures</Typography>
      {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}
      <FormControl fullWidth sx={{ mb: 3 }}>
        <InputLabel id="product-image-select-label">Select Product Image</InputLabel>
        <Select labelId="product-image-select-label" value={selectedPictureId} label="Select Product Image" onChange={handlePictureChange} disabled={isLoadingPictures || pictures.length === 0}>
          {isLoadingPictures && <MenuItem value="" disabled><em>Loading images...</em></MenuItem>}
          {!isLoadingPictures && pictures.length === 0 && <MenuItem value="" disabled><em>No pictures available.</em></MenuItem>}
          {Array.isArray(pictures) && pictures.map((pic) => (
            <MenuItem key={pic.id} value={pic.id}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {pic.image_data && (
                  <img
                    src={pic.image_data}
                    alt={pic.name}
                    style={{
                      width: 40,
                      height: 40,
                      objectFit: 'cover',
                      borderRadius: 4
                    }}
                  />
                )}
                <Typography>{pic.name}</Typography>
              </Box>
            </MenuItem>
          ))}
        </Select>
      </FormControl>

      {!isLoadingPictures && pictures.length === 0 && !error && (<Typography variant="body1" sx={{ mb: 2, textAlign: 'center' }}>No product pictures found.</Typography>)}

      {/* Selected Product Image Display */}
      {selectedPictureId && (
        <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
          <Typography variant="h6" gutterBottom>Selected Product Image</Typography>
          {(() => {
            const selectedPicture = pictures.find(pic => pic.id === selectedPictureId);
            return selectedPicture ? (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                {selectedPicture.image_data && (
                  <img
                    src={selectedPicture.image_data}
                    alt={selectedPicture.name}
                    style={{
                      maxWidth: 200,
                      maxHeight: 200,
                      objectFit: 'contain',
                      border: '1px solid #ddd',
                      borderRadius: 8
                    }}
                  />
                )}
                <Box>
                  <Typography variant="subtitle1" fontWeight="bold">{selectedPicture.name}</Typography>
                  <Typography variant="body2" color="textSecondary">
                    Selected for analysis
                  </Typography>
                </Box>
              </Box>
            ) : null;
          })()}
        </Paper>
      )}

      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        {isPolling && (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <CircularProgress size={16} />
            <Typography variant="caption" color="primary">
              Auto-refreshing results...
            </Typography>
          </Box>
        )}
        {!isPolling && <Box />} {/* Spacer */}

        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="outlined"
            color="primary"
            startIcon={<RefreshIcon />}
            onClick={() => selectedPictureId && fetchExperiments(selectedPictureId, currentPage)}
            disabled={!selectedPictureId || isLoadingExperiments}
          >
            Refresh
          </Button>
          <Button variant="contained" color="primary" startIcon={<AddCircleOutlineIcon />} onClick={handleOpenNewExperimentModal} disabled={!selectedPictureId || isLoadingPictures || isLoadingExperiments}>
            + New Experiment
          </Button>
        </Box>
      </Box>

      {/* New Experiment Modal */}
      <Modal open={isNewExperimentModalOpen} onClose={handleCloseNewExperimentModal} aria-labelledby="new-experiment-modal-title">
        <Paper sx={modalStyle}>
          <Typography id="new-experiment-modal-title" variant="h6" component="h2" gutterBottom>Create New Experiment</Typography>
          {newExperimentError && <Alert severity="error" sx={{mb:2}}>{newExperimentError}</Alert>}
          <Stack spacing={2}>
            <FormControl fullWidth>
              <InputLabel id="select-config-label">Select Existing Configuration (Optional)</InputLabel>
              <Select
                labelId="select-config-label"
                value={selectedConfiguration ? experimentConfigurations.indexOf(selectedConfiguration) : ""}
                label="Select Existing Configuration (Optional)"
                onChange={handleSelectedConfigurationChange}
              >
                <MenuItem value=""><em>None - Manual Input</em></MenuItem>
                {experimentConfigurations.map((config, index) => (
                  <MenuItem key={index} value={index}>
                    Prompt: {config.prompt && config.prompt.length > 30 ? config.prompt.substring(0,30) + '...' : config.prompt} | Size: {config.resize_width}x{config.resize_height} | Type: {config.output_type}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <TextField
              label="Prompt"
              value={newExperimentPrompt}
              onChange={(e) => setNewExperimentPrompt(e.target.value)}
              fullWidth
              required
              placeholder="e.g., 'Find all objects in this image' or 'Detect all products and items'"
              helperText="Be specific about what you want to detect. Vague prompts like 'sticker' may not work well."
            />
            {/* Aspect Ratio Preservation Controls */}
            <Box sx={{ mb: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={preserveAspectRatio}
                      onChange={(e) => setPreserveAspectRatio(e.target.checked)}
                    />
                  }
                  label="Preserve original aspect ratio"
                />
                <Button
                  size="small"
                  variant="outlined"
                  onClick={() => {
                    const size = Math.max(newExperimentResizeWidth, newExperimentResizeHeight);
                    setNewExperimentResizeWidth(size);
                    setNewExperimentResizeHeight(size);
                    setPreserveAspectRatio(false);
                  }}
                  sx={{ ml: 1 }}
                >
                  Make Square (Better Performance)
                </Button>
              </Box>
              {originalImageDimensions.width > 0 && originalImageDimensions.height > 0 && (
                <Box>
                  <Typography variant="caption" color="textSecondary" sx={{ display: 'block', mt: 0.5 }}>
                    Original dimensions: {originalImageDimensions.width} × {originalImageDimensions.height}
                    {preserveAspectRatio && ` (ratio: ${(originalImageDimensions.width / originalImageDimensions.height).toFixed(2)}:1)`}
                  </Typography>
                  {preserveAspectRatio && (
                    <Typography variant="caption" color="warning.main" sx={{ display: 'block', mt: 0.5, fontStyle: 'italic' }}>
                      ⚠️ Note: Square images (1:1 ratio) often produce better bounding box results
                    </Typography>
                  )}
                </Box>
              )}
            </Box>

            <Grid container spacing={2}>
                <Grid item xs={6}>
                    <TextField
                      label="Resize Width"
                      type="number"
                      value={newExperimentResizeWidth}
                      onChange={(e) => !isUpdatingDimensions && handleWidthChange(e.target.value)}
                      fullWidth
                      required
                      helperText={preserveAspectRatio && originalImageDimensions.width > 0 ? "Height will auto-adjust" : ""}
                    />
                </Grid>
                <Grid item xs={6}>
                    <TextField
                      label="Resize Height"
                      type="number"
                      value={newExperimentResizeHeight}
                      onChange={(e) => !isUpdatingDimensions && handleHeightChange(e.target.value)}
                      fullWidth
                      required
                      helperText={preserveAspectRatio && originalImageDimensions.height > 0 ? "Width will auto-adjust" : ""}
                    />
                </Grid>
            </Grid>
            <FormControl fullWidth required>
                <InputLabel id="output-type-label">Output Type</InputLabel>
                <Select labelId="output-type-label" value={newExperimentOutputType} label="Output Type" onChange={(e) => setNewExperimentOutputType(e.target.value)}>
                    {OUTPUT_TYPES.map(type => (<MenuItem key={type} value={type}>{type}</MenuItem>))}
                </Select>
            </FormControl>
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1, mt: 2 }}>
              <Button onClick={handleCloseNewExperimentModal} disabled={isCreatingExperiment}>Cancel</Button>
              <Button variant="contained" onClick={handleCreateNewExperiment} disabled={isCreatingExperiment || !newExperimentPrompt.trim() || newExperimentResizeHeight <=0 || newExperimentResizeWidth <=0}>
                {isCreatingExperiment ? <CircularProgress size={24} /> : "Add"}
              </Button>
            </Box>
          </Stack>
        </Paper>
      </Modal>

      {/* Experiment List */}
      {isLoadingExperiments && <Box sx={{display: 'flex', justifyContent: 'center', my: 3}}><CircularProgress /></Box>}
      {!isLoadingExperiments && selectedPictureId && experiments.length === 0 && !error && (<Typography variant="body1" sx={{ textAlign: 'center', mt: 2 }}>No experiments found. Create one!</Typography>)}

      {experiments.map((exp, index) => (
        <Card key={exp.id} sx={{ mb: 4 }}> {/* Increased margin bottom from 3 to 4 */}
          <CardContent sx={{ pb: 3 }}> {/* Added padding bottom to card content */}
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
              <Box>
                <Typography variant="h6" gutterBottom>
                  Experiment {totalExperiments - ((currentPage - 1) * EXPERIMENTS_PER_PAGE) - index}
                  <Typography variant="caption" sx={{ml: 1}}>Created: {formatDate(exp.created_at)}</Typography>
                </Typography>
              </Box>
              <Tooltip title="Delete Experiment">
                <IconButton
                  color="error"
                  onClick={() => setDeleteExperimentId(exp.id)}
                  disabled={isDeletingExperiment}
                  size="small"
                >
                  <DeleteIcon />
                </IconButton>
              </Tooltip>
            </Box>
            <Box sx={{ display: 'flex', gap: 3, mb: 3, flexWrap: 'wrap' }}> {/* Increased margin bottom from 2 to 3 */}
              <Typography variant="body2"><strong>Prompt:</strong> {exp.prompt}</Typography>
              <Typography variant="body2"><strong>Dimensions:</strong> {exp.resize_width}w x {exp.resize_height}h</Typography>
              <Typography variant="body2"><strong>Output Type:</strong> {exp.output_type}</Typography>
            </Box>
            <Typography variant="subtitle1" sx={{mt: 2, mb: 1}}>Model Outputs:</Typography>
            <Typography variant="body2" color="text.secondary" sx={{mb: 2, fontStyle: 'italic'}}>
              You can rate both successful and failed results. For failures, rate the error handling quality (0=poor error message, 10=helpful error message).
            </Typography>
            <Grid container spacing={3}> {/* Increased spacing from 2 to 3 */}
              {(exp.results || []).map((result) => (
                <Grid item xs={12} sm={6} md={4} key={result.id} sx={{ mb: 2 }}> {/* Added margin bottom to grid items */}
                  <Paper elevation={2} sx={{
                    p: 2,
                    height: '100%',
                    minHeight: 280, /* Added minimum height to ensure consistent card heights */
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'space-between'
                  }}>
                    <Typography variant="subtitle2" gutterBottom>{result.model_name}</Typography>
                    <Box sx={{
                      flexGrow: 1,
                      display: 'flex',
                      flexDirection:'column',
                      alignItems: 'center',
                      justifyContent: 'center',
                      minHeight: 180,
                      mb: 2 /* Increased margin bottom from 1 to 2 */
                    }}>
                      {result.status === 'success' && result.output_image_path && (
                        <Box sx={{ width: '100%', textAlign: 'center' }}>
                          <img
                            src={result.output_image_path}
                            alt={`Output for ${result.model_name}`}
                            style={{
                              maxWidth: '100%',
                              maxHeight: '150px',
                              objectFit: 'contain',
                              cursor: 'pointer'
                            }}
                            onClick={() => handleImageClick(result.output_image_path)}
                          />
                          {/* Display segmentation masks if available */}
                          {result.segmentation_masks && (() => {
                            try {
                              const masks = JSON.parse(result.segmentation_masks);
                              if (masks && masks.length > 0) {
                                return (
                                  <Box sx={{ mt: 1 }}>
                                    <Typography variant="caption" color="textSecondary" sx={{ display: 'block', mb: 1 }}>
                                      Segmentation Masks ({masks.length})
                                    </Typography>
                                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, justifyContent: 'center' }}>
                                      {masks.map((mask, index) => (
                                        <Box key={index} sx={{ textAlign: 'center' }}>
                                          <img
                                            src={mask.mask_base64}
                                            alt={`Mask: ${mask.label}`}
                                            style={{
                                              width: '40px',
                                              height: '40px',
                                              objectFit: 'contain',
                                              border: '1px solid #ddd',
                                              borderRadius: '4px',
                                              cursor: 'pointer'
                                            }}
                                            onClick={() => handleImageClick(mask.mask_base64)}
                                          />
                                          <Typography variant="caption" sx={{ display: 'block', fontSize: '0.6rem' }}>
                                            {mask.label}
                                          </Typography>
                                        </Box>
                                      ))}
                                    </Box>
                                  </Box>
                                );
                              }
                            } catch (e) {
                              console.error('Error parsing segmentation masks:', e);
                            }
                            return null;
                          })()}
                        </Box>
                      )}
                      {result.status === 'success' && !result.output_image_path && (<Typography variant="caption" color="textSecondary">Image path missing</Typography>)}
                      {result.status === 'failed' && (
                        <Box sx={{textAlign: 'center', p: 1}}>
                          <ErrorOutlineIcon color="error" sx={{ fontSize: 40 }} />
                          <Typography variant="body2" color="error" sx={{ fontWeight: 'bold', mb: 1 }}>
                            Failed
                          </Typography>
                          {result.error_message && (
                            <Typography
                              variant="caption"
                              color="error"
                              sx={{
                                display: 'block',
                                fontSize: '0.75rem',
                                lineHeight: 1.2,
                                wordBreak: 'break-word',
                                maxWidth: '100%'
                              }}
                            >
                              {result.error_message}
                            </Typography>
                          )}
                        </Box>
                      )}
                      {(result.status === 'processing' || result.status === 'pending') && (<Box sx={{textAlign: 'center'}}><CircularProgress size={30} /><Typography variant="body2" sx={{mt:1}}>{result.status === 'pending' ? 'Pending...' : 'Processing...'}</Typography></Box>)}
                    </Box>
                    {/* Allow scoring for both successful and failed results */}
                    {(result.status === 'success' || result.status === 'failed') && (
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 'auto' }}>
                        <TextField
                          label={result.status === 'failed' ? "Score (0=worst, 10=best)" : "Score (0-10)"}
                          type="number"
                          variant="outlined"
                          size="small"
                          value={experimentScores[result.id] === undefined ? '' : experimentScores[result.id]}
                          onChange={(e) => handleScoreChange(result.id, e.target.value)}
                          error={!!scoreErrors[result.id]}
                          helperText={
                            scoreErrors[result.id] ||
                            (experimentScores[result.id] !== undefined && experimentScores[result.id] !== '' ? 'Auto-saved' :
                            result.status === 'failed' ? 'Rate error handling quality' : '')
                          }
                          inputProps={{ min: 0, max: 10, step: 1 }}
                          sx={{
                            flexGrow: 1,
                            '& .MuiOutlinedInput-root': {
                              backgroundColor: result.status === 'failed' ? 'rgba(255, 0, 0, 0.05)' : 'inherit'
                            }
                          }}
                        />
                      </Box>
                    )}
                  </Paper>
                </Grid>
              ))}
            </Grid>
          </CardContent>
        </Card>
      ))}

      {totalPages > 1 && (<Box sx={{ display: 'flex', justifyContent: 'center', mt: 3, mb: 2 }}><Pagination count={totalPages} page={currentPage} onChange={handlePageChange} color="primary" disabled={isLoadingExperiments}/></Box>)}

      {/* Delete Experiment Confirmation Dialog */}
      <Dialog open={deleteExperimentId !== null} onClose={() => setDeleteExperimentId(null)}>
        <DialogTitle>Delete Experiment</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete this experiment? This will also delete all associated results and cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteExperimentId(null)}>Cancel</Button>
          <Button
            onClick={() => handleDeleteExperiment(deleteExperimentId)}
            color="error"
            variant="contained"
            disabled={isDeletingExperiment}
          >
            {isDeletingExperiment ? <CircularProgress size={20} /> : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Image Enlarge Modal */}
      <Modal
        open={imageModalOpen}
        onClose={handleCloseImageModal}
        aria-labelledby="enlarge-image-modal-title"
        aria-describedby="enlarge-image-modal-description"
      >
        <Box sx={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          bgcolor: 'background.paper',
          boxShadow: 24,
          p: 2,
          outline: 'none',
          maxWidth: '90vw',
          maxHeight: '90vh',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
          <img
            src={selectedImage}
            alt="Enlarged view"
            style={{
              maxWidth: '100%',
              maxHeight: '100%',
              objectFit: 'contain'
            }}
          />
        </Box>
      </Modal>
    </Container>
  );
};

export default ResultsPage;
