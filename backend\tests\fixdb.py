# backend/admin_scripts/cleanup_raw_descriptors.py

import sys
import os
sys.path.append(os.getcwd()) # Add project root to Python path

import logging
import pickle
from sqlalchemy import select, delete
from sqlalchemy.orm import Session

# Configure logging early
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(name)s - %(message)s')
logger = logging.getLogger(__name__)

try:
    logger.info("Importing necessary modules for cleanup script...")
    from backend import create_app
    from backend.extensions import db
    from backend.database.models import ModelTestsFeatureStorage, ModelTestsFeatureStatus, ModelTestsModel
    from backend.utils.retry_utils import execute_with_retry, DB_RETRYABLE_EXCEPTIONS
    logger.info("Modules imported successfully.")
except ImportError as e:
    logger.error(f"ImportError occurred during initial imports: {e}", exc_info=True)
    logger.error("Please ensure the script is run from the project root directory (ModelTestsWorkbench) "
                 "or that the backend package is correctly installed/discoverable.")
    sys.exit(1)


def cleanup_raw_binary_descriptors(db_session: Session):
    """
    Iterates through ModelTestsFeatureStorage entries for 'descriptor' models,
    attempts to unpickle features, and deletes entries (and corresponding statuses)
    if unpickling fails with an error indicative of raw binary data.
    """
    logger.info("Starting cleanup of raw binary descriptor features...")

    # Query to select feature storage entries for models of type 'descriptor'
    stmt = (
        select(ModelTestsFeatureStorage)
        .join(ModelTestsModel, ModelTestsFeatureStorage.model_id == ModelTestsModel.model_id)
        .where(ModelTestsModel.model_type == 'descriptor')
    )

    logger.info("Fetching descriptor feature entries from database...")
    try:
        feature_entries_to_check = execute_with_retry(
            lambda: db_session.execute(stmt).scalars().all(),
            DB_RETRYABLE_EXCEPTIONS,
            session=db_session,
            operation_name="fetch descriptor feature entries"
        )
    except Exception as e:
        logger.error(f"Failed to fetch feature entries: {e}", exc_info=True)
        return

    logger.info(f"Found {len(feature_entries_to_check)} descriptor feature entries to check.")

    deleted_storage_count = 0
    deleted_status_count = 0
    problematic_entries_details = []
    COMMIT_BATCH_SIZE = 500  # Commit after every 500 problematic entries processed
    processed_in_current_batch = 0

    if not feature_entries_to_check:
        logger.info("No descriptor feature entries found. Cleanup not needed.")
        return

    for feature_entry in feature_entries_to_check:
        is_problematic_and_staged_for_deletion = False
        try:
            pickle.loads(feature_entry.features)
        except pickle.UnpicklingError as e:
            if "invalid load key" in str(e).lower() or \
               "could not find mark" in str(e).lower() or \
               "opcode" in str(e).lower():

                logger.warning(
                    f"Problematic feature data found (likely raw binary) for "
                    f"image_id={feature_entry.image_id}, model_id={feature_entry.model_id}. Error: {e}"
                )
                problematic_entries_details.append({
                    'image_id': feature_entry.image_id,
                    'model_id': feature_entry.model_id,
                    'error': str(e)
                })
                # This entry is problematic, will attempt deletion

                try:
                    logger.info(f"Executing delete for ModelTestsFeatureStorage entry: image_id={feature_entry.image_id}, model_id={feature_entry.model_id}")
                    storage_delete_stmt = delete(ModelTestsFeatureStorage).where(
                        ModelTestsFeatureStorage.image_id == feature_entry.image_id,
                        ModelTestsFeatureStorage.model_id == feature_entry.model_id
                    )
                    storage_delete_result = execute_with_retry(
                        lambda: db_session.execute(storage_delete_stmt),
                        DB_RETRYABLE_EXCEPTIONS,
                        session=db_session,
                        operation_name=f"delete feature storage for {feature_entry.image_id}/{feature_entry.model_id}"
                    )
                    if storage_delete_result and storage_delete_result.rowcount > 0:
                        # Increment count only if deletion was successful
                        deleted_storage_count += storage_delete_result.rowcount 
                        is_problematic_and_staged_for_deletion = True # Mark as successfully staged

                    logger.info(f"Executing delete for ModelTestsFeatureStatus entry: image_id={feature_entry.image_id}, model_id={feature_entry.model_id}")
                    status_delete_stmt = delete(ModelTestsFeatureStatus).where(
                        ModelTestsFeatureStatus.image_id == feature_entry.image_id,
                        ModelTestsFeatureStatus.model_id == feature_entry.model_id
                    )
                    result = execute_with_retry(
                        lambda: db_session.execute(status_delete_stmt),
                        DB_RETRYABLE_EXCEPTIONS,
                        session=db_session,
                        operation_name=f"delete feature status for {feature_entry.image_id}/{feature_entry.model_id}"
                    )
                    if result and result.rowcount > 0:
                        # Increment count only if deletion was successful
                        deleted_status_count += result.rowcount 
                        # is_problematic_and_staged_for_deletion is already true if storage was deleted
                    elif result is None:
                        logger.warning(f"Feature status deletion for {feature_entry.image_id}/{feature_entry.model_id} returned None result. Assuming 0 rows affected.")
                    else:
                        logger.info(f"ModelTestsFeatureStatus deletion for image_id={feature_entry.image_id}, model_id={feature_entry.model_id} affected {result.rowcount} rows.")

                except Exception as del_e:
                    logger.error(
                        f"Failed to stage/execute deletions for image_id={feature_entry.image_id}, model_id={feature_entry.model_id}: {del_e}",
                        exc_info=True
                    )
                    # If deletion fails, we should rollback and stop to avoid partial commits.
                    logger.error("Rolling back session due to deletion error and stopping script.")
                    db_session.rollback()
                    # This rollback affects changes since the last periodic commit.
                    return # Stop the entire script
            else:
                logger.info(
                    f"Feature for image_id={feature_entry.image_id}, model_id={feature_entry.model_id} "
                    f"had an UnpicklingError but not the 'invalid load key' type: {e}. Skipping deletion."
                )
        except Exception as ex:
            logger.error(
                f"Unexpected error checking feature for image_id={feature_entry.image_id}, model_id={feature_entry.model_id}: {ex}",
                exc_info=True
            )

        if is_problematic_and_staged_for_deletion:
            processed_in_current_batch += 1

        # --- Periodic Commit Logic ---
        if processed_in_current_batch > 0 and processed_in_current_batch % COMMIT_BATCH_SIZE == 0:
            try:
                logger.info(f"Batch size of {COMMIT_BATCH_SIZE} reached. Committing {processed_in_current_batch} staged deletions.")
                execute_with_retry(
                    db_session.commit,
                    DB_RETRYABLE_EXCEPTIONS,
                    session=db_session,
                    operation_name="periodic descriptor cleanup commit"
                )
                logger.info(f"Periodic commit successful. {deleted_storage_count} total storage entries, {deleted_status_count} total status entries deleted so far.")
                processed_in_current_batch = 0 # Reset counter for the new batch
            except Exception as commit_e_periodic:
                logger.error(f"Failed to commit periodic batch: {commit_e_periodic}", exc_info=True)
                logger.info("Attempting to rollback session due to periodic commit failure...")
                try:
                    db_session.rollback()
                    logger.info("Session rollback successful after periodic commit failure.")
                except Exception as rb_e_periodic:
                    logger.error(f"Session rollback failed after periodic commit failure: {rb_e_periodic}", exc_info=True)
                logger.error("Stopping script due to periodic commit failure.")
                return # Stop the entire script

    # --- Final Commit for any remaining items in the last batch ---
    if processed_in_current_batch > 0:
        try:
            logger.info(f"Committing final batch of {processed_in_current_batch} staged deletions to the database.")
            execute_with_retry(
                db_session.commit,
                DB_RETRYABLE_EXCEPTIONS,
                session=db_session,
                operation_name="final descriptor cleanup commit"
            )
            logger.info(f"Final database commit successful. Total storage entries deleted: {deleted_storage_count}, total status entries deleted: {deleted_status_count}.")
        except Exception as commit_e:
            logger.error(f"Failed to commit changes to the database: {commit_e}", exc_info=True)
            logger.info("Attempting to rollback session due to commit failure...")
            try:
                db_session.rollback()
                logger.info("Session rollback successful after commit failure.")
            except Exception as rb_e:
                logger.error(f"Session rollback failed after commit failure: {rb_e}", exc_info=True)
    elif deleted_storage_count > 0: # Implies processed_in_current_batch is 0, all committed in batches
        logger.info("All identified problematic entries were processed and committed in batches. No final commit needed for new items.")
    else: # No deletions were made at all
        logger.info("No problematic descriptor entries were identified and staged for deletion in this run. No database commit needed.")

    logger.info(f"Cleanup summary: {deleted_storage_count} ModelTestsFeatureStorage entries deleted.")
    logger.info(f"Cleanup summary: {deleted_status_count} ModelTestsFeatureStatus entries deleted.")
    if problematic_entries_details:
        logger.info("Details of problematic entries that were targeted for deletion:")
        for detail in problematic_entries_details:
            logger.info(f"  - Image ID: {detail['image_id']}, Model ID: {detail['model_id']}, Error: {detail['error']}")


if __name__ == "__main__":
    logger.info("Starting descriptor cleanup script...")
    flask_app = None
    try:
        flask_app = create_app()
        logger.info("Flask app created for context.")
    except Exception as e:
        logger.error(f"Failed to create Flask app: {e}", exc_info=True)
        sys.exit(1)

    if flask_app:
        with flask_app.app_context():
            logger.info("Application context pushed.")
            try:
                cleanup_raw_binary_descriptors(db.session)
                logger.info("Descriptor cleanup process finished.")
            except Exception as e:
                logger.error(f"An error occurred during the cleanup process: {e}", exc_info=True)
                try:
                    db.session.rollback()
                    logger.info("Rolled back database session due to error in cleanup process.")
                except Exception as rb_err:
                    logger.error(f"Failed to rollback database session: {rb_err}", exc_info=True)
            finally:
                logger.info("Application context will be popped.")
    else:
        logger.error("Flask app instance is None. Cannot proceed with cleanup.")

    logger.info("Descriptor cleanup script finished execution.")