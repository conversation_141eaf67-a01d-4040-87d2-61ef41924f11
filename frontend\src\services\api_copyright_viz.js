import apiClient from './api';
import axios from 'axios';
const API_URL = '/api/v1/copyright';

const handleResponse = async (response) => {
  if (!response.ok) {
    // Try to parse standardized backend error structure
    let msg = 'Something went wrong';
    try {
      const error = await response.json();
      msg = error?.error || error?.message || msg;
    } catch (_) {
      // ignore parse errors, fall back to status text
      msg = response.statusText || msg;
    }
    throw new Error(msg);
  }
  return response.json();
};

export const getCopyrightAssets = async (filters) => {
  // Normalize filters: trim plaintiff_name and drop empty values
  const normalized = {};
  Object.entries(filters || {}).forEach(([k, v]) => {
    if (v === null || v === undefined) return;
    if (typeof v === 'string') {
      const t = v.trim();
      if (t !== '') normalized[k] = t;
    } else {
      normalized[k] = v;
    }
  });

  const query = new URLSearchParams(normalized).toString();
  const response = await fetch(`${API_URL}?${query}`);
  return handleResponse(response);
};



// New streaming function for adding copyright images with progress
export const addCopyrightAssetWithProgress = (formData, onProgress, onComplete, onError) => {
  // Create a unique session ID for this upload
  const sessionId = Date.now().toString() + Math.random().toString(36).substr(2, 9);
  console.log(`[DEBUG] Starting upload session: ${sessionId}`);

  // Start the SSE connection first for processing progress
  const sseUrl = `${API_URL}/image/upload/stream?session_id=${sessionId}`;
  console.log(`[DEBUG] Establishing SSE connection to: ${sseUrl}`);
  const eventSource = new EventSource(sseUrl);

  eventSource.onopen = () => {
    console.log(`[DEBUG] SSE connection opened successfully for session: ${sessionId}`);
  };

  eventSource.onmessage = (event) => {
    try {
      console.log(`[DEBUG] SSE message received:`, event.data);
      const data = JSON.parse(event.data);
      console.log(`[DEBUG] Parsed SSE data:`, data);

      if (data.type === 'progress' || data.type === 'success' || data.type === 'error' || data.type === 'log') {
        console.log(`[DEBUG] Processing progress update:`, data);
        // Add a flag to distinguish processing progress from upload progress
        onProgress && onProgress({ ...data, phase: 'processing' });
      } else if (data.type === 'waiting') {
        console.log(`[DEBUG] SSE waiting message:`, data.message);
        // Just log waiting messages, no action needed
      } else if (data.type === 'complete') {
        console.log(`[DEBUG] SSE complete received:`, data);
        eventSource.close();
        onComplete && onComplete(data);
      } else {
        console.log(`[DEBUG] Unknown SSE message type:`, data.type, data);
      }
    } catch (err) {
      console.error('[DEBUG] Error parsing SSE data:', err);
      console.error('[DEBUG] Raw SSE data:', event.data);
      eventSource.close();
      onError && onError(err);
    }
  };

  eventSource.onerror = (error) => {
    console.error('[DEBUG] SSE error occurred:', error);
    console.error('[DEBUG] SSE readyState:', eventSource.readyState);
    console.error('[DEBUG] SSE url:', eventSource.url);
    eventSource.close();
    onError && onError(error);
  };

  // Send the form data as a POST request after establishing SSE connection
  setTimeout(() => {
    console.log(`[DEBUG] Preparing POST request for session: ${sessionId}`);

    // Add session ID to form data for the POST request
    const formDataWithSession = new FormData();
    for (let [key, value] of formData.entries()) {
      console.log(`[DEBUG] Adding form data: ${key} =`, value);
      formDataWithSession.append(key, value);
    }
    formDataWithSession.append('session_id', sessionId);
    console.log(`[DEBUG] Added session_id to form data: ${sessionId}`);

    const postUrl = `${API_URL}/image/upload/stream`;
    console.log(`[DEBUG] Sending POST request to: ${postUrl}`);

    // Use Axios for upload with progress tracking
    axios.post(postUrl, formDataWithSession, {
      onUploadProgress: (progressEvent) => {
        const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        console.log(`[DEBUG] Upload progress: ${percentCompleted}% (${progressEvent.loaded}/${progressEvent.total} bytes)`);
        onProgress && onProgress({
          type: 'upload_progress',
          phase: 'upload',
          progress: percentCompleted,
          loaded: progressEvent.loaded,
          total: progressEvent.total
        });
      }
    })
    .then(response => {
      console.log(`[DEBUG] POST request successful:`, response);
      console.log(`[DEBUG] Response status: ${response.status}`);
      console.log(`[DEBUG] Response data:`, response.data);

      if (!response.data.success) {
        console.error(`[DEBUG] Upload failed based on response data:`, response.data);
        eventSource.close();
        onError && onError(new Error(response.data.error || 'Upload failed'));
      } else {
        console.log(`[DEBUG] Upload reported as successful in response`);
      }
    })
    .catch(error => {
      console.error('[DEBUG] Error sending form data:', error);
      console.error('[DEBUG] Error details:', {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        config: error.config
      });
      eventSource.close();
      onError && onError(error);
    });
  }, 100); // Small delay to ensure SSE connection is established

  console.log(`[DEBUG] Upload session setup complete: ${sessionId}`);

  // Return an object with the eventSource and a cleanup function
  return {
    eventSource,
    close: () => {
      console.log(`[DEBUG] Closing upload session: ${sessionId}`);
      eventSource.close();
    }
  };
};

// Only updates the database, cannot be used for production flag
export const updateCopyrightsFile = async (fileId, fileData) => {
  // Update a copyrights_files record
  const response = await fetch(`${API_URL}/files/${fileId}`, {
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(fileData),
  });
  return handleResponse(response);
};

// Specific to call the production flag endpoint
export const setCopyrightFileProductionStatus = async (fileId, isProduction) => {
  const response = await fetch(`${API_URL}/files/${fileId}/set-prod`, {
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ production: isProduction }),
  });
  return handleResponse(response);
};


export const changeRegNo = async (fileId, newRegNo, replaceFilename = null, keepExisting = false) => {
  const body = {
    new_reg_no: newRegNo,
    replace_filename: replaceFilename,
    keep_existing: keepExisting,
  };

  const response = await fetch(`${API_URL}/files/${fileId}/change-regno`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(body),
  });
  return handleResponse(response);
};

export const getNextDuplicatePair = async (data = null) => {
  const options = {
    method: data ? 'POST' : 'GET',
    headers: { 'Content-Type': 'application/json' },
    body: data ? JSON.stringify(data) : undefined,
  };

  const response = await fetch(`${API_URL}/duplicates`, options);
  return handleResponse(response);
};

export const resolveDuplicatePair = async (resolutionData) => {
  const response = await fetch(`${API_URL}/duplicates/resolve`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(resolutionData),
  });
  return handleResponse(response);
};

export const getOrphans = async (category, page = 1, perPage = 50) => {
  const params = new URLSearchParams({ page: page.toString(), per_page: perPage.toString() });
  const response = await fetch(`${API_URL}/orphans/${category}?${params}`);
  return handleResponse(response);
};

export const fixOrphans = async (fixData) => {
  const response = await fetch(`${API_URL}/orphans/fix`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(fixData),
  });
  return handleResponse(response);
};

export const refreshCache = async () => {
  const response = await fetch(`${API_URL}/cache/refresh`, {
    method: 'POST',
  });
  return handleResponse(response);
};

export const getCopyrightTypes = async () => {
  const response = await fetch(`${API_URL}/types`);
  return handleResponse(response);
};

export const getCopyrightMethods = async () => {
  const response = await fetch(`${API_URL}/methods`);
  return handleResponse(response);
};

export const manageCopyrightTypes = async (typeData) => {
  const response = await fetch(`${API_URL}/types`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(typeData),
  });
  return handleResponse(response);
};

export const moveCnWebsitesFilesWithProgress = (assetIds, onProgress, onComplete, onError) => {
  const query = new URLSearchParams({ asset_ids: assetIds.join(',') }).toString();
  const eventSource = new EventSource(`${API_URL}/cn-websites-files/move/stream?${query}`);

  eventSource.onmessage = (event) => {
    try {
      const data = JSON.parse(event.data);

      if (data.type === 'progress' || data.type === 'success' || data.type === 'error' || data.type === 'log') {
        onProgress && onProgress(data);
      } else if (data.type === 'complete') {
        eventSource.close();
        onComplete && onComplete(data);
      }
    } catch (err) {
      console.error('Error parsing SSE data:', err);
      eventSource.close();
      onError && onError(err);
    }
  };

  eventSource.onerror = (error) => {
    console.error('SSE error:', error);
    // Only close if not already closed and give it a moment
    if (eventSource.readyState !== EventSource.CLOSED) {
      setTimeout(() => {
        if (eventSource.readyState !== EventSource.CLOSED) {
          eventSource.close();
        }
      }, 100);
    }
    // Only close if not already closed and give it a moment
    if (eventSource.readyState !== EventSource.CLOSED) {
      setTimeout(() => {
        if (eventSource.readyState !== EventSource.CLOSED) {
          eventSource.close();
        }
      }, 100);
    }
    onError && onError(error);
  };

  return {
    eventSource,
    close: () => eventSource.close()
  };
};

export const bulkUpdateCopyrightsFiles = async (op, ids, value) => {
  const body = { op, ids };
  if (value !== undefined) {
    body.value = value;
  }
  const response = await fetch(`${API_URL}/files/bulk`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(body),
  });
  return handleResponse(response);
};

// New streaming bulk operations for set_prod/unset_prod
export const bulkSetProdWithProgress = (assetIds, onProgress, onComplete, onError) => {
  const query = new URLSearchParams({ asset_ids: assetIds.join(',') }).toString();
  const eventSource = new EventSource(`${API_URL}/bulk/set_prod/stream?${query}`);

  eventSource.onmessage = (event) => {
    try {
      const data = JSON.parse(event.data);

      if (data.type === 'progress') {
        onProgress && onProgress(data);
      } else if (data.type === 'success') {
        onProgress && onProgress(data);
      } else if (data.type === 'error') {
        onProgress && onProgress(data);
      } else if (data.type === 'complete') {
        eventSource.close();
        onComplete && onComplete(data);
      }
    } catch (err) {
      console.error('Error parsing SSE data:', err);
      eventSource.close();
      onError && onError(err);
    }
  };

  eventSource.onerror = (error) => {
    console.error('SSE error:', error);
    eventSource.close();
    onError && onError(error);
  };

  // Return an object with the eventSource and a cleanup function
  return {
    eventSource,
    close: () => eventSource.close()
  };
};

export const bulkUnsetProdWithProgress = (assetIds, onProgress, onComplete, onError) => {
  const query = new URLSearchParams({ asset_ids: assetIds.join(',') }).toString();
  const eventSource = new EventSource(`${API_URL}/bulk/unset_prod/stream?${query}`);

  eventSource.onmessage = (event) => {
    try {
      const data = JSON.parse(event.data);

      if (data.type === 'progress') {
        onProgress && onProgress(data);
      } else if (data.type === 'success') {
        onProgress && onProgress(data);
      } else if (data.type === 'error') {
        onProgress && onProgress(data);
      } else if (data.type === 'complete') {
        eventSource.close();
        onComplete && onComplete(data);
      }
    } catch (err) {
      console.error('Error parsing SSE data:', err);
      eventSource.close();
      onError && onError(err);
    }
  };

  eventSource.onerror = (error) => {
    console.error('SSE error:', error);
    eventSource.close();
    onError && onError(error);
  };

  // Return an object with the eventSource and a cleanup function
  return {
    eventSource,
    close: () => eventSource.close()
  };
};

export const getPlaintiffRegistrations = async (plaintiffId) => {
  const response = await fetch(`${API_URL}/plaintiffs/${plaintiffId}/registrations`);
  return handleResponse(response);
};

export const splitCopyrightFile = async (payload, fileId) => {
  try {
    const response = await apiClient.post(`${API_URL}/files/${fileId}/split`, payload);
    return response.data;
  } catch (error) {
    console.error(`Error splitting copyright file with ID ${fileId}:`, error);
    throw error; // Re-throw the error so the calling function's catch block can handle it.
  }
};
