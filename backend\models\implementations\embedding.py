# backend/models/implementations/embedding.py
import logging
import tensorflow as tf
import importlib # Added for dynamic imports
import numpy as np
from backend.models.base import ImageModelBase
import torch # Keep for TimmModel and potentially ClipModel if GPU is used
import timm # Keep for TimmModel
from PIL import Image # Keep for TimmModel and add for ClipModel, KerasModel
from timm.data import resolve_data_config, create_transform # Keep for TimmModel
from sentence_transformers import SentenceTransformer # Added for ClipModel

logger = logging.getLogger(__name__)

# Versions: transformers 4.51.3 works for Jina, but 4.52.0+ does not

# --- New ClipModel using SentenceTransformers ---

class ClipModel(ImageModelBase):
    """
    Image embedding model using SentenceTransformers with a CLIP model (e.g., Jina CLIP).
    """
    # No longer hardcode model name or vector size here

    def __init__(self, model_id: str, config: dict): # Changed 'parameters' back to 'config'
        """
        Initializes the ClipModel.

        Args:
            model_id (str): The unique identifier for the model instance.
            config (dict): Configuration parameters. Expected keys:
                                - 'vector_size'
                                - 'model_name_or_path' (e.g., 'jinaai/jina-clip-v2')
        """
        super().__init__(config) # Correct: Pass only config to base
        self.model_id = model_id # Store model_id in subclass
        self._vector_size = config.get('vector_size') # Use config dict
        self.model_name_or_path = config.get('model_name_or_path') # Use config dict

        if not self._vector_size:
            raise ValueError(f"Missing required parameter 'vector_size' for ClipModel {model_id}")
        if not self.model_name_or_path:
            raise ValueError(f"Missing required parameter 'model_name_or_path' for ClipModel {model_id}")

        self._vector_size = int(self._vector_size)
        self.model = None
        # Determine device (prefer CUDA if available)
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        logger.info(f"Initialized ClipModel (ID: {self.model_id}) "
                    f"for Model: {self.model_name_or_path}, Vector Size: {self._vector_size}, Device: {self.device}")
        # Load model immediately during initialization - consider making this optional?
        # For now, keep immediate load as per previous behavior.
        # self.load()


    def load(self):
        """
        Loads the SentenceTransformer CLIP model.
        """
        if self.model is not None:
            logger.info(f"Model {self.model_id} ({self.model_name_or_path}) is already loaded.")
            return

        try:
            logger.info(f"Loading SentenceTransformer model: {self.model_name_or_path} to device: {self.device}")
            # Load the model onto the specified device, adding trust_remote_code=True
            self.model = SentenceTransformer(
                self.model_name_or_path,
                device=self.device,
                trust_remote_code=True # Add this based on user feedback
            )
            self.model.eval() # Set to evaluation mode
            logger.info(f"Successfully loaded model {self.model_id} ({self.model_name_or_path})")

        except AttributeError as ae:
            # Handle the specific case of missing '_initialize_weights' method (due to newer versions of SentenceTransformers module)
            if '_initialize_weights' in str(ae):
                logger.warning(f"Jina CLIP compatibility workaround: {ae}. Skipping weight initialization.")
                # The model is likely already loaded, just skip the error
                self.model.eval()
            else:
                logger.error(f"Error loading model {self.model_id} ({self.model_name_or_path}): {ae}", exc_info=True)
                self.model = None
                raise
        except Exception as e:
            logger.error(f"Error loading model {self.model_id} ({self.model_name_or_path}): {e}", exc_info=True)
            self.model = None # Ensure model is None if loading failed
            raise # Re-raise the exception to signal failure

    def unload(self):
        """
        Unloads the SentenceTransformer model.
        """
        if self.model is not None:
            logger.info(f"Unloading model {self.model_id} ({self.model_name_or_path})...")
            # SentenceTransformer doesn't have an explicit unload, rely on garbage collection
            self.model = None
            if torch.cuda.is_available():
                torch.cuda.empty_cache() # Clear GPU cache if applicable
            logger.info(f"Model {self.model_id} unloaded.")
        else:
            logger.info(f"Model {self.model_id} is not loaded, nothing to unload.")

    def get_model_type(self) -> str:
        return 'embedding'

    @property
    def vector_size(self) -> int:
        if self._vector_size is None:
             raise ValueError(f"Vector size not set for model {self.model_id}")
        return self._vector_size

    def preprocess(self, image_path: str) -> Image.Image:
        """
        Loads an image using PIL. SentenceTransformer's encode method handles preprocessing internally.

        Args:
            image_path (str): The path to the image file.

        Returns:
            PIL.Image.Image: The loaded image object.

        Raises:
            FileNotFoundError: If the image file does not exist.
            Exception: For other image loading errors.
        """
        try:
            logger.debug(f"Preprocessing (loading) image for {self.model_name_or_path}: {image_path}")
            # Load image using PIL, ensure RGB
            img = Image.open(image_path).convert('RGB')
            logger.debug(f"Image loaded successfully: {image_path}")
            return img

        except FileNotFoundError:
             logger.error(f"Image file not found at path: {image_path}")
             raise
        except Exception as e:
            logger.error(f"Unexpected error during image loading of {image_path}: {e}", exc_info=True)
            raise

    def compute_features(self, preprocessed_image: Image.Image) -> np.ndarray:
        """
        Computes the feature vector for the preprocessed image (PIL Image).

        Args:
            preprocessed_image (PIL.Image.Image): The output from the preprocess method.

        Returns:
            np.ndarray: The computed feature vector as a NumPy array.

        Raises:
            ValueError: If the model is not loaded.
            Exception: If an error occurs during model inference.
        """
        if self.model is None:
            logger.error(f"Model {self.model_id} ({self.model_name_or_path}) is not loaded. Cannot compute features.")
            raise ValueError(f"Model {self.model_id} must be loaded before computing features.")

        try:
            logger.debug(f"Computing features for PIL image")
            # SentenceTransformer expects a list of PIL Images or image paths
            # Encode the single image
            features = self.model.encode([preprocessed_image], batch_size=1, convert_to_numpy=True, show_progress_bar=False)

            # Result should be a numpy array of shape (1, vector_size)
            features_np = features[0]

            # Ensure the output vector has the expected size
            if features_np.shape[0] != self.vector_size:
                 logger.warning(f"Model {self.model_id} produced vector of size {features_np.shape[0]}, "
                                f"but expected {self.vector_size}. Check model implementation.")
                 # Depending on strictness, you might raise an error here instead.

            logger.debug(f"Computed features shape: {features_np.shape}")
            return features_np

        except Exception as e:
            logger.error(f"Error computing features with model {self.model_id} ({self.model_name_or_path}): {e}", exc_info=True)
            raise # Re-raise the exception

    def normalize_score(self, raw_score: float) -> float:
        """
        Normalizes cosine similarity score. Assumes raw_score is similarity in [-1, 1].
        Maps it to [0, 1].
        """
        # Cosine similarity is in [-1, 1]. Map to [0, 1] where 1 is most similar.
        normalized = (float(raw_score) + 1.0) / 2.0
        normalized = max(0.0, min(1.0, normalized)) # Clamp to [0, 1]
        logger.debug(f"Normalizing raw score {raw_score} to {normalized}")
        return normalized


# --- KerasApplicationModel ---

# Mapping from model names (as strings) to Keras application modules and *default* input sizes
# Config can override input_size via input_shape parameter
KERAS_APP_CONFIG = {
    'EfficientNetB7': {'module': 'tensorflow.keras.applications.efficientnet', 'input_size': (600, 600)}, # Default B7 size
    'ResNet50': {'module': 'tensorflow.keras.applications.resnet50', 'input_size': (224, 224)},
    'VGG16': {'module': 'tensorflow.keras.applications.vgg16', 'input_size': (224, 224)},
    'VGG19': {'module': 'tensorflow.keras.applications.vgg19', 'input_size': (224, 224)},
    'EfficientNetV2L': {'module': 'tensorflow.keras.applications.efficientnet_v2', 'input_size': (480, 480)},
    'InceptionResNetV2': {'module': 'tensorflow.keras.applications.inception_resnet_v2', 'input_size': (299, 299)},
    'Xception': {'module': 'tensorflow.keras.applications.xception', 'input_size': (299, 299)},
    'ConvNeXtXLarge': {'module': 'tensorflow.keras.applications.convnext', 'input_size': (224, 224)}, # Default 224, can be 384
    'ResNet152V2': {'module': 'tensorflow.keras.applications.resnet_v2', 'input_size': (224, 224)},
    # Add other Keras models here if needed
}

class KerasApplicationModel(ImageModelBase):
    """
    Generic image embedding model using tf.keras.applications.
    """
    def __init__(self, model_id: str, config: dict): # Changed 'parameters' back to 'config'
        """
        Initializes the KerasApplicationModel.

        Args:
            model_id (str): The unique identifier for the model instance.
            config (dict): Configuration parameters for the model. # Changed 'parameters' to 'config'
                               Expected keys:
                                - 'keras_app_name' (e.g., 'EfficientNetB7', 'ResNet50')
                                - 'vector_size'
                                - 'input_shape' (optional, list/tuple [H, W, C], e.g., [480, 480, 3])
                                - 'weights' (optional, default 'imagenet')
                                - 'pooling' (optional, default 'avg')
                                - 'preprocess_func' (optional, string name like 'efficientnet', 'resnet'. If omitted, uses default from app module)
        """
        super().__init__(config) # Correct: Pass only config to base
        self.model_id = model_id # Store model_id in subclass
        self.keras_app_name = config.get('keras_app_name') # Use config dict
        self._vector_size = config.get('vector_size') # Use config dict
        self.weights = config.get('weights', 'imagenet') # Use config dict
        self.pooling = config.get('pooling', 'avg') # Use config dict
        # Get input_shape from parameters if provided
        self.input_shape_param = config.get('input_shape') # Use config dict, e.g., [480, 480, 3]
        # Get specific preprocess function name if provided
        self.preprocess_func_name = config.get('preprocess_func') # Use config dict, e.g., "efficientnet"

        if not self.keras_app_name:
            raise ValueError(f"Missing required parameter 'keras_app_name' for model {model_id}")
        if self.keras_app_name not in KERAS_APP_CONFIG:
             raise ValueError(f"Unsupported 'keras_app_name': {self.keras_app_name}. Supported: {list(KERAS_APP_CONFIG.keys())}")
        if not self._vector_size:
            raise ValueError(f"Missing required parameter 'vector_size' for model {model_id}")

        self._vector_size = int(self._vector_size)
        self.config = KERAS_APP_CONFIG[self.keras_app_name]

        # Determine input size: Use parameter if provided, otherwise use default from config
        if self.input_shape_param:
            if not isinstance(self.input_shape_param, (list, tuple)) or len(self.input_shape_param) != 3:
                 raise ValueError(f"Parameter 'input_shape' for model {model_id} must be a list/tuple of length 3 (H, W, C), got: {self.input_shape_param}")
            # Keras applications expect (height, width) for input_shape argument during loading,
            # but preprocessing needs the full shape including channels.
            self.input_size = tuple(self.input_shape_param[:2]) # (height, width)
            self.input_shape = tuple(self.input_shape_param) # (height, width, channels)
            logger.info(f"Using input_shape from parameters: {self.input_shape}")
        else:
            self.input_size = self.config['input_size'] # Default (height, width)
            self.input_shape = (*self.input_size, 3) # Assume 3 channels if not specified
            logger.info(f"Using default input_size from KERAS_APP_CONFIG: {self.input_size}")


        # Dynamically import the application and preprocessing function
        try:
            app_module = importlib.import_module(self.config['module'])
            # The actual application class name is usually the same as keras_app_name
            self.keras_application = getattr(app_module, self.keras_app_name)

            # Determine which preprocessing function to use
            if self.preprocess_func_name:
                # If a specific name is given (e.g., 'efficientnet'), look for that attribute
                # This assumes the module structure (e.g., tf.keras.applications.efficientnet.preprocess_input)
                # We might need a mapping here if names don't align perfectly.
                # For now, assume preprocess_func_name directly maps to the function name suffix.
                preprocess_func_actual_name = f"preprocess_input" # Standard name in Keras apps
                if hasattr(app_module, preprocess_func_actual_name):
                     self.preprocess_input = getattr(app_module, preprocess_func_actual_name)
                     logger.info(f"Using preprocess_input function from module {self.config['module']} based on keras_app_name.")
                else:
                     # Fallback or error if specific function not found - maybe try default?
                     logger.warning(f"Specified preprocess_func '{self.preprocess_func_name}' "
                                    f"did not directly lead to a 'preprocess_input' function in {self.config['module']}. "
                                    f"Attempting to load default 'preprocess_input'.")
                     # Try loading the default 'preprocess_input' if specific one wasn't found or logic is simple
                     self.preprocess_input = getattr(app_module, 'preprocess_input')

            else:
                # Default: load 'preprocess_input' from the application's module
                self.preprocess_input = getattr(app_module, 'preprocess_input')
                logger.info(f"Using default preprocess_input function from module {self.config['module']}.")


        except (ImportError, AttributeError) as e:
            logger.error(f"Failed to import Keras application or preprocess function for {self.keras_app_name}: {e}", exc_info=True)
            raise ValueError(f"Could not load Keras application components for {self.keras_app_name}") from e

        self.model = None
        logger.info(f"Initialized KerasApplicationModel (ID: {self.model_id}) "
                    f"for Keras App: {self.keras_app_name}, Vector Size: {self._vector_size}, "
                    f"Input Shape (H, W, C): {self.input_shape}, Pooling: {self.pooling}")


    def load(self):
        """
        Loads the specified Keras Application model using the determined input shape.
        """
        if self.model is not None:
            logger.info(f"Model {self.model_id} ({self.keras_app_name}) is already loaded.")
            return

        try:
            logger.info(f"Loading Keras Application model: {self.keras_app_name} with weights: {self.weights}")
            # Load the model without the classification head, using the determined input_shape
            base_model = self.keras_application(
                weights=self.weights,
                include_top=False,
                input_shape=self.input_shape, # Use the full (H, W, C) shape here
                pooling=self.pooling # Use pooling layer to get feature vector
            )
            base_model.trainable = False # Freeze weights for feature extraction

            self.model = base_model
            logger.info(f"Successfully loaded model {self.model_id} ({self.keras_app_name})")
            # self.model.summary(print_fn=logger.info) # Log model summary - Commented out to reduce verbosity

        except Exception as e:
            logger.error(f"Error loading model {self.model_id} ({self.keras_app_name}): {e}", exc_info=True)
            self.model = None # Ensure model is None if loading failed
            raise # Re-raise the exception to signal failure

    def unload(self):
        """
        Unloads the model and clears the Keras session.
        """
        if self.model is not None:
            logger.info(f"Unloading model {self.model_id} ({self.keras_app_name})...")
            self.model = None
            tf.keras.backend.clear_session()
            logger.info(f"Model {self.model_id} unloaded and Keras session cleared.")
        else:
            logger.info(f"Model {self.model_id} is not loaded, nothing to unload.")

    def get_model_type(self) -> str:
        return 'embedding'

    @property
    def vector_size(self) -> int:
        if self._vector_size is None:
             raise ValueError(f"Vector size not set for model {self.model_id}")
        return self._vector_size


    def preprocess(self, image_path: str) -> tf.Tensor:
        """
        Loads, decodes, resizes (to self.input_size H,W), and preprocesses an image
        using the application-specific function.
        """
        try:
            logger.debug(f"Preprocessing image for {self.keras_app_name}: {image_path}")
            # Load image using PIL first to handle formats like WebP
            img_pil = Image.open(image_path).convert('RGB')
            # Convert PIL image to TensorFlow tensor
            img = tf.convert_to_tensor(np.array(img_pil), dtype=tf.float32)

            # Resize the image using the determined (height, width)
            # Ensure resizing uses a method compatible with TF tensors
            img = tf.image.resize(img, self.input_size, method=tf.image.ResizeMethod.BILINEAR) # Use H, W here

            # Add batch dimension BEFORE preprocessing
            img = tf.expand_dims(img, axis=0)

            # Apply the model-specific preprocessing
            img = self.preprocess_input(img)

            logger.debug(f"Preprocessing complete for {image_path}. Output shape: {img.shape}")
            return img

        except tf.errors.NotFoundError as e:
             logger.error(f"Image file not found at path: {image_path}")
             raise FileNotFoundError(f"Image file not found: {image_path}") from e
        except tf.errors.OpError as e:
            logger.error(f"Error processing image file {image_path}: {e}", exc_info=True)
            raise
        except Exception as e:
            logger.error(f"Unexpected error during preprocessing of {image_path}: {e}", exc_info=True)
            raise

    def compute_features(self, preprocessed_image: tf.Tensor) -> np.ndarray:
        """
        Computes the feature vector for the preprocessed image.
        """
        if self.model is None:
            logger.error(f"Model {self.model_id} ({self.keras_app_name}) is not loaded. Cannot compute features.")
            raise ValueError(f"Model {self.model_id} must be loaded before computing features.")

        try:
            logger.debug(f"Computing features for input shape: {preprocessed_image.shape}")
            features_np = self.model.predict(preprocessed_image, verbose=0) # Ensure inference mode
            features_np = np.squeeze(features_np) # Remove batch dim if size 1

            if features_np.ndim > 0 and features_np.shape[0] != self.vector_size:
                 logger.warning(f"Model {self.model_id} ({self.keras_app_name}) produced vector of size {features_np.shape[0]}, "
                                f"but expected {self.vector_size}. Check model/config pooling layer.")
                 # Consider raising an error for strictness

            logger.debug(f"Computed features shape: {features_np.shape}")
            return features_np

        except Exception as e:
            logger.error(f"Error computing features with model {self.model_id} ({self.keras_app_name}): {e}", exc_info=True)
            raise

    def normalize_score(self, raw_score: float) -> float:
        """
        Normalizes cosine similarity score. Assumes raw_score is similarity in [-1, 1].
        Maps it to [0, 1].
        """
        # Cosine similarity is in [-1, 1]. Map to [0, 1] where 1 is most similar.
        normalized = (float(raw_score) + 1.0) / 2.0
        normalized = max(0.0, min(1.0, normalized)) # Clamp to [0, 1]
        logger.debug(f"Normalizing raw score {raw_score} to {normalized}")
        return normalized


# --- TimmModel ---

class TimmModel(ImageModelBase):
    """
    Generic image embedding model using PyTorch models from the 'timm' library.
    """
    def __init__(self, model_id: str, config: dict): # Changed 'parameters' back to 'config'
        """
        Initializes the TimmModel.

        Args:
            model_id (str): The unique identifier for the model instance.
            config (dict): Configuration parameters for the model. # Changed 'parameters' to 'config'
                               Expected keys: 'timm_model_name' (e.g., 'swin_large_patch4_window12_384'),
                                              'vector_size',
                                              'pretrained' (optional, default True).
        """
        super().__init__(config) # Correct: Pass only config to base
        self.model_id = model_id # Store model_id in subclass
        self.timm_model_name = config.get('timm_model_name') # Use config dict
        self._vector_size = config.get('vector_size') # Use config dict
        self.pretrained = config.get('pretrained', True) # Use config dict

        if not self.timm_model_name:
            raise ValueError(f"Missing required parameter 'timm_model_name' for model {model_id}")
        if not self._vector_size:
            raise ValueError(f"Missing required parameter 'vector_size' for model {model_id}")

        self._vector_size = int(self._vector_size)
        self.model = None
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.transform = None # Will be determined during load

        logger.info(f"Initialized TimmModel (ID: {self.model_id}) "
                    f"for Timm Model: {self.timm_model_name}, Vector Size: {self._vector_size}, Device: {self.device}")

    def load(self):
        """
        Loads the specified Timm model.
        """
        if self.model is not None:
            logger.info(f"Model {self.model_id} ({self.timm_model_name}) is already loaded.")
            return

        try:
            logger.info(f"Loading Timm model: {self.timm_model_name} (pretrained={self.pretrained})")
            # Load the model
            self.model = timm.create_model(
                self.timm_model_name,
                pretrained=self.pretrained,
                num_classes=0 # Load without classifier head for feature extraction
            )
            self.model.eval() # Set to evaluation mode
            self.model.to(self.device) # Move model to appropriate device

            # Determine the correct preprocessing transform for the model
            config = resolve_data_config({}, model=self.model)
            self.transform = create_transform(**config)
            self.input_size = config['input_size'][1:] # Get (height, width)

            logger.info(f"Successfully loaded model {self.model_id} ({self.timm_model_name}) to {self.device}")
            logger.info(f"Input size: {self.input_size}, Transform: {self.transform}")
            # PyTorch models don't have a built-in summary like Keras
            # logger.info(self.model) # This can be very verbose

        except Exception as e:
            logger.error(f"Error loading model {self.model_id} ({self.timm_model_name}): {e}", exc_info=True)
            self.model = None
            self.transform = None
            raise

    def unload(self):
        """
        Unloads the PyTorch model.
        """
        if self.model is not None:
            logger.info(f"Unloading model {self.model_id} ({self.timm_model_name})...")
            self.model = None
            self.transform = None
            if torch.cuda.is_available():
                torch.cuda.empty_cache() # Clear GPU cache if applicable
            logger.info(f"Model {self.model_id} unloaded.")
        else:
            logger.info(f"Model {self.model_id} is not loaded, nothing to unload.")

    def get_model_type(self) -> str:
        return 'embedding'

    @property
    def vector_size(self) -> int:
        if self._vector_size is None:
             raise ValueError(f"Vector size not set for model {self.model_id}")
        return self._vector_size

    def preprocess(self, image_path: str) -> torch.Tensor:
        """
        Loads and preprocesses an image using the Timm model's transform.
        """
        if self.transform is None:
             raise ValueError(f"Model {self.model_id} transform not initialized. Load the model first.")

        try:
            logger.debug(f"Preprocessing image for {self.timm_model_name}: {image_path}")
            # Load image using PIL
            img = Image.open(image_path).convert('RGB')

            # Apply the transform
            tensor = self.transform(img)

            # Add batch dimension (unsqueeze(0)) -> shape [1, C, H, W]
            tensor = tensor.unsqueeze(0)

            logger.debug(f"Preprocessing complete for {image_path}. Output shape: {tensor.shape}")
            return tensor

        except FileNotFoundError as e:
             logger.error(f"Image file not found at path: {image_path}")
             raise
        except Exception as e:
            logger.error(f"Unexpected error during preprocessing of {image_path}: {e}", exc_info=True)
            raise

    def compute_features(self, preprocessed_image: torch.Tensor) -> np.ndarray:
        """
        Computes the feature vector for the preprocessed image using the Timm model.
        """
        if self.model is None:
            logger.error(f"Model {self.model_id} ({self.timm_model_name}) is not loaded. Cannot compute features.")
            raise ValueError(f"Model {self.model_id} must be loaded before computing features.")

        try:
            logger.debug(f"Computing features for input shape: {preprocessed_image.shape}")
            preprocessed_image = preprocessed_image.to(self.device) # Ensure tensor is on the correct device

            with torch.no_grad(): # Disable gradient calculation for inference
                features = self.model(preprocessed_image)

            # Move features to CPU and convert to NumPy array
            features_np = features.cpu().numpy()
            features_np = np.squeeze(features_np) # Remove batch dim if size 1

            if features_np.ndim > 0 and features_np.shape[0] != self.vector_size:
                 logger.warning(f"Model {self.model_id} ({self.timm_model_name}) produced vector of size {features_np.shape[0]}, "
                                f"but expected {self.vector_size}. Check model/config.")
                 # Consider raising an error

            logger.debug(f"Computed features shape: {features_np.shape}")
            return features_np

        except Exception as e:
            logger.error(f"Error computing features with model {self.model_id} ({self.timm_model_name}): {e}", exc_info=True)
            raise

    def normalize_score(self, raw_score: float) -> float:
        """
        Normalizes cosine similarity score. Assumes raw_score is similarity in [-1, 1].
        Maps it to [0, 1].
        """
        # Cosine similarity is in [-1, 1]. Map to [0, 1] where 1 is most similar.
        normalized = (float(raw_score) + 1.0) / 2.0
        normalized = max(0.0, min(1.0, normalized)) # Clamp to [0, 1]
        logger.debug(f"Normalizing raw score {raw_score} to {normalized}")
        return normalized


# --- SiglipModel ---

from transformers import AutoModel, AutoProcessor
from transformers.image_utils import load_image

class SiglipModel(ImageModelBase):
    """
    Image embedding model using Hugging Face transformers with a Siglip model.
    """
    def __init__(self, model_id: str, config: dict):
        """
        Initializes the SiglipModel.

        Args:
            model_id (str): The unique identifier for the model instance.
            config (dict): Configuration parameters. Expected keys:
                                - 'vector_size'
                                - 'model_name_or_path' (e.g., 'google/siglip2-large-patch16-512' or 'google/siglip2-giant-opt-patch16-384')
        """
        super().__init__(config) # Corrected: Pass only config to base
        self.model_id = model_id
        self._vector_size = config.get('vector_size')
        self.model_name_or_path = config.get('model_name_or_path')

        if not self._vector_size:
            raise ValueError(f"Missing required parameter 'vector_size' for SiglipModel {model_id}")
        if not self.model_name_or_path:
            raise ValueError(f"Missing required parameter 'model_name_or_path' for SiglipModel {model_id}")

        self._vector_size = int(self._vector_size)
        self.model = None
        self.processor = None
        # Determine device (prefer CUDA if available, otherwise CPU)
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        logger.info(f"Initialized SiglipModel (ID: {self.model_id}) "
                    f"for Model: {self.model_name_or_path}, Vector Size: {self._vector_size}, Device: {self.device}")

    def load(self):
        """
        Loads the Siglip model and processor.
        """
        if self.model is not None and self.processor is not None:
            logger.info(f"Model {self.model_id} ({self.model_name_or_path}) is already loaded.")
            return

        try:
            logger.info(f"Loading Siglip model: {self.model_name_or_path} to device: {self.device}")
            # Load the model and processor
            self.model = AutoModel.from_pretrained(
                self.model_name_or_path, trust_remote_code=True # Add trust_remote_code
            ).eval().to(self.device)
            self.processor = AutoProcessor.from_pretrained(
                self.model_name_or_path, use_fast=True, trust_remote_code=True # Add trust_remote_code
            )
            logger.info(f"Successfully loaded model {self.model_id} ({self.model_name_or_path})")

        except Exception as e:
            logger.error(f"Error loading model {self.model_id} ({self.model_name_or_path}): {e}", exc_info=True)
            self.model = None
            self.processor = None
            raise

    def unload(self):
        """
        Unloads the Siglip model and processor.
        """
        if self.model is not None or self.processor is not None:
            logger.info(f"Unloading model {self.model_id} ({self.model_name_or_path})...")
            self.model = None
            self.processor = None
            if torch.cuda.is_available():
                torch.cuda.empty_cache() # Clear GPU cache if applicable
            logger.info(f"Model {self.model_id} unloaded.")
        else:
            logger.info(f"Model {self.model_id} is not loaded, nothing to unload.")

    def get_model_type(self) -> str:
        return 'embedding'

    @property
    def vector_size(self) -> int:
        if self._vector_size is None:
             raise ValueError(f"Vector size not set for model {self.model_id}")
        return self._vector_size

    def preprocess(self, image_path: str):
        """
        Loads and preprocesses an image using the Siglip processor.
        Converts image to RGB to handle grayscale inputs.
        """
        if self.processor is None:
             raise ValueError(f"Model {self.model_id} processor not initialized. Load the model first.")

        try:
            logger.debug(f"Preprocessing image for {self.model_name_or_path}: {image_path}")
            # Load image using transformers' utility and convert to RGB
            image = load_image(image_path).convert("RGB")
            # Process the image
            inputs = self.processor(images=[image], return_tensors="pt").to(self.device)

            logger.debug(f"Preprocessing complete for {image_path}. Output shape: {inputs['pixel_values'].shape}")
            return inputs

        except FileNotFoundError as e:
             logger.error(f"Image file not found at path: {image_path}")
             raise
        except Exception as e:
            logger.error(f"Unexpected error during preprocessing of {image_path}: {e}", exc_info=True)
            raise

    def compute_features(self, preprocessed_image) -> np.ndarray:
        """
        Computes the feature vector for the preprocessed image using the Siglip model.
        """
        if self.model is None:
            logger.error(f"Model {self.model_id} ({self.model_name_or_path}) is not loaded. Cannot compute features.")
            raise ValueError(f"Model {self.model_id} must be loaded before computing features.")

        try:
            logger.debug(f"Computing features for input")
            # Ensure input tensor is on the correct device
            # preprocessed_image = preprocessed_image.to(self.device) # Already moved in preprocess

            with torch.no_grad(): # Disable gradient calculation for inference
                # Siglip uses get_image_features
                features = self.model.get_image_features(**preprocessed_image)

            # Move features to CPU and convert to NumPy array
            features_np = features.cpu().numpy()
            features_np = np.squeeze(features_np) # Remove batch dim if size 1

            if features_np.ndim > 0 and features_np.shape[0] != self.vector_size:
                 logger.warning(f"Model {self.model_id} ({self.model_name_or_path}) produced vector of size {features_np.shape[0]}, "
                                 f"but expected {self.vector_size}. Check model/config.")
                 # Consider raising an error

            logger.debug(f"Computed features shape: {features_np.shape}")
            return features_np

        except Exception as e:
            logger.error(f"Error computing features with model {self.model_id} ({self.model_name_or_path}): {e}", exc_info=True)
            raise

    def normalize_score(self, raw_score: float) -> float:
        """
        Normalizes cosine similarity score. Assumes raw_score is similarity in [-1, 1].
        Maps it to [0, 1].
        """
        # Cosine similarity is in [-1, 1]. Map to [0, 1] where 1 is most similar.
        normalized = (float(raw_score) + 1.0) / 2.0
        normalized = max(0.0, min(1.0, normalized)) # Clamp to [0, 1]
        logger.debug(f"Normalizing raw score {raw_score} to {normalized}")
        return normalized