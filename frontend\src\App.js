import React from 'react';
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import Layout from './components/Layout';
import TrademarkPage from './pages/model-test-workbench/TrademarkPage';
import CopyrightPage from './pages/model-test-workbench/CopyrightPage';
import PatentPage from './pages/model-test-workbench/PatentPage';
import DashboardPage from './pages/model-test-workbench/DashboardPage';
import SettingsPage from './pages/model-test-workbench/SettingsPage';
import PatentPlatformPage from './pages/PatentPlatformPage'; // Import the new Patent Platform Page
import ResultsPage from './pages/boundingbox/ResultsPage'; // Import Bounding Box Results Page
import ModelPage from './pages/boundingbox/ModelPage'; // Import Bounding Box Model Page
import BoundingBoxPictureManagementPage from './pages/boundingbox/PictureManagementPage'; // Import Bounding Box Picture Management Page
import RankPage from './pages/boundingbox/RankPage'; // Import Bounding Box Rank Page
import Gallery from './pages/copyright-viz/Gallery';
import DuplicateReviewer from './pages/copyright-viz/DuplicateReviewer';
import OrphanFinder from './pages/copyright-viz/OrphanFinder';
import AddImage from './pages/copyright-viz/AddImage';
import Settings from './pages/copyright-viz/Settings';
import TrademarkPlatformPage from './pages/TrademarkPlatformPage';
import './App.css'; // Keep default styles for now

function App() {
  return (
    <BrowserRouter>
      <Routes>
        <Route path="/" element={<Layout />}>
          {/* Default route redirects to Dashboard */}
          <Route index element={<Navigate to="/dashboard" replace />} />
          <Route path="trademark" element={<TrademarkPage />} />
          <Route path="copyright" element={<CopyrightPage />} />
          <Route path="patent" element={<PatentPage />} />
          <Route path="dashboard" element={<DashboardPage />} />
          <Route path="settings" element={<SettingsPage />} />
          <Route path="boundingbox" element={<ResultsPage />} />
          <Route path="boundingbox/models" element={<ModelPage />} />
          <Route path="boundingbox/pictures" element={<BoundingBoxPictureManagementPage />} />
          <Route path="boundingbox/ranking" element={<RankPage />} /> {/* New Bounding Box Ranking Route */}
          <Route path="patent-viz/*" element={<PatentPlatformPage />} /> {/* Route for Patent Visualization Platform */}
          <Route path="trademark-viz/*" element={<TrademarkPlatformPage />} /> {/* Route for Trademark Visualization Platform */}
          <Route path="copyright/gallery" element={<Gallery />} />
          {/* Primary routes used by Layout tabs */}
          <Route path="copyright/duplicates" element={<DuplicateReviewer />} />
          <Route path="copyright/orphans" element={<OrphanFinder />} />
          {/* Backwards-compatible aliases (old paths) */}
          <Route path="copyright/review/duplicates" element={<DuplicateReviewer />} />
          <Route path="copyright/review/orphans" element={<OrphanFinder />} />
          <Route path="copyright/add" element={<AddImage />} />
          <Route path="copyright/settings" element={<Settings />} />
          {/* Add other nested routes or a 404 handler here if needed */}
          <Route path="*" element={<Navigate to="/dashboard" replace />} /> {/* Basic fallback */}
        </Route>
      </Routes>
    </BrowserRouter>
  );
}

export default App;
