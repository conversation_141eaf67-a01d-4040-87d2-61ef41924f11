"""
Orphan Processing Types and Constants

This module contains all constants, enums, and data models used in orphan processing.
"""

from dataclasses import dataclass
from typing import Dict, List, Optional, Any


class OrphanCategory:
    """Constants for different orphan categories."""
    COURT_ONLY = 'court-only'
    DB_ONLY = 'db-only'
    PROD_NO_QDRANT = 'prod-no-qdrant'
    QDRANT_ONLY = 'qdrant-only'
    MISSING_COS = 'missing-cos'
    DUPLICATE_REG_NO = 'duplicate-reg-no'

    VALID_CATEGORIES = [
        COURT_ONLY, DB_ONLY, PROD_NO_QDRANT,
        QDRANT_ONLY, MISSING_COS, DUPLICATE_REG_NO
    ]


class FixAction:
    """Constants for different fix actions."""
    DELETE_QDRANT = 'delete_qdrant'
    ADD_PROD_TRUE = 'add_prod_true'
    ADD_PROD_TRUE_REMOVE_SIMILAR = 'add_prod_true_remove_similar'
    SET_DB_PLAINTIFF_ID = 'set_db_plaintiff_id'
    SET_QDRANT_PLAINTIFF_ID = 'set_qdrant_plaintiff_id'

    # Valid actions per category
    VALID_ACTIONS = {
        OrphanCategory.QDRANT_ONLY: [
            DELETE_QDRANT, ADD_PROD_TRUE, ADD_PROD_TRUE_REMOVE_SIMILAR,
            SET_DB_PLAINTIFF_ID, SET_QDRANT_PLAINTIFF_ID
        ]
    }


@dataclass
class OrphanData:
    """Data model for orphan records."""
    id: str
    data: Dict[str, Any]


@dataclass
class PaginationInfo:
    """Data model for pagination information."""
    page: int
    per_page: int
    total_count: int
    total_pages: int
    has_next: bool
    has_prev: bool


@dataclass
class PaginatedResult:
    """Data model for paginated results."""
    items: List[OrphanData]
    pagination: PaginationInfo


@dataclass
class FixResult:
    """Data model for fix operation results."""
    success: bool
    category: str
    action: str
    deleted_count: int
    total_requested: int
    errors: List[str]
