#!/usr/bin/env python3
"""
Script to clean up stuck transactions that are causing deadlocks.
Run this script when you encounter hanging INSERT operations.
"""

import os
import sys
import time

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from dotenv import load_dotenv
load_dotenv()

from backend import create_app
from backend.utils.db_utils import cleanup_stuck_transactions, get_database_health_status

def main():
    """Main cleanup function"""
    print("🧹 Database Transaction Cleanup Tool")
    print("=" * 50)

    # Create Flask app context
    print("🔧 Setting up Flask application context...")
    app = create_app(init_for_dev=False)

    with app.app_context():
        # First, check current status
        print("📊 Checking current database health...")
        health_status = get_database_health_status(bind='maidalv_db')

        if not health_status['connection']:
            print("❌ Cannot connect to database. Check connection settings.")
            return False

        print(f"\n⚠️ Current issues:")
        print(f"   Long-running transactions: {len(health_status['long_transactions'])}")
        print(f"   Lock conflicts: {len(health_status['locks'])}")

        if len(health_status['long_transactions']) == 0:
            print("✅ No stuck transactions found. Database appears healthy.")
            return True

        # Ask user to confirm cleanup
        print(f"\n🔧 About to terminate {len(health_status['long_transactions'])} stuck transactions.")
        confirm = input("Do you want to proceed? (y/N): ").strip().lower()

        if confirm not in ['y', 'yes']:
            print("❌ Cleanup cancelled by user.")
            return False

        # Perform cleanup
        print("\n🧹 Starting cleanup process...")
        success = cleanup_stuck_transactions(bind='maidalv_db', max_age_minutes=5)  # Terminate anything older than 5 minutes

        if success:
            print("✅ Cleanup completed successfully.")

            # Wait a moment and check status again
            print("⏳ Waiting 2 seconds before re-checking status...")
            time.sleep(2)

            print("\n📊 Post-cleanup health status:")
            new_health = get_database_health_status(bind='maidalv_db')

            if len(new_health['long_transactions']) == 0 and len(new_health['locks']) == 0:
                print("✅ Database is now healthy!")
                return True
            else:
                print("⚠️ Some issues remain:")
                print(f"   Long transactions: {len(new_health['long_transactions'])}")
                print(f"   Lock conflicts: {len(new_health['locks'])}")
                return False
        else:
            print("❌ Cleanup failed.")
            return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)