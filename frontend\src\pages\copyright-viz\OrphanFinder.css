.orphan-finder-container {
    padding: 2rem;
    font-family: sans-serif;
    background-color: #f4f6f8;
}

.orphan-header {
    text-align: center;
    margin-bottom: 2rem;
}

.orphan-tabs {
    display: flex;
    justify-content: center;
    margin-bottom: 2rem;
    border-bottom: 1px solid #ddd;
}

.tab-button {
    padding: 1rem 1.5rem;
    border: none;
    background: none;
    cursor: pointer;
    font-size: 1rem;
    color: #555;
    border-bottom: 3px solid transparent;
    margin-bottom: -1px;
}

.tab-button.active {
    color: #007bff;
    border-bottom-color: #007bff;
}

.orphan-content {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.orphan-panel {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.orphan-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid #e9ecef;
    margin-bottom: 1rem;
}

.orphan-actions button {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.orphan-actions button:first-child {
    background-color: #dc3545;
    color: white;
}

.orphan-actions button:first-child:hover:not(:disabled) {
    background-color: #c82333;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
}

.orphan-actions button:last-child {
    background-color: #007bff;
    color: white;
}

.orphan-actions button:last-child:hover:not(:disabled) {
    background-color: #0056b3;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
}

.orphan-actions button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.orphan-table {
    width: 100%;
    border-collapse: collapse;
}

.orphan-table th,
.orphan-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.orphan-table th {
    background-color: #f8f9fa;
}

.no-orphans {
    text-align: center;
    color: #888;
    padding: 2rem;
}

.error-message {
    color: #dc3545;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    padding: 1rem;
    border-radius: 4px;
    text-align: center;
}

.loading-spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 4rem auto;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* New Card Layout Styles */
.orphan-items-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
    margin: 1rem 0;
}

.orphan-card {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s, box-shadow 0.2s;
}

.orphan-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.orphan-card.selected {
    background-color: #e3f2fd;
    border-color: #2196f3;
    box-shadow: 0 4px 16px rgba(33, 150, 243, 0.3);
}

.orphan-card.selected:hover {
    box-shadow: 0 6px 20px rgba(33, 150, 243, 0.4);
}

.card-header {
    padding: 0.75rem;
    background-color: #f8f9fa;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: flex-end;
}

.card-checkbox {
    width: 16px;
    height: 16px;
}

.card-image-container {
    height: 130px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    overflow: hidden;
    position: relative;
}

.card-thumbnail {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    cursor: pointer;
    transition: transform 0.2s;
}

.card-thumbnail:hover {
    transform: scale(1.05);
}

.no-image {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 100%;
    background-color: #e9ecef;
    color: #6c757d;
    font-size: 0.9rem;
}

.card-info {
    padding: 1rem;
}

.info-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: 0.85rem;
}

.info-row label {
    font-weight: 600;
    color: #495057;
    margin-right: 0.5rem;
}

.info-row span {
    color: #6c757d;
    word-break: break-word;
    text-align: right;
    flex: 1;
}

/* Fullscreen Modal Styles */
.fullscreen-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 2rem;
}

.fullscreen-content {
    position: relative;
    max-width: 90vw;
    max-height: 90vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.fullscreen-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 4px;
}

.close-modal {
    position: absolute;
    top: -40px;
    right: 0;
    background: none;
    border: none;
    color: white;
    font-size: 2rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.close-modal:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Pagination Styles */
.orphan-pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 2rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.pagination-info {
    font-size: 0.9rem;
    color: #495057;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.pagination-btn {
    padding: 0.5rem 1rem;
    border: 1px solid #ddd;
    background: white;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.2s;
}

.pagination-btn:hover:not(:disabled) {
    background-color: #e9ecef;
    border-color: #adb5bd;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.page-indicator {
    font-weight: 500;
    color: #495057;
}

.page-input-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.page-input {
    width: 60px;
    padding: 0.25rem 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-align: center;
    font-size: 0.9rem;
}

.go-btn {
    padding: 0.25rem 0.75rem;
    border: 1px solid #007bff;
    background: #007bff;
    color: white;
    cursor: pointer;
    border-radius: 4px;
    font-size: 0.8rem;
    transition: background-color 0.2s;
}

.go-btn:hover {
    background-color: #0056b3;
}

.per-page-selector {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.per-page-select {
    padding: 0.25rem 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
}

.orphan-count-info {
    text-align: center;
    margin-top: 1rem;
    color: #6c757d;
    font-size: 0.85rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .orphan-items-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .orphan-pagination {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .fullscreen-modal {
        padding: 1rem;
    }

    .fullscreen-content {
        max-width: 95vw;
        max-height: 95vh;
    }
}