# Containerized Deployment Guide

## Prerequisites
- Docker 20.10+
- Docker Compose 1.29+

## Getting Started
1. Build and start all services:
```bash
docker-compose up --build
```

2. Access the application:
- Frontend: http://localhost
- Backend API: http://localhost:5000/api

## Service Details
| Service   | Port  | Description                     |
|-----------|-------|---------------------------------|
| Frontend  | 80    | React app served by Ngin<PERSON>       |
| Backend   | 5000  | Flask app with Gunicorn         |
| Worker    | -     | Celery background tasks         |
| Redis     | 6379  | Message broker for Celery       |

## Environment Configuration
Create a `.env` file in the root directory with required variables:
```env
POSTGRES_USER=your_user
POSTGRES_PASSWORD=your_password
CELERY_BROKER_URL=redis://redis:6379/0
```

## Managing Services
- Start in detached mode: `docker-compose up -d`
- Stop services: `docker-compose down`
- View logs: `docker-compose logs -f`
- Scale workers: `docker-compose up --scale worker=3`

## Production Considerations
1. Use a production-grade database (PostgreSQL)
2. Implement HTTPS with Let's Encrypt
3. Add monitoring and logging
4. Set resource limits in docker-compose.yml