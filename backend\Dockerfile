# Use an official Python runtime as a parent image
FROM python:3.12-slim

# Set the working directory in the container
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends gcc libpq-dev && \
    rm -rf /var/lib/apt/lists/*

# Copy only necessary files for dependency installation
COPY backend/requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt gunicorn

# Copy application code (only backend and models directories)
COPY backend/ ./backend
COPY models/ ./models
COPY setup.py ./

# Set environment variables
ENV PYTHONUNBUFFERED=1
# Gunicorn arguments are now managed in gunicorn.conf.py, but we can keep some here for clarity or overrides.
# In this configuration, we have 5 workers, and each get blocked if there is a long streaming session (e.g. uploading 150 files)
# If you add -k gthread --threads 20, each worker can create 20 threads, so you can have 100 threads in total.
ENV GUNICORN_CMD_ARGS="--workers=5 --bind=0.0.0.0:5000 --timeout 3600 --log-level debug --access-logfile -"

# Command to run the application using <PERSON><PERSON> with the configuration file
# The --preload flag is implicitly handled by the presence of server hooks in the config.
CMD ["gunicorn", "-c", "backend/gunicorn.conf.py", "backend:create_app()"]
