"""
Orphan Processing Helper Functions

This module contains all shared utility functions used in orphan processing.
"""

from flask import current_app
from sqlalchemy import func
from backend.api.copyright.helpers import get_high_res_path, get_low_res_path, QDRANT_COLLECTION_NAME
from backend.extensions import db
from backend.database.copyrights_file_model import CopyrightsFiles
from backend.utils.vector_store import get_qdrant_client
from backend.utils.cache_utils import get_plaintiff_df, get_case_df
from qdrant_client import models
from datetime import datetime
import uuid


def get_plaintiff_lookup():
    """Get plaintiff lookup dictionary for name resolution."""
    plaintiff_df = get_plaintiff_df()
    plaintiff_lookup = {}
    if not plaintiff_df.empty:
        plaintiff_lookup = dict(zip(plaintiff_df['id'], plaintiff_df['plaintiff_name']))
    return plaintiff_lookup


def generate_file_paths(plaintiff_id, filename, timestamp=None):
    """Generate high-res and low-res paths for a file."""
    if timestamp is None:
        timestamp = datetime.now().isoformat()

    high_res_path = get_high_res_path(plaintiff_id, filename, timestamp) if plaintiff_id and filename else None
    low_res_path = get_low_res_path(plaintiff_id, filename, timestamp) if plaintiff_id and filename else None

    return high_res_path, low_res_path


def apply_pagination(items, page, per_page):
    """Apply pagination to a list of items and return paginated results with metadata."""
    from .orphan_types import PaginationInfo, PaginatedResult, OrphanData

    total_count = len(items)
    offset = (page - 1) * per_page
    paginated_items = items[offset:offset + per_page]

    total_pages = (total_count + per_page - 1) // per_page if total_count > 0 else 1

    pagination_info = PaginationInfo(
        page=page,
        per_page=per_page,
        total_count=total_count,
        total_pages=total_pages,
        has_next=page < total_pages,
        has_prev=page > 1
    )

    return PaginatedResult(
        items=[OrphanData(id=str(i), data=item) if not isinstance(item, OrphanData) else item
               for i, item in enumerate(paginated_items)],
        pagination=pagination_info
    )


def extract_court_filenames(case_df):
    """Extract all copyright filenames from court case data."""
    court_filenames = set()

    if case_df.empty:
        return court_filenames

    for _, case in case_df.iterrows():
        if case.get('images') and isinstance(case['images'], dict):
            copyright_data = case['images'].get('copyrights', {})
            if isinstance(copyright_data, dict):
                court_filenames.update(copyright_data.keys())

    return court_filenames


def get_qdrant_points_batch(limit=10000):
    """Get all Qdrant points for copyright IP type in batches."""
    client = get_qdrant_client()
    copyright_filter = models.Filter(
        must=[models.FieldCondition(key="ip_type", match=models.MatchValue(value="Copyright"))]
    )

    all_qdrant_points = []
    offset = None

    while True:
        scroll_result = client.scroll(
            collection_name=QDRANT_COLLECTION_NAME,
            scroll_filter=copyright_filter,
            limit=limit,
            offset=offset,
            with_payload=True,
            with_vectors=False
        )

        points_batch = scroll_result[0]
        if not points_batch:
            break

        all_qdrant_points.extend(points_batch)
        offset = scroll_result[1]

        # Safety check to prevent infinite loops
        if len(all_qdrant_points) > 100000:
            break

    return all_qdrant_points


def get_specific_qdrant_points(ids):
    """Fetch specific Qdrant points by their IDs."""
    try:
        client = get_qdrant_client()
        if not client:
            raise Exception("Failed to connect to Qdrant client")

        # Fetch points by IDs
        points_response = client.retrieve(
            collection_name=QDRANT_COLLECTION_NAME,
            ids=ids,
            with_payload=True,
            with_vectors=False
        )

        return points_response
    except Exception as e:
        current_app.logger.error(f"Error fetching specific Qdrant points: {str(e)}")
        raise


def extract_method_from_filename(filename):
    """Extract method from filename (substring between underscore and file extension)."""
    if not filename or '_' not in filename or '.' not in filename:
        return None

    try:
        # Find the last underscore and the last dot
        underscore_pos = filename.rfind('_')
        dot_pos = filename.rfind('.')

        # Ensure underscore comes before the file extension
        if underscore_pos == -1 or dot_pos == -1 or underscore_pos >= dot_pos:
            return None

        # Extract method between underscore and file extension
        method = filename[underscore_pos + 1:dot_pos]

        # Clean up the method (remove any extra spaces or special characters if needed)
        return method.strip() if method.strip() else None

    except Exception as e:
        current_app.logger.error(f"Error extracting method from filename '{filename}': {str(e)}")
        return None


def generate_unique_filename(registration_number, method, file_extension):
    """Generate a unique filename by adding a number suffix if needed."""
    base_filename = f"{registration_number}_{method}.{file_extension}"

    # Check if the base filename already exists
    existing_files = CopyrightsFiles.query.filter_by(registration_number=registration_number).all()
    existing_filenames = {file.filename for file in existing_files}

    if base_filename not in existing_filenames:
        return base_filename

    # If base filename exists, find the next available number
    counter = 1
    while True:
        numbered_filename = f"{registration_number}_{method}_{counter}.{file_extension}"
        if numbered_filename not in existing_filenames:
            return numbered_filename
        counter += 1

def find_most_similar_points(orphaned_points, all_qdrant_points, similarity_threshold=0.90):
    """
    Find the most similar point for each orphaned point from all Qdrant points.

    Args:
        orphaned_points: List of orphaned Qdrant points to find similarities for
        all_qdrant_points: List of all Qdrant points for comparison
        similarity_threshold: Minimum similarity score (0.0 to 1.0) to consider a match

    Returns:
        Dictionary mapping orphaned point IDs to their most similar point data
    """
    try:
        from sklearn.metrics.pairwise import cosine_similarity
        import numpy as np
        from backend.utils.vector_store import get_qdrant_client

        if not orphaned_points or not all_qdrant_points:
            return {}

        # Extract vectors from all points
        VECTOR_NAME = "siglip_vector"

        # Get vectors for all Qdrant points
        all_vectors = []
        all_points_data = []

        for point in all_qdrant_points:
            if hasattr(point, 'vector') and point.vector and VECTOR_NAME in point.vector:
                all_vectors.append(point.vector[VECTOR_NAME])
                all_points_data.append({
                    'id': point.id,
                    'payload': point.payload
                })

        if not all_vectors:
            current_app.logger.warning("No vectors found in Qdrant points")
            return {}

        all_vectors = np.array(all_vectors)

        # Get vectors for orphaned points
        orphaned_vectors = []
        orphaned_points_data = []

        for orphan_item in orphaned_points:
            # Extract the actual Qdrant point from OrphanData if needed
            point = orphan_item.data if hasattr(orphan_item, 'data') else orphan_item
            if hasattr(point, 'vector') and point.vector and VECTOR_NAME in point.vector:
                orphaned_vectors.append(point.vector[VECTOR_NAME])
                orphaned_points_data.append({
                    'id': point.id,
                    'payload': point.payload
                })

        if not orphaned_vectors:
            current_app.logger.warning("No vectors found in orphaned points")
            return {}

        orphaned_vectors = np.array(orphaned_vectors)

        # Compute cosine similarity matrix
        similarity_matrix = cosine_similarity(orphaned_vectors, all_vectors)

        # Find most similar point for each orphaned point
        similar_points = {}

        for i, orphaned_point in enumerate(orphaned_points_data):
            # Get similarity scores for this orphaned point
            similarities = similarity_matrix[i]

            # Find the best match above threshold (excluding self if present)
            best_match_idx = None
            best_similarity = -1

            for j, similarity in enumerate(similarities):
                # Skip if it's the same point (based on filename or ID)
                if (all_points_data[j]['payload'].get('filename') == orphaned_point['payload'].get('filename') or
                    all_points_data[j]['id'] == orphaned_point['id']):
                    continue

                if similarity > best_similarity and similarity >= similarity_threshold:
                    best_similarity = similarity
                    best_match_idx = j

            if best_match_idx is not None:
                similar_point_data = all_points_data[best_match_idx]
                similar_points[str(orphaned_point['id'])] = {
                    'filename': similar_point_data['payload'].get('filename', ''),
                    'plaintiff_id': similar_point_data['payload'].get('plaintiff_id'),
                    'plaintiff_name': similar_point_data['payload'].get('plaintiff_name', ''),
                    'registration_number': similar_point_data['payload'].get('reg_no', ''),
                    'similarity_score': float(best_similarity),
                    'id': similar_point_data['id'],
                    'high_res_path': generate_file_paths(
                        similar_point_data['payload'].get('plaintiff_id'),
                        similar_point_data['payload'].get('filename', '')
                    )[0],  # high_res_path
                    'low_res_path': generate_file_paths(
                        similar_point_data['payload'].get('plaintiff_id'),
                        similar_point_data['payload'].get('filename', '')
                    )[1]  # low_res_path
                }

        return similar_points

    except Exception as e:
        current_app.logger.error(f"Error in find_most_similar_points: {str(e)}")
        return {}


def find_knn_points(orphaned_points, k=5, similarity_threshold=0.90):
    """
    Find the K-nearest neighbors for each orphaned point using QDrant's built-in similarity search
    and return the 1st most similar neighbor.

    This function uses QDrant's native vector search capabilities to efficiently find the top K most
    similar points for each orphaned point, then returns only the most similar one (1st neighbor).

    Args:
        orphaned_points: List of orphaned Qdrant points to find similarities for
        all_qdrant_points: Not used when using QDrant search (kept for compatibility)
        k: Number of nearest neighbors to find (default: 5)
        similarity_threshold: Minimum similarity score (0.0 to 1.0) to consider a match

    Returns:
        Dictionary mapping orphaned point IDs to their most similar point data (1st neighbor)
    """
    try:
        from backend.utils.vector_store import get_qdrant_client

        if not orphaned_points:
            return {}

        client = get_qdrant_client()
        if not client:
            current_app.logger.error("Failed to connect to Qdrant client")
            return {}

        VECTOR_NAME = "siglip_vector"
        similar_points = {}

        # Process each orphaned point
        for orphan_item in orphaned_points:
            # Extract the actual Qdrant point from OrphanData if needed
            point = orphan_item.data if hasattr(orphan_item, 'data') else orphan_item

            # Check if the point has the required vector
            if not (hasattr(point, 'vector') and point.vector and VECTOR_NAME in point.vector):
                current_app.logger.warning(f"Orphaned point {point.id} missing vector data")
                continue

            query_vector = point.vector[VECTOR_NAME]

            # Search for similar points using QDrant's built-in similarity search
            search_results = client.search(
                collection_name=QDRANT_COLLECTION_NAME,
                query_vector=(VECTOR_NAME, query_vector),
                limit=k,
                score_threshold=similarity_threshold,
                search_params=models.SearchParams(
                    exact=False,  # Use approximate search for better performance
                    quantization=models.QuantizationSearchParams(
                        ignore=False,
                        rescore=False,
                        oversampling=1.0
                    )
                )
            )

            # Find the best match (excluding self if present)
            best_match = None
            best_score = -1

            for result in search_results:
                # Skip if it's the same point (based on filename or ID)
                if (result.payload.get('filename') == point.payload.get('filename') or
                    result.id == point.id):
                    continue

                # Check if this is the best match so far
                if result.score > best_score:
                    best_score = result.score
                    best_match = result

            # If we found a valid match, format the response
            if best_match:
                similar_points[str(point.id)] = {
                    'filename': best_match.payload.get('filename', ''),
                    'plaintiff_id': best_match.payload.get('plaintiff_id'),
                    'plaintiff_name': best_match.payload.get('plaintiff_name', ''),
                    'registration_number': best_match.payload.get('reg_no', ''),
                    'similarity_score': float(best_score),
                    'high_res_path': generate_file_paths(
                        best_match.payload.get('plaintiff_id'),
                        best_match.payload.get('filename', '')
                    )[0],  # high_res_path
                    'low_res_path': generate_file_paths(
                        best_match.payload.get('plaintiff_id'),
                        best_match.payload.get('filename', '')
                    )[1]  # low_res_path
                }

        return similar_points

    except Exception as e:
        current_app.logger.error(f"Error in find_knn_points: {str(e)}")
        return {}
