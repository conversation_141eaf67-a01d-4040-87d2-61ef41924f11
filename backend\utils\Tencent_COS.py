from qcloud_cos import CosConfig
from qcloud_cos import CosS3Client
import os
import asyncio

def get_cos_client(secret_id_env="COS_SECRET_ID", secret_key_env="COS_SECRET_KEY", bucket=None):
    config = CosConfig(
        Region='ap-guangzhou',
        SecretId=os.getenv(secret_id_env),
        SecretKey=os.getenv(secret_key_env),
        Token=None,
        Scheme='https'
    )
    if bucket is None:
        bucket = 'troimages-1329604052'
    return CosS3Client(config), bucket


### Used by Check
async def async_copy_file_with_retry(client, bucket, to_key, from_key, max_retries=3):
    """Helper function for asynchronous file upload with retry logic"""

    for attempt in range(max_retries):
        try:
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,  # Use default executor
                lambda: client.copy_object(
                    Bucket=bucket,
                    Key=to_key,
                    CopySource={
                        'Bucket': bucket,
                        'Key': from_key,
                        'Region': 'ap-guangzhou'
                    }
                )
            )
            print(f"Copied {from_key} to {to_key} in COS", level='INFO') # Added keys for context
            return response
        except Exception as e:
            if attempt == max_retries - 1:
                raise
            print(f"Failed to copy {from_key} to {to_key}: {str(e)}, retrying in {2 ** attempt} seconds", level='WARNING')
            await asyncio.sleep(2 ** attempt)  # Exponential backoff

### End of Check functions


def delete_cos_files(client, bucket, file_infos):
    # Construct the batch delete request.
    delete_list = []
    for file in file_infos:
        delete_list.append({"Key": file['Key']})

    response = client.delete_objects(Bucket=bucket, Delete={"Object": delete_list})
    print(f"COS Delete response: {response}", level='INFO') # Log response

async def rename_cos_file(client, bucket, source_key, dest_key):
    """
    Renames a file in COS by copying it to a new destination and then deleting the original.

    This operation is implemented as a copy-then-delete pattern.

    Args:
        client: The COS client instance.
        bucket (str): The name of the COS bucket.
        source_key (str): The key of the original file to be renamed.
        dest_key (str): The key of the new file (the destination).

    Returns:
        bool: True if the rename operation (copy and delete) was successful, False otherwise.
    """
    try:
        # Step 1: Copy the object to the new key using the existing retry logic.
        copy_result = await async_copy_file_with_retry(
            client,
            bucket,
            to_key=dest_key,
            from_key=source_key
        )

        # The copy function returns the response on success and raises an exception on failure after retries.
        # If we get here without an exception, the copy was successful.
        print(f"Successfully copied '{source_key}' to '{dest_key}'.", level='INFO')

        # Step 2: If the copy was successful, delete the original object.
        try:
            # The delete_cos_files function is synchronous, but it's a straightforward API call.
            # It takes a list of dictionaries, so we format the input accordingly.
            delete_cos_files(client, bucket, [{'Key': source_key}])
            print(f"Successfully deleted original file '{source_key}'.", level='INFO')
            return True
        except Exception as delete_error:
            # This is a critical failure state: the file was copied, but the original could not be deleted.
            # This results in data duplication and requires manual intervention or a cleanup process.
            print(
                f"CRITICAL: Failed to delete original file '{source_key}' after copying to '{dest_key}'. "
                f"Manual cleanup required. Error: {delete_error}",
                level='ERROR'
            )
            # We return False because the rename operation as a whole did not complete successfully.
            return False

    except Exception as e:
        # This will catch failures from the copy operation after all retries have been exhausted.
        print(
            f"Failed to rename file from '{source_key}' to '{dest_key}' due to a copy error: {e}",
            level='ERROR'
        )
        return False

def check_cos_files_exist(file_identifiers):
    """
    Checks for the existence of multiple files in COS concurrently.

    Args:
        file_identifiers (list of tuples): A list of (plaintiff_id, filename) tuples.

    Returns:
        list: A list of (plaintiff_id, filename) tuples for files that were NOT found.
    """
    client, bucket = get_cos_client()
    missing_files = []

    async def check_file(plaintiff_id, filename):
        """Async helper to check a single file."""
        # Construct the full key for the high-res image.
        # This logic should match the path generation in the API (e.g., get_high_res_path).
        key = f"plaintiff_images/{plaintiff_id}/high/{filename}"
        try:
            loop = asyncio.get_event_loop()
            # object_exists is a lightweight way to check for existence.
            exists = await loop.run_in_executor(
                None,
                lambda: client.object_exists(Bucket=bucket, Key=key)
            )
            if not exists:
                missing_files.append((plaintiff_id, filename))
        except Exception as e:
            print(f"Error checking COS file existence for key {key}: {e}", level='ERROR')
            # Decide if an error checking should be treated as missing.
            # For this case, we'll add it to the list to be safe.
            missing_files.append((plaintiff_id, filename))

    async def main():
        tasks = [check_file(pid, fname) for pid, fname in file_identifiers]
        await asyncio.gather(*tasks)

    # Run the async main function
    asyncio.run(main())
    
    return missing_files