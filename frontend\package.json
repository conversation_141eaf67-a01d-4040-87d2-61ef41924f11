{"name": "frontend", "version": "0.1.0", "private": true, "proxy": "http://localhost:5000", "dependencies": {"@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mui/icons-material": "^7.0.2", "@mui/material": "^7.3.1", "@mui/x-date-pickers": "^7.29.4", "@mui/x-date-pickers-pro": "^7.0.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "axios": "^1.9.0", "date-fns": "^2.30.0", "plotly.js": "^3.0.1", "react": "^19.1.0", "react-day-picker": "^9.7.0", "react-dom": "^19.1.0", "react-image-crop": "^11.0.10", "react-plotly.js": "^2.6.0", "react-router-dom": "^7.5.3", "react-scripts": "5.0.1", "web-vitals": "^4.2.4", "yet-another-react-lightbox": "^3.25.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}