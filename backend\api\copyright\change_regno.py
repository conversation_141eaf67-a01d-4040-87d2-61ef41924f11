"""
File processing operations for copyright assets
"""
import os
import uuid
from flask import current_app, request, jsonify, g
from backend.utils.Tencent_COS import get_cos_client
from backend.utils.cache_utils import get_case_df
from .helpers import sanitize_name
from backend.utils.db_utils import insert_and_update_df_to_GZ_batch, safe_transaction
from sqlalchemy import text
from backend.utils.vector_store import get_qdrant_client, delete_vector, store_vector
from backend.api.copyright.helpers import QDRANT_COLLECTION_NAME

@safe_transaction(bind='maidalv_db')
def change_regno(file_id):
    """
    Changes the registration number for a copyright file.
    """
    try:
        data = request.get_json()
        new_reg_no = data.get('new_reg_no')
        replace_filename = data.get('replace_filename')
        keep_existing = data.get('keep_existing', False)

        if not new_reg_no:
            return jsonify({"success": False, "error": "New registration number is required."}), 400

        conn = g.db_conn
        query = text("""
            SELECT
                cf.id, cf.registration_number, cf.filename, cf.method, cf.type, cf.production,
                cf.create_time, cf.update_time,
                c.plaintiff_id
            FROM copyrights_files cf
            JOIN copyrights c ON cf.registration_number = c.registration_number
            WHERE cf.id = :id
        """)
        result = conn.execute(query, {"id": file_id}).first()
        if not result:
            return jsonify({"success": False, "error": f"Copyright file with id '{file_id}' not found or no matching registration in copyrights table."}), 404
        
        # Convert to dict for easier manipulation
        original_asset = dict(result._mapping)
        old_reg_no = original_asset['registration_number']
        old_filename = original_asset['filename']
        plaintiff_id = original_asset['plaintiff_id']
        method = original_asset['method']
        production = original_asset['production']

        # 1. Determine the new filename
        if replace_filename:
            # Overwrite the existing file
            new_filename = replace_filename
            # Since the filename must be unique, we delete the old record.
            conn.execute(text("DELETE FROM copyrights_files WHERE id = :id"), {"id": file_id})
            
            asset_to_update = None
            asset_to_delete = {"id": file_id, "action": "delete"} # Always delete the original file
    
            # Standard file renaming and metadata updates
            handle_case_file_renaming(old_reg_no, new_reg_no, plaintiff_id, method, new_filename)
            rename_cos_files_for_plaintiff(plaintiff_id, old_reg_no, new_reg_no, method, new_filename)

            if production:
                # If the original file was in production, the new file should be too.
                update_query = text("""
                    UPDATE copyrights_files
                    SET production = TRUE
                    WHERE filename = :filename
                    RETURNING id, registration_number, filename, method, type, production, create_time, update_time
                """)
                result = conn.execute(update_query, {"filename": replace_filename}).first()
                if result:
                    asset_to_update = dict(result._mapping)
                    asset_to_update['plaintiff_id'] = plaintiff_id
                
                update_qdrant_point(old_filename, new_filename, new_reg_no)

            # Return a structured response for the frontend to handle both actions
            return jsonify({
                "success": True,
                "message": "Asset replaced. Update and delete operations initiated.",
                "asset_to_update": asset_to_update,
                "asset_to_delete": asset_to_delete
            }), 200

        # This part handles the case where we are not replacing a file, but just changing the reg_no
        # or creating a new indexed file.
        else:
            if keep_existing:
                import re
                results = conn.execute(text("SELECT filename FROM copyrights_files WHERE registration_number = :reg_no AND method = :method"), {"reg_no": new_reg_no, "method": method}).fetchall()
                max_i = 0
                indexed_pattern = re.compile(f"^{new_reg_no}_{method}_(\\d+)\\.webp$")
                for row in results:
                    filename = row[0]
                    match = indexed_pattern.match(filename)
                    if match:
                        i = int(match.group(1))
                        if i > max_i:
                            max_i = i
                new_filename = f"{new_reg_no}_{method}_{max_i + 1}.webp"
            else:
                new_filename = f"{new_reg_no}_{method}.webp"

            conn.execute(text("UPDATE copyrights_files SET registration_number = :new_reg_no, filename = :new_filename, update_time = NOW() WHERE id = :id"), {"new_reg_no": new_reg_no, "new_filename": new_filename, "id": file_id})
            
            updated_asset = original_asset.copy()
            updated_asset['registration_number'] = new_reg_no
            updated_asset['filename'] = new_filename
            
            # Standard file renaming and metadata updates
            handle_case_file_renaming(old_reg_no, new_reg_no, plaintiff_id, method, new_filename)
            rename_cos_files_for_plaintiff(plaintiff_id, old_reg_no, new_reg_no, method, new_filename)
            if production:
                update_qdrant_point(old_filename, new_filename, new_reg_no)

            return jsonify({
                "success": True,
                "message": f"Successfully changed registration number for file {file_id}.",
                "asset_to_update": updated_asset
            }), 200

    except Exception as e:
        current_app.logger.error(f"Error changing registration number for file_id={file_id}: {e}")
        return jsonify({"success": False, "error": "An unexpected error occurred."}), 500

def handle_case_file_renaming(old_reg_no, new_reg_no, plaintiff_id, method, new_filename):
    """Handle file renaming for affected cases."""
    case_df = get_case_df()
    old_filename = f"{old_reg_no}_{method}.webp"
    old_cert_filename = f"{old_reg_no}_{method}_full.webp"
    new_cert_filename = new_filename.replace('.webp', '_full.webp')

    affected_cases = []
    for _, case_row in case_df.iterrows():
        if case_row['plaintiff_id'] == plaintiff_id:
            images = case_row.get('images', {})
            if isinstance(images, dict):
                copyrights = images.get('copyrights', {})
                if isinstance(copyrights, dict) and old_filename in copyrights:
                    affected_cases.append(case_row)

    for case_row in affected_cases:
        docket = case_row.get('docket', '')
        date_filed = case_row.get('date_filed')
        
        if date_filed:
            date_filed_str = date_filed.strftime('%Y-%m-%d') if not isinstance(date_filed, str) else date_filed
        else:
            date_filed_str = "unknown-date"
        
        case_dir = f"D:\\Documents\\Programing\\TRO\\Documents\\Case Files\\{sanitize_name(f'{date_filed_str} - {docket}')}\\images" if os.name == 'nt' else f"/Documents/Case Files/{sanitize_name(f'{date_filed_str} - {docket}')}/images"
        
        rename_local_files(case_dir, old_filename, new_filename, old_cert_filename, new_cert_filename)
        update_case_metadata_for_rename(case_row['id'], old_filename, new_filename, new_reg_no, new_cert_filename)

def rename_local_files(case_dir, old_filename, new_filename, old_cert_filename, new_cert_filename):
    """Rename files in local directory structure."""
    for subdir in ['', 'high', 'low']:
        for old, new in [(old_filename, new_filename), (old_cert_filename, new_cert_filename)]:
            old_path = os.path.join(case_dir, subdir, old)
            new_path = os.path.join(case_dir, subdir, new)
            if os.path.exists(old_path):
                os.rename(old_path, new_path)
                current_app.logger.info(f"Renamed local file: {old_path} -> {new_path}")

def rename_cos_files_for_plaintiff(plaintiff_id, old_reg_no, new_reg_no, method, new_filename):
    """Rename files in Tencent COS for a plaintiff."""
    client, bucket = get_cos_client()
    old_filename = f"{old_reg_no}_{method}.webp"
    old_cert_filename = f"{old_reg_no}_{method}_full.webp"
    new_cert_filename = new_filename.replace('.webp', '_full.webp')

    for subdir in ['high', 'low']:
        old_key = f"plaintiff_images/{plaintiff_id}/{subdir}/{old_filename}"
        new_key = f"plaintiff_images/{plaintiff_id}/{subdir}/{new_filename}"
        try:
            client.copy_object(Bucket=bucket, Key=new_key, CopySource={'Bucket': bucket, 'Key': old_key, 'Region': 'ap-guangzhou'})
            current_app.logger.info(f"Copied COS file: {old_key} -> {new_key}")
        except Exception as e:
            current_app.logger.warning(f"Could not copy COS file {old_key}: {e}")

    old_cert_key = f"plaintiff_images/{plaintiff_id}/high/{old_cert_filename}"
    new_cert_key = f"plaintiff_images/{plaintiff_id}/high/{new_cert_filename}"
    try:
        client.copy_object(Bucket=bucket, Key=new_cert_key, CopySource={'Bucket': bucket, 'Key': old_cert_key, 'Region': 'ap-guangzhou'})
        current_app.logger.info(f"Copied COS certificate: {old_cert_key} -> {new_cert_key}")
    except Exception as e:
        current_app.logger.warning(f"Could not copy COS certificate {old_cert_key}: {e}")

def update_case_metadata_for_rename(case_id, old_filename, new_filename, new_reg_no, new_cert_filename):
    """Update case metadata after file rename."""
    case_df = get_case_df()
    case_index = case_df[case_df['id'] == case_id].index

    if not case_index.empty:
        idx = case_index[0]
        images_data = case_df.at[idx, 'images']
        if 'copyrights' in images_data and old_filename in images_data['copyrights']:
            del images_data['copyrights'][old_filename]
        images_data.setdefault('copyrights', {})[new_filename] = {
            "reg_no": new_reg_no,
            "full_filename": new_cert_filename
        }
        row_to_update = case_df.loc[case_index].copy()
        insert_and_update_df_to_GZ_batch(row_to_update, "tb_case", "id")
        current_app.logger.info(f"Updated case {case_id} in tb_case: {old_filename} -> {new_filename}")

def update_qdrant_point(old_filename, new_filename, new_reg_no):
    """Update the Qdrant vector store."""
    client = get_qdrant_client()
    if not client:
        current_app.logger.error("Qdrant client not available.")
        return

    old_point_id = str(uuid.uuid5(uuid.NAMESPACE_DNS, old_filename))
    new_point_id = str(uuid.uuid5(uuid.NAMESPACE_DNS, new_filename))

    # Retrieve the old point to get the vector
    try:
        old_point = client.retrieve(collection_name=QDRANT_COLLECTION_NAME, ids=[old_point_id], with_vectors=True)
        if not old_point:
            current_app.logger.warning(f"Point ID {old_point_id} not found in Qdrant.")
            return
        
        vector = old_point[0].vector
        payload = old_point[0].payload or {}
        
        # The vector from client can be a dict {'siglip_vector': [...]}, extract the list.
        vector = vector['siglip_vector']

        # Update payload with new info
        payload['filename'] = new_filename
        payload['reg_no'] = new_reg_no
        payload['full_filename'] = new_filename.replace('.webp', '_full.webp')

        # Delete the old point and create a new one
        delete_vector(client, QDRANT_COLLECTION_NAME, old_point_id)
        store_vector(client, QDRANT_COLLECTION_NAME, new_point_id, vector, payload)
        current_app.logger.info(f"Updated Qdrant point: {old_point_id} -> {new_point_id}")

    except Exception as e:
        current_app.logger.error(f"Error updating Qdrant point: {e}")
