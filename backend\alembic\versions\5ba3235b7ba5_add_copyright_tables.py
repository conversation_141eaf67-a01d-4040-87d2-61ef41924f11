"""add copyright tables

Revision ID: 5ba3235b7ba5
Revises: 5af0834e6d08
Create Date: 2025-08-02 01:02:23.875692

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '5ba3235b7ba5'
down_revision: Union[str, None] = '5af0834e6d08'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('copyright_duplicate_exclusions',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('reg_no_left', sa.String(length=20), nullable=False),
    sa.Column('reg_no_right', sa.String(length=20), nullable=False),
    sa.Column('created_at', sa.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('reg_no_left', 'reg_no_right', name='uq_duplicate_exclusion'),
    sa.UniqueConstraint('reg_no_left', 'reg_no_right', name='uq_duplicate_exclusion')
    )
    op.create_table('copyright_types',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=50), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name'),
    sa.UniqueConstraint('name')
    )
    op.create_table('copyrights',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('copyrights_viz',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('copyright_id', sa.UUID(), nullable=False),
    sa.Column('filename', sa.String(length=255), nullable=False),
    sa.Column('production', sa.Boolean(), nullable=True),
    sa.Column('deleted', sa.Boolean(), nullable=True),
    sa.Column('certificate_status', sa.String(length=20), nullable=True),
    sa.Column('created_at', sa.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['copyright_id'], ['copyrights.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('copyright_id'),
    sa.UniqueConstraint('copyright_id')
    )
    op.create_table('copyright_asset_types',
    sa.Column('asset_id', sa.Integer(), nullable=False),
    sa.Column('type_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['asset_id'], ['copyrights_viz.id'], ),
    sa.ForeignKeyConstraint(['type_id'], ['copyright_types.id'], ),
    sa.PrimaryKeyConstraint('asset_id', 'type_id')
    )
    op.drop_index('ix_bounding_box_results_experiment_id', table_name='bounding_box_results')
    op.drop_index('ix_bounding_box_results_model_id', table_name='bounding_box_results')
    op.alter_column('modeltests_combined_scores_config', 'is_active',
               existing_type=sa.BOOLEAN(),
               nullable=True,
               existing_server_default=sa.text('true'))
    op.alter_column('modeltests_combined_scores_config', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=True,
               existing_server_default=sa.text('now()'))
    op.alter_column('modeltests_combined_scores_config', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=True,
               existing_server_default=sa.text('now()'))
    op.alter_column('modeltests_comparison_results', 'computed_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=True,
               existing_server_default=sa.text('now()'))
    op.alter_column('modeltests_feature_storage', 'computed_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=True,
               existing_server_default=sa.text('now()'))
    op.alter_column('modeltests_ground_truth', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=True,
               existing_server_default=sa.text('now()'))
    op.alter_column('modeltests_images', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=True,
               existing_server_default=sa.text('now()'))
    op.alter_column('modeltests_images', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=True,
               existing_server_default=sa.text('now()'))
    op.alter_column('modeltests_models', 'is_active',
               existing_type=sa.BOOLEAN(),
               nullable=True,
               existing_server_default=sa.text('true'))
    op.alter_column('modeltests_models', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=True,
               existing_server_default=sa.text('now()'))
    op.alter_column('modeltests_models', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=True,
               existing_server_default=sa.text('now()'))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('modeltests_models', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('modeltests_models', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('modeltests_models', 'is_active',
               existing_type=sa.BOOLEAN(),
               nullable=False,
               existing_server_default=sa.text('true'))
    op.alter_column('modeltests_images', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('modeltests_images', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('modeltests_ground_truth', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('modeltests_feature_storage', 'computed_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('modeltests_comparison_results', 'computed_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('modeltests_combined_scores_config', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('modeltests_combined_scores_config', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('modeltests_combined_scores_config', 'is_active',
               existing_type=sa.BOOLEAN(),
               nullable=False,
               existing_server_default=sa.text('true'))
    op.create_index('ix_bounding_box_results_model_id', 'bounding_box_results', ['model_id'], unique=False)
    op.create_index('ix_bounding_box_results_experiment_id', 'bounding_box_results', ['experiment_id'], unique=False)
    op.drop_table('copyright_asset_types')
    op.drop_table('copyrights_viz')
    op.drop_table('copyrights')
    op.drop_table('copyright_types')
    op.drop_table('copyright_duplicate_exclusions')
    # ### end Alembic commands ###
