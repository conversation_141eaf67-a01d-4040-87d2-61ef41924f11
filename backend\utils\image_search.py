# backend/utils/image_search.py
# Consolidated image search functionality for both Patents and Trademarks
# Provides: embedding generation, Qdrant querying, domain-specific parsing, and unified API endpoints

import os
import time
import uuid
import tempfile
from typing import Callable, List, Tuple, Dict, Any, Optional

import numpy as np
from qdrant_client.http import models as qdrant_models
from flask import request, jsonify

from backend.AI.shared_models import get_siglip_model
from backend.utils.vector_store import get_qdrant_client
from backend.utils.uuid_utils import generate_obfuscated_key
from backend.utils.db_utils import get_patent_data_as_dataframe, get_trademark_data_as_dataframe

# Initialize QdrantClient globally using the shared function
_qdrant_client_instance = get_qdrant_client()

# Constants
COLLECTION_NAME = "IP_Assets_Optimized"
VECTOR_NAME = "siglip_vector"

# Embedding helpers

def siglip_embedding(image_path: str) -> np.ndarray[np.float32]:
    """
    Generate a single image embedding using the lazy Siglip model.
    """
    model = get_siglip_model(load_if_needed=True)
    return model.compute_features(data_list=[image_path], data_type="image")[0]

# Qdrant helpers

def query_qdrant_by_embedding(
    query_embedding: np.ndarray[np.float32],
    collection_name: str,
    vector_name: str,
    num_close: int,
    filter_must: Optional[List[qdrant_models.FieldCondition]] = None,
) -> List[Dict[str, Any]]:
    """
    Query Qdrant by embedding with optional filter conditions.
    Returns a list of entries with id, score, and payload (if requested in config).
    """
    must_conditions = filter_must or []
    query_filter = qdrant_models.Filter(must=must_conditions) if must_conditions else None

    query_response = _qdrant_client_instance.query_points(
        collection_name=collection_name,
        query=query_embedding,
        using=vector_name,
        limit=num_close,
        query_filter=query_filter,
        with_payload=True,
        with_vectors=False,
    )

    results = []
    for result in query_response.points:
        results.append(
            {
                "id": result.id,
                "score": result.score,
                "payload": getattr(result, "payload", None),
            }
        )
    return results

# URL generation helpers

def get_cos_patent_fig_path(patent_filename: str) -> str:
    """Create IP URL with proper formatting for patent filenames."""
    obfuscated_reg_no = generate_obfuscated_key(os.path.splitext(patent_filename)[0])
    return f"https://tro-1330776830.cos.ap-guangzhou.myqcloud.com/ip_assets/Patents/{obfuscated_reg_no}.png"

def get_cos_trademark_fig_path(ser_no: str) -> str:
    """Create IP URL with proper formatting for trademark images."""
    obfuscated_reg_no = generate_obfuscated_key(ser_no)
    return f"https://tro-1330776830.cos.ap-guangzhou.myqcloud.com/ip_assets/Trademarks/{obfuscated_reg_no}.webp"

def generate_uuid(num: str) -> str:
    """Generate consistent UUID from filename (PT) using UUID v5."""
    return str(uuid.uuid5(uuid.NAMESPACE_OID, num))

# Patent-specific parsing functions

def _parse_qdrant_results_patents(points: List[Dict[str, Any]]) -> Tuple[List[Dict[str, Any]], float]:
    """Domain-specific parsing for Patent results."""
    parsed_results = _parse_initial_qdrant_points_patents(points)
    db_retrieval_time, patent_df = _enrich_patent_results_from_db(parsed_results)
    _attach_patent_info(parsed_results, patent_df)

    return parsed_results, db_retrieval_time

def _parse_initial_qdrant_points_patents(points: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Parses and validates initial Qdrant points for patents."""
    parsed = []

    for point in points:
        payload = point.get("payload") or {}
        reg_no = payload.get("reg_no")
        if not reg_no:
            print(f"The point {point['id']} does not contain a registration number in its payload. Skipping.")
            continue

        maidalv_id = generate_uuid(reg_no)

        parsed.append({
            "Qdrant_id": point["id"],
            "maidalv_patent_table_id": maidalv_id,
            "reg_no": reg_no,
            "score": point["score"],
        })

    return parsed

def _enrich_patent_results_from_db(parsed_results: List[Dict[str, Any]]) -> Tuple[float, Any]:
    """Fetches patent data from DB using parsed maidalv IDs."""
    maidalv_ids = [result["maidalv_patent_table_id"] for result in parsed_results]
    start_time = time.time()
    patent_df = get_patent_data_as_dataframe(maidalv_ids)
    elapsed_time = time.time() - start_time

    if patent_df is None or patent_df.empty:
        raise ValueError("No patent data found for the provided registration numbers.")

    return elapsed_time, patent_df

def _attach_patent_info(parsed_results: List[Dict[str, Any]], patent_df) -> None:
    """Attaches detailed patent info and matched image paths to each result."""
    patent_df['id'] = patent_df['id'].astype(str).str.strip()
    for result in parsed_results:
        target_id = result["maidalv_patent_table_id"]
        match_row = patent_df[patent_df['id'] == target_id]

        if match_row.empty:
            continue

        row = match_row.iloc[0]
        fig_file_names = row.get("fig_files", [])

        result.update({
            "patent_title": row.get("patent_title", ""),
            "date_published": row.get("date_published", ""),
            "fig_file_names": fig_file_names,
            "fig_file_paths": [get_cos_patent_fig_path(fname) for fname in fig_file_names]
        })

        result["matched_image_path"] = _find_matched_figure(result["Qdrant_id"], fig_file_names)

        if not result.get("matched_image_path"):
            raise ValueError(
                f"Matched image not found for Qdrant ID {result['Qdrant_id']} with registration number {result['reg_no']}."
            )

def _find_matched_figure(qdrant_id: str, fig_file_names: List[str]) -> str:
    """Finds and returns the matched figure path based on Qdrant ID."""
    for fig_name in fig_file_names:
        fig_base = os.path.splitext(os.path.basename(fig_name))[0]
        if generate_uuid(fig_base) == qdrant_id:
            return get_cos_patent_fig_path(fig_name)
    return ""

# Trademark-specific parsing functions

def _parse_qdrant_results_trademarks(points: List[Dict[str, Any]]) -> Tuple[List[Dict[str, Any]], float]:
    """Domain-specific parsing for Trademark results."""
    parsed_results, ids = _parse_initial_trademark_points(points)
    db_retrieval_time, trademark_df = _fetch_trademark_data(ids)
    _enrich_trademark_results(parsed_results, trademark_df)

    return parsed_results, db_retrieval_time

def _parse_initial_trademark_points(points: List[Dict[str, Any]]) -> Tuple[List[Dict[str, Any]], List[str]]:
    """Parses initial Qdrant points into result structure and collects IDs."""
    parsed = []
    ids = []

    for point in points:
        point_id = str(point["id"]).strip()
        ids.append(point_id)
        parsed.append({
            "Qdrant_id": point_id,
            "score": point["score"],
        })

    return parsed, ids

def _fetch_trademark_data(ids: List[str]) -> Tuple[float, Any]:
    """Fetches trademark data from DB and normalizes ID column."""
    start_time = time.time()
    trademark_df = get_trademark_data_as_dataframe(ids)
    db_time = time.time() - start_time

    if trademark_df is None or trademark_df.empty:
        raise ValueError("No trademark data found for the provided registration numbers.")

    trademark_df["id"] = trademark_df["id"].astype(str).str.strip()

    return db_time, trademark_df

def _enrich_trademark_results(parsed_results: List[Dict[str, Any]], trademark_df) -> None:
    """Attaches DB info (reg_no, ser_no, etc.) to each result."""
    trademark_df['id'] = trademark_df['id'].astype(str).str.strip()
    for result in parsed_results:
        target_id = result["Qdrant_id"]
        match_row = trademark_df[trademark_df['id'] == target_id]

        if match_row.empty:
            continue

        row = match_row.iloc[0]
        if row is not None:
            result.update({
                "reg_no": row.get("reg_no", ""),
                "ser_no": row.get("ser_no", ""),
                "Filing_date": row.get("filing_date", ""),
                "matched_image_path": get_cos_trademark_fig_path(row.get("ser_no", "")),
            })

# Generic orchestration

def search_similar_images_generic(
    to_compare_images: List[str],
    *,
    num_close: int,
    ip_type_filter: str,
    collection_name: str = COLLECTION_NAME,
    vector_name: str = VECTOR_NAME,
    filter_extra: Optional[List[qdrant_models.FieldCondition]] = None,
    parse_results_fn: Callable[[List[Dict[str, Any]]], Tuple[List[Dict[str, Any]], float]] = lambda points: (points, 0.0),
) -> Dict[str, Any]:
    """
    Generic image search pipeline:
      1) Compute embedding with Siglip
      2) Query Qdrant with ip_type filter and any extra conditions
      3) Parse results via provided parse_results_fn (domain-specific)
      4) Return structured results with timing metrics

    parse_results_fn must return: (parsed_results: List[dict], db_retrieval_time_sec: float)
    """
    if len(to_compare_images) != 1:
        raise ValueError("Currently only one image is supported for comparison.")

    # 1) Embedding
    start_time_embedding = time.time()
    query_embedding = siglip_embedding(to_compare_images[0])
    embedding_conversion_time = time.time() - start_time_embedding

    # 2) Build filters
    must_conditions: List[qdrant_models.FieldCondition] = [
        qdrant_models.FieldCondition(key="ip_type", match=qdrant_models.MatchValue(value=ip_type_filter))
    ]
    if filter_extra:
        must_conditions.extend(filter_extra)

    # 3) Qdrant query
    start_time_qdrant = time.time()
    points = query_qdrant_by_embedding(
        query_embedding=query_embedding,
        collection_name=collection_name,
        vector_name=vector_name,
        num_close=num_close,
        filter_must=must_conditions,
    )
    qdrant_retrieval_time = time.time() - start_time_qdrant

    # 4) Domain parsing
    parsed_results, db_retrieval_time = parse_results_fn(points)

    return {
        "similar_images_info": parsed_results,
        "timings": {
            "embedding_conversion_sec": embedding_conversion_time,
            "qdrant_retrieval_sec": qdrant_retrieval_time,
            "db_retrieval_sec": db_retrieval_time,
        },
    }

# Unified Flask endpoint handler

def handle_image_search_request(ip_type: str) -> tuple:
    """
    Unified Flask request handler for image search endpoints.
    Args: ip_type: Either "Patent" or "Trademark"
    Returns: Tuple of (response_data, status_code)
    """
    if 'image' not in request.files:
        return jsonify({"error": "No image file provided"}), 400

    image_file = request.files['image']
    if image_file.filename == '':
        return jsonify({"error": "No selected image file"}), 400

    num_close = request.form.get('num_close', type=int, default=10)
    reg_no_constraint = request.form.get('reg_no_constraint', type=int)  # Optional for both

    # Save the image temporarily
    temp_dir = tempfile.gettempdir()
    temp_image_path = os.path.join(temp_dir, image_file.filename)

    try:
        image_file.save(temp_image_path)

        # Build filter conditions
        filter_extra = []
        if reg_no_constraint is not None:
            print(f"Adding reg_no constraint: {reg_no_constraint}")
            filter_extra.append(
                qdrant_models.FieldCondition(key="reg_no", match=qdrant_models.MatchValue(value=str(reg_no_constraint)))
            )

        # Select the appropriate parsing function based on IP type
        if ip_type == "Patent":
            parse_results_fn = _parse_qdrant_results_patents
        elif ip_type == "Trademark":
            parse_results_fn = _parse_qdrant_results_trademarks
        else:
            return jsonify({"error": f"Unsupported IP type: {ip_type}"}), 400

        # Perform the search using the generic function
        results = search_similar_images_generic(
            to_compare_images=[temp_image_path],
            num_close=num_close,
            ip_type_filter=ip_type,
            filter_extra=filter_extra,
            parse_results_fn=parse_results_fn,
        )

        print("Search results:", results)

        return jsonify(results), 200

    except ValueError as e:
        print(f"Value error during {ip_type.lower()} image search: {e}")
        return jsonify({"error": "Invalid input for image search", "details": str(e)}), 400
    except Exception as e:
        print(f"Error during {ip_type.lower()} image search: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({"error": f"Failed to perform {ip_type.lower()} image search", "details": str(e)}), 500
    finally:
        # Clean up the temporary file
        if os.path.exists(temp_image_path):
            os.remove(temp_image_path)