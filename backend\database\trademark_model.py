from sqlalchemy import (
    Column, String, Text, Boolean, TIMESTAMP, Integer, Date
)
from sqlalchemy.dialects.postgresql import UUID as PG_UUID, JSONB, ARRAY, BIGINT
from backend.extensions import db

class Trademark(db.Model):
    __tablename__ = 'trademarks'
    __bind_key__ = 'maidalv_db'

    id = Column(PG_UUID(as_uuid=True), primary_key=True, nullable=False)
    reg_no = Column(Text)
    ser_no = Column(Text)
    tro = Column(Boolean)
    applicant_name = Column(Text)
    mark_text = Column(Text)
    int_cls = Column(ARRAY(Text))
    filing_date = Column(Date)
    plaintiff_id = Column(Integer)
    nb_suits = Column(BIGINT)
    country_codes = Column(ARRAY(Text))
    associated_marks = Column(ARRAY(Text))
    info_source = Column(Text)
    image_source = Column(Text)
    certificate_source = Column(Text)
    mark_current_status_code = Column(Integer)
    mark_feature_code = Column(Integer)
    mark_standard_character_indicator = Column(Boolean)
    mark_disclaimer_text = Column(ARRAY(Text))
    mark_disclaimer_text_daily = Column(ARRAY(Text))
    mark_image_colour_claimed_text = Column(Text)
    mark_image_colour_part_claimed_text = Column(Text)
    mark_image_colour_statement_daily = Column(ARRAY(Text))
    mark_translation_statement_daily = Column(Text)
    name_portrait_statement_daily = Column(Text)
    mark_description_statement_daily = Column(Text)
    certification_mark_statement_daily = Column(Text)
    lining_stippling_statement_daily = Column(Text)
    section_2f_statement_daily = Column(Text)
    national_design_code = Column(ARRAY(Text))
    goods_services = Column(JSONB)
    goods_services_text_daily = Column(Text)
    case_file_statements_other = Column(JSONB)
    mark_current_status_external_description_text = Column(Text)
    create_time = Column(TIMESTAMP, nullable=False)
    update_time = Column(TIMESTAMP, nullable=False)

    query = db.session.query_property()

    def __repr__(self):
        return f'<Trademark {self.reg_no}>'
