import axios from 'axios';
import apiClient from './api'; // Assuming api.js will now export apiClient


export const searchTrademarks = async (imageData, numClose, regNoConstraint) => {
    const formData = new FormData();
    formData.append('image', imageData);
    if (numClose) {
        formData.append('num_close', numClose);
    }
    if (regNoConstraint) {
        formData.append('reg_no_constraint', regNoConstraint);
    }

    try {
        const response = await axios.post(`/api/v1/trademarks/image-search`, formData, {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        });
        return response.data;
    } catch (error) {
        console.error('Error searching trademarks:', error);
        throw error;
    }
};

export const getTrademarksForExploration = (filters) => {
    // Filters object includes various search and pagination parameters
    const params = { ...filters };
    // if (Array.isArray(params.patent_types)) {
    //     params.patent_types = params.patent_types.join(',');
    // }
    // if (Array.isArray(params.selected_columns)) {
    //     params.columns = params.selected_columns.join(','); // API expects 'columns'
    //     delete params.selected_columns;
    // }
    if (params.page) params.page = Number(params.page);
    if (params.per_page) params.per_page = Number(params.per_page);

    return apiClient.get(`/api/v1/trademarks/explore`, { params });
};

export const getTrademarkImagesInfo = (trademark_reg_no) => {
    return apiClient.get(`/api/v1/trademarks/explore/${trademark_reg_no}/images`);
};

// Corresponds to GET /api/v1/patents/explore/<patent_id>
export const getTrademarkDetails = (trademarkId) => {
    return apiClient.get(`/api/v1/trademarks/explore/${trademarkId}`);
};