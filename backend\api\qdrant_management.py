import os
import json
import uuid
import re
from flask import Blueprint, jsonify, current_app, abort
from qdrant_client import QdrantClient
from qdrant_client.http.exceptions import UnexpectedResponse

from backend.extensions import db
from backend.database.models import ModelTestsModel
from backend.utils.vector_store import get_qdrant_client

qdrant_bp = Blueprint('qdrant_api', __name__, url_prefix='/api/qdrant')

# collection name prefix
COLLECTION_PREFIX = "workbench_"

def generate_uuid_from_model_id(model_id_str):
    """Generates a UUID5 from a model_id string."""
    return str(uuid.uuid5(uuid.NAMESPACE_DNS, model_id_str))

def get_model_configs():
    """Reads model configurations from models/config.json."""
    config_path = os.path.join(current_app.root_path, '..', 'models', 'config.json')
    if not os.path.exists(config_path):
        current_app.logger.error(f"Model config file not found at {config_path}")
        return None
    try:
        with open(config_path, 'r') as f:
            return json.load(f)
    except json.JSONDecodeError as e:
        current_app.logger.error(f"Error decoding model config JSON: {e}")
        return None
    except IOError as e:
        current_app.logger.error(f"Error reading model config file: {e}")
        return None

@qdrant_bp.route('/collections', methods=['GET'])
def list_qdrant_collections():
    """
    Provides a list of workbench-related Qdrant collections and their status
    relative to active models.
    """
    try:
        client = get_qdrant_client()
        if not client:
            return jsonify({"error": "Failed to connect to Qdrant"}), 500

        qdrant_collections = client.get_collections().collections
        current_app.logger.info(f"Found {len(qdrant_collections)} base collections in Qdrant.")

        # Fetch aliases
        all_aliases = client.get_aliases().aliases
        current_app.logger.info(f"Found {len(all_aliases)} aliases in Qdrant.")

        # Create a map from base collection name to workbench alias name
        base_to_workbench_alias = {
            alias.collection_name: alias.alias_name
            for alias in all_aliases
            if alias.alias_name.startswith(COLLECTION_PREFIX)
        }
        current_app.logger.debug(f"Created base_to_workbench_alias map: {base_to_workbench_alias}")

    except Exception as e:
        current_app.logger.error(f"Error connecting to or listing collections/aliases from Qdrant: {e}")
        return jsonify({"error": "Could not retrieve collections or aliases from Qdrant"}), 500

    # Fetch active models from DB
    try:
        active_db_models = db.session.query(ModelTestsModel).filter(ModelTestsModel.is_active == True).all()
        active_model_names = {model.model_name for model in active_db_models} # Use correct attribute model_name
        current_app.logger.info(f"Found {len(active_model_names)} active models in DB: {active_model_names}")
    except Exception as e:
        current_app.logger.error(f"Error fetching active models from database: {e}")
        return jsonify({"error": "Database error fetching active models"}), 500

    # Read model config
    model_configs_data = get_model_configs()
    if model_configs_data is None:
        return jsonify({"error": "Could not load model configurations"}), 500

    # Create mappings and generate expected collections
    uuid_to_model_name = {}
    expected_collections = set()

    for cfg in model_configs_data: # Iterate over the list directly
        model_name = cfg.get("model_name") # Get model_name from the dict item
        if not model_name:
            current_app.logger.warning(f"Skipping entry in config.json due to missing 'model_name': {cfg}")
            continue

        if 'model_id' not in cfg or 'applicable_ip_category' not in cfg:
            current_app.logger.warning(f"Skipping model '{model_name}' due to missing 'model_id' or 'applicable_ip_category' in config.")
            continue

        model_id_str = cfg['model_id']
        applicable_categories = cfg['applicable_ip_category'] # This is a list
        model_uuid = generate_uuid_from_model_id(model_id_str)
        # Ensure model_name is defined from the loop start
        formatted_model_name = model_name.lower().replace(' ', '_')

        uuid_to_model_name[model_uuid] = formatted_model_name
        # model_name_to_ip_category mapping might be less useful now as a model can have multiple categories
        # model_name_to_ip_category[formatted_model_name] = applicable_categories # Store the list if needed elsewhere

        # Generate expected collection names only if the model is active
        if model_name in active_model_names:
            categories_to_process = set()
            if "all" in applicable_categories:
                # If 'all' is present, assume it applies to standard categories
                categories_to_process.update(["copyright", "trademark", "patent"])
            else:
                # Otherwise, use the specific categories listed
                categories_to_process.update([cat.lower() for cat in applicable_categories])

            for ip_category in categories_to_process:
                 expected_collection_name = f"{COLLECTION_PREFIX}{ip_category}_{formatted_model_name}"
                 expected_collections.add(expected_collection_name)
                 current_app.logger.debug(f"Expecting active collection: {expected_collection_name} for model '{model_name}'")

    current_app.logger.info(f"Total expected active collections based on config and DB: {len(expected_collections)}")

    # Process Qdrant collections, prioritizing aliases
    results = []
    processed_base_collections = set() # Keep track of base collections handled via alias

    for collection in qdrant_collections:
        base_collection_name = collection.name
        entry = {"name": None, "ip_category": None, "model_name": None, "is_active": False, "needs_migration": False, "points_count": None}

        # Check if this base collection has a workbench alias
        alias_name = base_to_workbench_alias.get(base_collection_name)

        if alias_name:
            # Process using the alias
            entry["name"] = alias_name
            entry["needs_migration"] = False
            processed_base_collections.add(base_collection_name) # Mark as handled

            parts = alias_name[len(COLLECTION_PREFIX):].split('_', 1)
            if len(parts) == 2:
                entry["ip_category"] = parts[0]
                entry["model_name"] = parts[1] # Already formatted (lowercase, underscores)
                entry["is_active"] = alias_name in expected_collections

                # Fetch points_count for the base collection
                actual_qdrant_name_for_info = base_collection_name
                try:
                    collection_info = client.get_collection(collection_name=actual_qdrant_name_for_info)
                    entry["points_count"] = collection_info.points_count
                except Exception as e_info:
                    current_app.logger.error(f"Could not get info for collection {actual_qdrant_name_for_info} (aliased as {alias_name}): {e_info}")
                    entry["points_count"] = None # Or -1, or some error indicator

                results.append(entry)
                current_app.logger.debug(f"Processed collection via alias: {entry}")
            else:
                 current_app.logger.warning(f"Could not parse workbench alias name: {alias_name} for base collection {base_collection_name}")

        elif base_collection_name.startswith(COLLECTION_PREFIX) and base_collection_name not in processed_base_collections:
            entry["name"] = base_collection_name
            # If a collection name directly starts with the prefix, it implies it's already in the new format.
            # Thus, it likely doesn't need migration in the sense of renaming to add the prefix.
            entry["needs_migration"] = False

            # Parse ip_category and model_name from the base_collection_name
            # The prefix is defined as COLLECTION_PREFIX = "workbench_"
            parts = base_collection_name[len(COLLECTION_PREFIX):].split('_', 1)
            if len(parts) == 2:
                entry["ip_category"] = parts[0]
                entry["model_name"] = parts[1] # This is the formatted model name
                # Determine if this directly named collection is active by checking against 'expected_collections'
                entry["is_active"] = base_collection_name in expected_collections

                # Fetch points_count for the collection
                actual_qdrant_name_for_info = base_collection_name
                try:
                    collection_info = client.get_collection(collection_name=actual_qdrant_name_for_info)
                    entry["points_count"] = collection_info.points_count
                except Exception as e_info:
                    current_app.logger.error(f"Could not get info for collection {actual_qdrant_name_for_info}: {e_info}")
                    entry["points_count"] = None # Or -1, or some error indicator

                results.append(entry)
                current_app.logger.debug(f"Processed collection directly by name: {entry}")
            else:
                current_app.logger.warning(f"Could not parse workbench collection name (direct match): {base_collection_name}")
        
        elif base_collection_name not in processed_base_collections: # Fallback for truly unhandled collections
            current_app.logger.debug(f"Skipping collection '{base_collection_name}' - doesn't match alias or workbench prefix.")


    current_app.logger.info(f"Returning {len(results)} processed workbench-related collections.")
    return jsonify(results)


@qdrant_bp.route('/collections/<path:collection_name>', methods=['DELETE'])
def delete_qdrant_collection(collection_name):
    """
    Securely deletes a specific Qdrant collection.
    """
    # Safety Check
    if not collection_name.startswith(COLLECTION_PREFIX):
        current_app.logger.warning(f"Attempt to delete non-workbench collection denied: {collection_name}")
        # 403 Forbidden is more appropriate than 400 Bad Request for an unauthorized action
        abort(403, description=f"Collection name '{collection_name}' does not match allowed patterns.")

    try:
        client = get_qdrant_client()
        if not client:
            return jsonify({"error": "Failed to connect to Qdrant"}), 500

        current_app.logger.info(f"Attempting to delete Qdrant collection: {collection_name}")
        # The delete_collection operation returns a boolean in recent versions
        success = client.delete_collection(collection_name=collection_name)

        if success:
            current_app.logger.info(f"Successfully deleted Qdrant collection: {collection_name}")
            return jsonify({"message": f"Collection '{collection_name}' deleted successfully."}), 200
        else:
            # This path might be less common if exceptions are raised first, but good to handle
            current_app.logger.error(f"Qdrant client reported failure deleting collection: {collection_name}, but no exception raised.")
            return jsonify({"error": f"Failed to delete collection '{collection_name}'. Reason unknown."}), 500

    except UnexpectedResponse as e:
        if e.status_code == 404:
            current_app.logger.warning(f"Attempted to delete non-existent collection: {collection_name}")
            abort(404, description=f"Collection '{collection_name}' not found.")
        else:
            current_app.logger.error(f"Unexpected Qdrant error deleting collection {collection_name}: {e}")
            return jsonify({"error": f"Qdrant error: {e.reason} (Status: {e.status_code})"}), e.status_code
    except Exception as e:
        current_app.logger.error(f"Generic error deleting Qdrant collection {collection_name}: {e}")
        return jsonify({"error": "An unexpected error occurred while deleting the collection."}), 500