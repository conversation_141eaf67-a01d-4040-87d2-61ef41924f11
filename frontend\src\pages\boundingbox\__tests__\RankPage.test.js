import React from 'react';
import { render, screen } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import RankPage from '../RankPage';

// Mock the API service used by RankPage
jest.mock('../../services/api_bounding_box', () => ({
  getBbRankData: jest.fn(() => Promise.resolve({ models: [], experiments: [] })),
}));

describe('RankPage', () => {
  test('renders the page title', () => {
    render(
      <MemoryRouter>
        <RankPage />
      </MemoryRouter>
    );
    expect(screen.getByText(/Model Ranking \(Bounding Box\)/i)).toBeInTheDocument();
  });

  test('displays loading state initially', async () => {
    render(
      <MemoryRouter>
        <RankPage />
      </MemoryRouter>
    );
    // Check for loading text or a CircularProgress role (if it has one)
    // Using findByText to wait for potential state changes if loading is very brief
    expect(await screen.findByText(/Loading model ranking.../i)).toBeInTheDocument();
  });

  test('displays "no models rated" message when data is empty', async () => {
    // Ensure the mock resolves with empty data that would lead to this message
    jest.spyOn(require('../../services/api_bounding_box'), 'getBbRankData').mockResolvedValueOnce({ models: [], experiments: [] });

    render(
      <MemoryRouter>
        <RankPage />
      </MemoryRouter>
    );
    // Wait for loading to complete and then check for the message
    expect(await screen.findByText(/No models have been rated yet or no data available for ranking./i)).toBeInTheDocument();
  });
});
