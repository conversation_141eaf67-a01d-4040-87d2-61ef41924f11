server {
    listen 80;
    server_name localhost;

    # Increase max request body size to 100MB to handle large file uploads
    client_max_body_size 100M;

    location / {
        root /usr/share/nginx/html;
        index index.html index.htm;
        try_files $uri $uri/ /index.html;
    }

    location /api/ {
        proxy_pass http://backend:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location ~ ^/api/v1/copyright/(bulk/(set|unset)_prod/stream|cn-websites-files/move/stream|image/upload/stream)$ {
        proxy_pass http://backend:5000;


        # SSE specific settings
        proxy_http_version 1.1;
        proxy_set_header Connection '';
        
        # absolutely critical: disable buffering/caching so events flush immediately
        proxy_buffering off;
        proxy_cache off;
        gzip off;
        add_header X-Accel-Buffering no;

        # keep the connection open for long jobs
        proxy_read_timeout 1h;
        proxy_send_timeout 1h;
        send_timeout 1h;

        # standard headers you already set elsewhere
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # optional: hint browsers/CDNs not to cache
        add_header Cache-Control "no-cache";
    }

    error_page 500 502 503 504 /50x.html;
    location = /50x.html { root /usr/share/nginx/html; }
}