# backend/logic/metrics.py
from sqlalchemy import func, case, desc, and_, not_, select, literal_column, or_ # Added or_
from sqlalchemy.orm import aliased
from backend.extensions import db
from backend.database.models import (
    ModelTestsComparisonResult,
    ModelTestsGroundTruth,
    ModelTestsImage, # Represents both ProductImage and IPImage
    ModelTestsModel, # Renamed from ModelConfig
    ModelTestsCombinedScoresConfig, # Renamed from CombinedScoreConfig
)
import uuid # Import uuid

# Define a constant for the maximum rank to assign when a GT pair is not found
# Assuming N=200 results are stored per product image, N+1 = 201
MISSING_GT_RANK = 201
# Define N for Top-N non-GT results
TOP_N = 200

def calculate_precision_avg_rank(model_id: uuid.UUID, ip_category: str) -> float | None:
    """
    Calculates the average rank of ground truth matches for a given model and IP category.

    Args:
        model_id: The UUID of the model or combined score config.
        ip_category: The IP category ('patent', 'trademark', 'copyright').

    Returns:
        The average rank, or None if no data is available.
    """
    # Subquery to rank comparison results for each product image
    RankedResults = (
        select(
            ModelTestsComparisonResult.product_image_id, # Correct class
            ModelTestsComparisonResult.ip_image_id, # Correct class
            func.rank()
            .over(
                order_by=ModelTestsComparisonResult.similarity_score.desc(), # Correct column name
                partition_by=ModelTestsComparisonResult.product_image_id, # Correct class
            )
            .label("rank"),
        )
        .where(ModelTestsComparisonResult.model_id == model_id) # Correct class
        .subquery("ranked_results")
    )

    # Alias ModelTestsGroundTruth for clarity in the join
    GT = aliased(ModelTestsGroundTruth) # Correct class

    # Query to get the rank for each ground truth pair
    # Join ModelTestsImage (product) -> ModelTestsGroundTruth -> RankedResults (LEFT JOIN to handle missing GTs)
    # Need aliases for ModelTestsImage to distinguish product and IP
    ProductImg = aliased(ModelTestsImage)
    IpImg = aliased(ModelTestsImage)

    query = (
        db.session.query(
            ProductImg.image_id.label("product_image_id"), # Correct class and column
            GT.correct_ip_image_id,
            # Use coalesce to assign MISSING_GT_RANK if no rank is found (GT pair not in results)
            func.coalesce(RankedResults.c.rank, MISSING_GT_RANK).label("gt_rank"),
        )
        .select_from(ProductImg) # Start select from ProductImg alias
        .join(GT, ProductImg.image_id == GT.product_image_id) # Join ProductImg to GT using correct columns
        .join(IpImg, GT.correct_ip_image_id == IpImg.image_id) # Join GT to IpImg alias using correct columns
        .outerjoin(
            RankedResults,
            and_(
                GT.product_image_id == RankedResults.c.product_image_id,
                GT.correct_ip_image_id == RankedResults.c.ip_image_id,
            ),
        )
        .filter(ProductImg.image_type == 'product') # Ensure we start with products
        .filter(IpImg.ip_category == ip_category) # Filter based on the IP image's category
    )

    # Calculate the average rank per product
    # We need to average the gt_rank for each product_image_id
    # Then average those averages.

    # Step 1: Get rank for each GT pair
    gt_ranks_data = query.all()
    if not gt_ranks_data:
        return None

    # Step 2: Group ranks by product_image_id
    product_ranks = {}
    for row in gt_ranks_data:
        product_id = row.product_image_id
        rank = row.gt_rank
        if product_id not in product_ranks:
            product_ranks[product_id] = []
        product_ranks[product_id].append(rank)

    # Step 3: Calculate average rank per product
    product_avg_ranks = []
    for product_id, ranks in product_ranks.items():
        if ranks:
            avg_rank = sum(ranks) / len(ranks)
            product_avg_ranks.append(avg_rank)

    # Step 4: Calculate overall average rank
    if not product_avg_ranks:
        return None

    overall_avg_rank = sum(product_avg_ranks) / len(product_avg_ranks)
    return overall_avg_rank


def calculate_score_distribution(model_id: uuid.UUID, ip_category: str) -> dict:
    """
    Calculates the distribution of scores for Ground Truth (GT) pairs vs. non-GT pairs.

    Args:
        model_id: The UUID of the model or combined score config.
        ip_category: The IP category ('patent', 'trademark', 'copyright').

    Returns:
        A dictionary containing lists of GT scores and non-GT scores.
        e.g., { "gt_scores": [...], "non_gt_scores": [...] }
    """
    # 1. Get all GT pairs for the category
    # Need aliases for ModelTestsImage
    ProductImg = aliased(ModelTestsImage)
    IpImg = aliased(ModelTestsImage)
    gt_pairs_query = (
        db.session.query(ModelTestsGroundTruth.product_image_id, ModelTestsGroundTruth.correct_ip_image_id) # Correct class
        .join(ProductImg, ModelTestsGroundTruth.product_image_id == ProductImg.image_id) # Correct class and column
        .join(IpImg, ModelTestsGroundTruth.correct_ip_image_id == IpImg.image_id) # Correct class and column
        .filter(ProductImg.image_type == 'product') # Ensure product image is product type
        .filter(IpImg.ip_category == ip_category) # Filter based on IP image category
    )
    gt_pairs = {(row.product_image_id, row.correct_ip_image_id) for row in gt_pairs_query.all()}

    if not gt_pairs:
        return {"gt_scores": [], "non_gt_scores": []}

    # 2. Fetch all relevant comparison results for the model and category
    # We need product_image_id to filter by category indirectly
    # Need aliases for ModelTestsImage
    ProductImg = aliased(ModelTestsImage)
    IpImg = aliased(ModelTestsImage)
    all_results_query = (
        db.session.query(
            ModelTestsComparisonResult.product_image_id, # Correct class
            ModelTestsComparisonResult.ip_image_id, # Correct class
            ModelTestsComparisonResult.similarity_score, # Correct column name
        )
        .join(ProductImg, ModelTestsComparisonResult.product_image_id == ProductImg.image_id) # Correct class and column
        .join(IpImg, ModelTestsComparisonResult.ip_image_id == IpImg.image_id) # Correct class and column
        .filter(ModelTestsComparisonResult.model_id == model_id) # Correct class
        .filter(ProductImg.image_type == 'product') # Ensure product image is product type
        .filter(IpImg.ip_category == ip_category) # Filter based on IP image category
        # Optimization: Order and limit here if possible, but we need all for GT/non-GT split
        # Order by score to potentially limit non-GT later if needed, though splitting first is safer.
        .order_by(ModelTestsComparisonResult.product_image_id, ModelTestsComparisonResult.similarity_score.desc()) # Correct class and column
    )

    all_results = all_results_query.all()

    gt_scores = []
    non_gt_scores = []
    processed_products = {} # Keep track of non-GT scores per product to limit to Top N

    for result in all_results:
        pair = (result.product_image_id, result.ip_image_id)
        product_id = result.product_image_id

        if pair in gt_pairs:
            gt_scores.append(result.similarity_score) # Correct column name
        else:
            # Add to non-GT scores, respecting TOP_N per product
            if product_id not in processed_products:
                processed_products[product_id] = 0
            if processed_products[product_id] < TOP_N:
                 non_gt_scores.append(result.similarity_score) # Correct column name
                 processed_products[product_id] += 1


    return {"gt_scores": gt_scores, "non_gt_scores": non_gt_scores}


def calculate_confusion_matrix_data(model_id: uuid.UUID, ip_category: str, threshold: float) -> dict:
    """
    Calculates confusion matrix data (TP, FN, FP, TN) based on a score threshold.

    Args:
        model_id: The UUID of the model or combined score config.
        ip_category: The IP category ('patent', 'trademark', 'copyright').
        threshold: The score threshold to classify matches.

    Returns:
        A dictionary containing TP, FN, FP, TN counts.
        e.g., { "tp": count, "fn": count, "fp": count, "tn": count }
    """
    # 1. Get GT pairs for the category
    # Need aliases for ModelTestsImage
    ProductImg = aliased(ModelTestsImage)
    IpImg = aliased(ModelTestsImage)
    gt_pairs_query = (
        db.session.query(ModelTestsGroundTruth.product_image_id, ModelTestsGroundTruth.correct_ip_image_id) # Correct class
        .join(ProductImg, ModelTestsGroundTruth.product_image_id == ProductImg.image_id) # Correct class and column
        .join(IpImg, ModelTestsGroundTruth.correct_ip_image_id == IpImg.image_id) # Correct class and column
        .filter(ProductImg.image_type == 'product') # Ensure product image is product type
        .filter(IpImg.ip_category == ip_category) # Filter based on IP image category
    )
    gt_pairs = {(row.product_image_id, row.correct_ip_image_id) for row in gt_pairs_query.all()}

    print(f"calculate_confusion_matrix_data: Found {len(gt_pairs)} ground truth pairs for category {ip_category}") # Added logging

    if not gt_pairs:
        return {"tp": 0, "fn": 0, "fp": 0, "tn": 0}

    # 2. Fetch all relevant comparison results (scores) for the model and category
    # We need to rank results per product to consider only Top N non-GT pairs for TN/FP
    # Need aliases for ModelTestsImage
    ProductImg = aliased(ModelTestsImage)
    IpImg = aliased(ModelTestsImage)
    RankedResults = (
        select(
            ModelTestsComparisonResult.product_image_id, # Correct class
            ModelTestsComparisonResult.ip_image_id, # Correct class
            ModelTestsComparisonResult.similarity_score, # Correct column name
            func.rank()
            .over(
                order_by=ModelTestsComparisonResult.similarity_score.desc(), # Correct column name
                partition_by=ModelTestsComparisonResult.product_image_id, # Correct class
            )
            .label("rank"),
        )
        .join(ProductImg, ModelTestsComparisonResult.product_image_id == ProductImg.image_id) # Correct class and column
        .join(IpImg, ModelTestsComparisonResult.ip_image_id == IpImg.image_id) # Correct class and column
        .where(ModelTestsComparisonResult.model_id == model_id) # Correct class
        .where(ProductImg.image_type == 'product') # Ensure product image is product type
        .where(IpImg.ip_category == ip_category) # Filter based on IP image category
        .subquery("ranked_results")
    )

    results_query = db.session.query(
        RankedResults.c.product_image_id,
        RankedResults.c.ip_image_id,
        RankedResults.c.similarity_score, # Correct column name
        RankedResults.c.rank,
    )

    all_results = results_query.all()

    print(f"calculate_confusion_matrix_data: Found {len(all_results)} comparison results for model {model_id} and category {ip_category}") # Added logging

    tp = 0
    fn = 0
    fp = 0
    # tn requires knowing the total number of possible non-GT pairs, which is hard.
    # Let's define TN based on the Top N non-GT pairs.
    # A non-GT pair within the Top N results is considered a Negative case.
    # If its score < threshold, it's a True Negative.
    # If its score >= threshold, it's a False Positive.
    tn = 0

    # Keep track of which GT pairs were found in the results
    found_gt_pairs = set()

    for result in all_results:
        pair = (result.product_image_id, result.ip_image_id)
        score = result.similarity_score # Correct column name
        rank = result.rank

        is_gt = pair in gt_pairs

        if is_gt:
            found_gt_pairs.add(pair)
            # Calculation moved below to handle rank check first

        # Consider only Top N results for FP/TN calculation
        if rank <= TOP_N:
            if is_gt:
                if score >= threshold:
                    tp += 1
                else:
                    fn += 1 # GT pair found but below threshold
            else: # Is Non-GT within Top N
                if score >= threshold:
                    fp += 1
                else:
                    tn += 1

    # Account for GT pairs that were *not found* in the results at all (implicitly score < threshold)
    missed_gt_count = len(gt_pairs) - len(found_gt_pairs)
    fn += missed_gt_count

    print(f"calculate_confusion_matrix_data: Calculated TP={tp}, FN={fn}, FP={fp}, TN={tn} for model {model_id}, category {ip_category}, threshold {threshold}") # Added logging

    return {"tp": tp, "fn": fn, "fp": fp, "tn": tn}

def get_active_models_for_category(ip_category: str) -> list:
    """
    Fetches active models and combined score configurations applicable to a category.
    """
    # Fetch active base models
    models = db.session.scalars(select(ModelTestsModel).where( # Use correct class and select
        ModelTestsModel.is_active == True,
        # Use array contains operator `any`
        or_(
             ModelTestsModel.applicable_ip_category.any(ip_category),
             ModelTestsModel.applicable_ip_category.any('all')
        )
    )).all()
    # Fetch active combined score configs
    combined_scores = db.session.scalars(select(ModelTestsCombinedScoresConfig).where( # Use correct class and select
        ModelTestsCombinedScoresConfig.is_active == True,
        ModelTestsCombinedScoresConfig.ip_category == ip_category # Direct comparison for this table
    )).all()

    result_list = []
    for model in models:
        result_list.append({
            "id": str(model.model_id), # Use correct attribute and convert UUID to string
            "name": model.model_name, # Use correct attribute
            "type": "model"
        })
    for config in combined_scores:
         # Combined score IDs are typically negative or have a prefix in other parts,
         # but here we use the primary key. Ensure consistency if needed elsewhere.
         # For calculations, we use the actual ID.
        result_list.append({
            "id": str(config.config_id), # Use correct attribute and convert UUID to string
            "name": config.config_name, # Use correct attribute
            "type": "combined"
        })
    return result_list