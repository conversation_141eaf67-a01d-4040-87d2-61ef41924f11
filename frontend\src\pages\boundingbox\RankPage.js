import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Paper,
  CircularProgress,
  Alert,
  Box
} from '@mui/material';
import { getBbRankData } from '../../services/api_bounding_box';

// Helper to format rank number
const RankIcon = ({ rank, isNA = false }) => (
  <Box sx={{
    width: 30, height: 30, borderRadius: '50%',
    backgroundColor: isNA ? 'grey.400' : 'primary.main',
    color: 'white', display: 'flex', alignItems: 'center', justifyContent: 'center',
    fontWeight: 'bold', fontSize: isNA ? '10px' : '14px'
  }}>
    {isNA ? 'N/A' : rank}
  </Box>
);

const RankPage = () => {
  const [rankedModels, setRankedModels] = useState([]);
  const [isLoading, setIsLoading] = useState(true); // Start with loading true
  const [error, setError] = useState(null);

  useEffect(() => {
    const calculateAndSetRankings = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const { models: allModels, experiments: allExperiments } = await getBbRankData();

        if (!allModels || !allExperiments) {
          setRankedModels([]);
          setError("Could not retrieve all necessary data for ranking.");
          return;
        }

        const modelScores = {}; // { modelId: { totalScore: X, count: Y, name: Z, id: ID } }

        allModels.forEach(model => {
          modelScores[model.id] = { totalScore: 0, count: 0, name: model.name, id: model.id };
        });

        allExperiments.forEach(experiment => {
          if (experiment.results && Array.isArray(experiment.results)) {
            experiment.results.forEach(result => {
              // Include scores from both successful and failed results
              if (result.model_id && result.score !== null && result.score !== undefined) {
                if (modelScores[result.model_id]) {
                  modelScores[result.model_id].totalScore += result.score;
                  modelScores[result.model_id].count += 1;
                }
              }
            });
          }
        });

        // Calculate additional statistics for each model
        const modelsWithRatings = Object.values(modelScores)
          .filter(model => model.count > 0)
          .map(model => {
            // Calculate success/failure breakdown
            let successCount = 0;
            let failureCount = 0;

            allExperiments.forEach(experiment => {
              if (experiment.results && Array.isArray(experiment.results)) {
                experiment.results.forEach(result => {
                  if (result.model_id === model.id && result.score !== null && result.score !== undefined) {
                    if (result.status === 'success') {
                      successCount++;
                    } else if (result.status === 'failed') {
                      failureCount++;
                    }
                  }
                });
              }
            });

            return {
              ...model,
              averageScore: model.totalScore / model.count,
              hasRating: true,
              successCount,
              failureCount
            };
          })
          .sort((a, b) => b.averageScore - a.averageScore); // Sort descending by average score

        const modelsWithoutRatings = Object.values(modelScores)
          .filter(model => model.count === 0)
          .map(model => ({
            ...model,
            averageScore: null,
            hasRating: false
          }))
          .sort((a, b) => a.name.localeCompare(b.name)); // Sort alphabetically

        // Combine: rated models first, then unrated models
        const allRankedModels = [...modelsWithRatings, ...modelsWithoutRatings];

        setRankedModels(allRankedModels);

      } catch (err) {
        setError('Failed to load or process ranking data.');
        console.error("Error in ranking calculation:", err);
        setRankedModels([]);
      } finally {
        setIsLoading(false);
      }
    };

    calculateAndSetRankings();
  }, []);


  if (isLoading) {
    return (
      <Container maxWidth="xl" sx={{ mt: 4, mb: 4, textAlign: 'center' }}>
        <CircularProgress />
        <Typography sx={{ mt: 1 }}>Loading model ranking...</Typography>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" gutterBottom component="h1">
        Model Ranking (Bounding Box)
      </Typography>

      <Alert severity="info" sx={{ mb: 2 }}>
        Rankings include scores from both successful detections and failed attempts.
        Failed results are scored based on error handling quality (0=poor, 10=helpful).
      </Alert>

      {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}

      {!isLoading && !error && rankedModels.length === 0 && (
        <Alert severity="info">No models found.</Alert>
      )}

      {rankedModels.length > 0 && (
        <Paper elevation={2}>
          <List>
            {rankedModels.map((model, index) => {
              const isRated = model.hasRating;
              const rank = isRated ? index + 1 : null;

              return (
                <ListItem key={model.id} divider>
                  <ListItemIcon>
                    <RankIcon rank={rank} isNA={!isRated} />
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Typography variant="h6" component="span">
                          {model.name}
                        </Typography>
                        <Typography variant="h6" component="span" sx={{ fontWeight: 'bold', color: isRated ? 'primary.main' : 'text.secondary' }}>
                          {isRated ? model.averageScore.toFixed(2) : 'N/A'}
                        </Typography>
                      </Box>
                    }
                    secondary={
                      isRated
                        ? (
                          <Box>
                            <Typography variant="body2" component="span">
                              {`Based on ${model.count} rating${model.count !== 1 ? 's' : ''}`}
                            </Typography>
                            {(model.successCount > 0 || model.failureCount > 0) && (
                              <Typography variant="body2" component="span" sx={{ ml: 1, color: 'text.secondary' }}>
                                ({model.successCount} successful, {model.failureCount} failed)
                              </Typography>
                            )}
                          </Box>
                        )
                        : 'No ratings yet'
                    }
                  />
                </ListItem>
              );
            })}
          </List>
        </Paper>
      )}
    </Container>
  );
};

export default RankPage;
