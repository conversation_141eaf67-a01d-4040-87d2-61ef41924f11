# Image Searcher Architecture

## 1. Overview
This document outlines the architecture for the new "Image Searcher" section of the Patent Visualization Platform. This section will allow users to input text, convert it into an embedding, and find the most similar patent titles from the Qdrant vector store.

## 2. System Decomposition and Responsibilities

### Frontend (React Application)
*   **ImageSearcherPage.js**: A new React page to host the image searcher functionality.
*   **ImageSearcherComponent.js**: A reusable React component within the page for user input (text field), displaying search results, and handling API calls.
*   **api_image_searcher.js**: A new service file to encapsulate API calls related to the image searcher.

### Backend (Flask Application)
*   **image_searcher_api.py**: A new Flask Blueprint to define the API endpoint for text-to-embedding search.
*   **Existing Qdrant Client Integration**: Reuses the existing `QdrantClient` and `SentenceTransformer` model for embedding generation and Qdrant interaction.

### Data Layer
*   **Qdrant Vector Store**: Stores patent title embeddings.
*   **PostgreSQL Database**: Stores patent metadata (e.g., `reg_no`, `patent_title`).

## 3. Interface Design and Data Flow

### API Endpoint
*   **Endpoint**: `/api/image_searcher/search_patent_titles`
*   **Method**: `POST`
*   **Request Body**:
    ```json
    {
        "query_text": "string"
    }
    ```
*   **Response Body (Success - 200 OK)**:
    ```json
    [
        {
            "id": "string (UUID of the Qdrant point)",
            "score": "number (similarity score)",
            "reg_no": "string (Patent Registration Number)"
        },
        ...
    ]
    ```
*   **Response Body (Error - 400/500)**:
    ```json
    {
        "error": "string (error message)"
    }
    ```

### Data Flow Diagram

```mermaid
graph TD
    A[User] -->|Enters Text Query| B(Frontend: ImageSearcherComponent)
    B -->|POST /api/image_searcher/search_patent_titles| C(Backend: image_searcher_api.py)
    C -->|Load SentenceTransformer Model| D(SentenceTransformer)
    C -->|Encode Query Text| D
    D -->|Query Embedding| C
    C -->|Search Qdrant Collection (Patent_Title_bge_small)| E(Qdrant Vector Store)
    E -->|Similar Patent IDs & Scores| C
    C -->|Retrieve Patent Details (Optional, if needed from DB)| F(PostgreSQL Database)
    F -->|Patent Metadata| C
    C -->|JSON Response (Patent Titles, Scores)| B
    B -->|Display Results| A
```

## 4. Security and Performance Considerations
*   **API Key Management**: Ensure Qdrant API key is securely managed via environment variables.
*   **Input Validation**: Implement robust input validation on the backend for `query_text` to prevent injection attacks or malformed requests.
*   **Rate Limiting**: Consider implementing rate limiting on the API endpoint to prevent abuse.
*   **Scalability**: The Qdrant client and SentenceTransformer model loading should be optimized for performance. Model loading should ideally happen once or be cached.
*   **Error Handling**: Comprehensive error handling and logging should be in place for all API interactions and external service calls.

## 5. Future Enhancements
*   **Image Search**: Extend functionality to allow image input for search, requiring a different embedding model (e.g., CLIP, SigLIP).
*   **Filtering/Sorting**: Add options to filter and sort search results.
*   **Pagination**: Implement pagination for large result sets.