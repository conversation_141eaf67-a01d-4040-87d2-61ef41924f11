"""
This module handles the addition of new copyright files.
"""
from flask import g, request, jsonify, current_app
from backend.api.copyright.helpers import generate_md_registration_number, zero_pad_reg_no, check_registration_number_exists, upsert_copyrights_table, parse_registration_number
from backend.api.copyright.move_cn_to_copyright import upload_to_cos
from backend.api.copyright.handle_prod import process_copyright_production
from backend.api.copyright.Copyright_USCO import get_info_from_USCO_using_reg_no
from backend.utils.db_utils import safe_transaction
from backend.utils.image_resize import create_resized_image
from backend.extensions import db
from sqlalchemy.exc import IntegrityError
from sqlalchemy import text
import os
import tempfile
import shutil
import threading
import time
import uuid
import redis
import json
import re
import asyncio

# Redis client for shared session storage
redis_client = redis.Redis(host='redis', port=6379, decode_responses=True)

def get_session_data(session_id):
    """Get session data from Redis"""
    data = redis_client.get(f"upload_session:{session_id}")
    return json.loads(data) if data else None

def set_session_data(session_id, data):
    """Set session data in Redis with 1 hour expiration"""
    redis_client.setex(f"upload_session:{session_id}", 3600, json.dumps(data))

def update_session_data(session_id, data):
    """Update specific fields in session data"""
    current = get_session_data(session_id)
    if current:
        current.update(data)
        set_session_data(session_id, current)

def delete_session_data(session_id):
    """Delete session data from Redis"""
    redis_client.delete(f"upload_session:{session_id}")


def add_copyright_image_with_progress():
    """
    Handle image upload with progress tracking.
    Returns a session ID that can be used to track progress via SSE.
    """
    if 'files' not in request.files:
        return jsonify({"success": False, "error": "No files part"}), 400

    files = request.files.getlist('files')
    if not files or all(file.filename == '' for file in files):
        return jsonify({"success": False, "error": "No files selected"}), 400

    # Filter out empty filenames
    files = [file for file in files if file.filename != '']

    if not files:
        return jsonify({"success": False, "error": "No valid files selected"}), 400

    reg_no = request.form.get('registration_number')
    plaintiff_id = request.form.get('plaintiff_id')
    method = request.form.get('method', 'Manual')
    type_ = request.form.get('type')
    certificate_status = request.form.get('certificate_status')
    production_str = request.form.get('production')
    session_id = request.form.get('session_id')

    if not production_str:
        return jsonify({"success": False, "error": "Missing required field: production"}), 400

    production = production_str.lower() in ['true', '1', 't']

    if not plaintiff_id:
        return jsonify({"success": False, "error": "Missing required field: plaintiff_id"}), 400

    try:
        plaintiff_id = int(plaintiff_id)
    except (ValueError, TypeError):
        return jsonify({"success": False, "error": "Invalid plaintiff_id"}), 400

    if not session_id:
        return jsonify({"success": False, "error": "Missing session_id"}), 400

    # Save uploaded files to temporary files before starting background thread
    temp_file_paths = []
    temp_upload_dir = tempfile.mkdtemp(prefix="upload_")

    try:
        for file in files:
            if file.filename:
                temp_path = os.path.join(temp_upload_dir, file.filename)
                file.save(temp_path)
                temp_file_paths.append((temp_path, file.filename))

        # Initialize session progress
        set_session_data(session_id, {
            'progress': {'current': 0, 'total': len(temp_file_paths), 'successful': 0, 'failed': 0},
            'logs': [],
            'status': 'processing'
        })

        # Get the app context before starting the thread
        app = current_app._get_current_object()

        # Start processing in background thread with Flask app context
        def process_images():
            try:
                with app.app_context():
                    _process_images_background(temp_file_paths, reg_no, plaintiff_id, method, type_, certificate_status, production, session_id)
            except Exception as e:
                current_session = get_session_data(session_id)
                if current_session:
                    current_session['status'] = 'error'
                    current_session['logs'].append({
                        'type': 'error',
                        'message': f'Processing failed: {str(e)}'
                    })
                    set_session_data(session_id, current_session)
            finally:
                # Clean up temporary files
                shutil.rmtree(temp_upload_dir, ignore_errors=True)

        # We run the job in a thread so that we can return the session id
        thread = threading.Thread(target=process_images)
        thread.start()

    except Exception as e:
        # Clean up on error
        shutil.rmtree(temp_upload_dir, ignore_errors=True)
        return jsonify({"success": False, "error": f"Failed to save temporary files: {str(e)}"}), 500

    return jsonify({"success": True, "session_id": session_id}), 200


def _process_single_image(temp_file_path, original_filename, file_reg_no, plaintiff_id, method, type_, certificate_status, production, temp_upload_dir, temp_output_dir, session_data):
    """
    Process a single image with its own database transaction.
    Returns (success: bool, file_id: str or None, error: str or None)
    """
    engine = db.get_engine(bind='maidalv_db')
    with engine.connect() as conn:
        with conn.begin() as trans:
            try:
                # Generate filename with suffix if using same registration number
                final_filename = f"{file_reg_no}_{method}.webp"

                # Copy file to processing directory with new name
                final_file_path = os.path.join(temp_upload_dir, final_filename)
                shutil.copy2(temp_file_path, final_file_path)

                # Resize image
                resized_filename = create_resized_image(temp_upload_dir, final_filename, temp_output_dir)
                if resized_filename is None:
                    raise Exception(f"Image resizing failed for {original_filename}")

                # Create copyright entry for each MD number if needed
                try:
                    conn.execute(text("""
                        INSERT INTO copyrights (registration_number, tro, plaintiff_id, certificate_status)
                        VALUES (:reg_no, :tro, :plaintiff_id, :certificate_status)
                    """), {
                        "reg_no": file_reg_no,
                        "tro": True,
                        "plaintiff_id": plaintiff_id,
                        "certificate_status": certificate_status
                    })
                    print(f"Copyright entry created for {file_reg_no}")
                except Exception as e:
                    # Check for duplicate key errors
                    if "duplicate key" in str(e).lower() or "already exists" in str(e).lower():
                        print(f"Copyright entry already exists for {file_reg_no}")
                        pass  # Ignore duplicates
                    else:
                        print(f"Error creating copyright entry for {file_reg_no}: {e}")
                        raise e

                insert_stmt = text("""
                    INSERT INTO copyrights_files (filename, registration_number, method, production, type)
                    VALUES (:filename, :reg_no, :method, :production, :type)
                    RETURNING id, filename, registration_number, method, type, production
                """)

                result = conn.execute(insert_stmt, {
                    "filename": final_filename,
                    "reg_no": file_reg_no,
                    "method": method,
                    "production": production,
                    "type": type_
                }).first()
                print(f"Copyright file entry created for {final_filename}")

                file_id = result[0]
                asset_row = {
                    "id": str(file_id),
                    "filename": result[1],
                    "registration_number": result[2],
                    "method": result[3],
                    "type": result[4],
                    "production": result[5],
                    "plaintiff_id": plaintiff_id
                }

                # Upload to COS
                list(upload_to_cos(plaintiff_id, resized_filename, temp_output_dir))

                # Call production process if needed
                if production:
                    success, message, _ = process_copyright_production(
                        file_id=file_id,
                        reg_no=file_reg_no,
                        production=production,
                        filename=final_filename,
                        asset_row=asset_row
                    )

                    if not success:
                        raise Exception(f"Production processing failed for {original_filename}: {message}")
                
                trans.commit()
                return True, str(file_id), None

            except Exception as e:
                trans.rollback()
                error_msg = str(e)
                print(f"Error processing {original_filename}: {error_msg}")
                return False, None, error_msg


def _process_images_background(temp_file_paths, reg_no, plaintiff_id, method, type_, certificate_status, production, session_id):
    """
    Process images in the background and update progress.
    temp_file_paths: List of tuples (temp_file_path, original_filename)
    """
    print(f"Starting background image processing for {len(temp_file_paths)} files ...")
    session_data = get_session_data(session_id)
    if not session_data:
        return

    # Generate registration numbers
    registration_numbers = []
    if reg_no:
        # Parse the registration number to get chart and number parts
        chart, number = parse_registration_number(reg_no)
        if chart and number:
            # Format the registration number properly
            formatted_reg_no = zero_pad_reg_no(chart, number)
            registration_numbers = [formatted_reg_no] * len(temp_file_paths)
            
            # Check if the registration number exists in the database
            if not check_registration_number_exists(formatted_reg_no):
                # If not in database, get it from USCO
                try:
                    # Use existing driver if available
                    existing_driver = getattr(_process_images_background, 'driver', None)
                    usco_info = asyncio.run(get_info_from_USCO_using_reg_no(formatted_reg_no, existing_driver=existing_driver, include_canceled=True, max_click_attempts=1))
                    if usco_info:
                        # Process USCO info and create copyright entry
                        print(f"Retrieved USCO info for {formatted_reg_no}: {usco_info}")
                        # Upsert the copyright information into the database
                        upsert_success = upsert_copyrights_table(plaintiff_id, usco_info)
                        if upsert_success:
                            print(f"Successfully upserted copyright info for {formatted_reg_no}")
                        else:
                            print(f"Failed to upsert copyright info for {formatted_reg_no}")
                except Exception as e:
                    print(f"Error retrieving or upserting USCO info for {formatted_reg_no}: {e}")
        else:
            registration_numbers = [reg_no] * len(temp_file_paths)
    else:
        first_md_reg_no = generate_md_registration_number(plaintiff_id)
        plaintiff_str = f"{plaintiff_id:04d}"
        base_num = int(first_md_reg_no[-4:])

        for i in range(len(temp_file_paths)):
            md_reg_no = f"MD{plaintiff_str}{(base_num + i):04d}"
            registration_numbers.append(md_reg_no)

    # Create temp directories for processing
    temp_upload_dir = tempfile.mkdtemp(prefix="upload_")
    temp_output_dir = tempfile.mkdtemp(prefix="output_")

    try:
        processed = 0
        successful = 0
        failed = 0

        # Process each file
        for i, ((temp_file_path, original_filename), file_reg_no) in enumerate(zip(temp_file_paths, registration_numbers)):
            # Update progress log
            print(f"Starting background image processing for {original_filename}...")
            session_data['logs'].append({
                'type': 'log',
                'message': f'Starting processing of {original_filename}'
            })
            set_session_data(session_id, session_data)

            # Process the image using the helper function
            success, file_id, error_msg = _process_single_image(
                temp_file_path, original_filename, file_reg_no, plaintiff_id, method, type_,
                certificate_status, production, temp_upload_dir, temp_output_dir, session_data
            )

            if success:
                successful += 1
                session_data['logs'].append({
                    'type': 'success',
                    'filename': original_filename,
                    'registration_number': file_reg_no,
                    'file_id': file_id,
                    'message': 'File processed successfully'
                })
            else:
                failed += 1
                session_data['logs'].append({
                    'type': 'error',
                    'filename': original_filename,
                    'message': error_msg or 'Unknown error occurred'
                })

            processed += 1

            # Update progress
            session_data['progress']['current'] = processed
            session_data['progress']['successful'] = successful
            session_data['progress']['failed'] = failed

            # Save updated session data to Redis
            set_session_data(session_id, session_data)

            print(f"Finished background image processing for {original_filename}...")

        # Mark session as complete
        session_data['status'] = 'complete'
        set_session_data(session_id, session_data)

        # Clean up session after some time to prevent memory leaks
        def cleanup_session():
            time.sleep(300)  # Keep session data for 5 minutes
            delete_session_data(session_id)

        cleanup_thread = threading.Thread(target=cleanup_session)
        cleanup_thread.daemon = True
        cleanup_thread.start()

    except Exception as e:
        session_data['status'] = 'error'
        session_data['logs'].append({
            'type': 'error',
            'message': f'Processing failed: {str(e)}'
        })
        set_session_data(session_id, session_data)

        # Clean up session after some time to prevent memory leaks
        def cleanup_session():
            time.sleep(300)  # Keep session data for 5 minutes
            delete_session_data(session_id)

        cleanup_thread = threading.Thread(target=cleanup_session)
        cleanup_thread.daemon = True
        cleanup_thread.start()

    finally:
        # Clean up temp directories
        shutil.rmtree(temp_upload_dir, ignore_errors=True)
        shutil.rmtree(temp_output_dir, ignore_errors=True)


def add_copyright_image_progress_generator(session_id):
    """
    Generator function that yields SSE progress data for a specific session.
    """
    import json

    # Wait for session to be initialized
    while True:
        session_data = get_session_data(session_id)
        if session_data:
            break
        # Send heartbeat to keep connection alive while waiting
        yield f"data: {json.dumps({'type': 'waiting', 'message': f'Initializing session...'})}\n\n"
        time.sleep(0.5)

    # Session is now available, start sending progress updates
    last_log_count = 0
    while True:
        session_data = get_session_data(session_id)
        if not session_data:
            yield f"data: {json.dumps({'type': 'error', 'message': 'Session lost'})}\n\n"
            break

        # Send current progress
        progress = session_data['progress']
        yield f"data: {json.dumps({'type': 'progress', 'current': progress['current'], 'total': progress['total'], 'successful': progress['successful'], 'failed': progress['failed']})}\n\n"

        # Send any new logs since last update
        new_logs = session_data['logs'][last_log_count:]
        for log in new_logs:
            yield f"data: {json.dumps(log)}\n\n"

        last_log_count = len(session_data['logs'])

        # Check if complete or error
        if session_data['status'] == 'complete':
            yield f"data: {json.dumps({'type': 'complete', 'total': progress['total'], 'successful': progress['successful'], 'failed': progress['failed']})}\n\n"
            break
        elif session_data['status'] == 'error':
            yield f"data: {json.dumps({'type': 'error', 'message': 'Processing failed'})}\n\n"
            break

        # Wait before sending next update
        time.sleep(0.5)