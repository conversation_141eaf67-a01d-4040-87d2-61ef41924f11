from sqlalchemy import (
    Column, String, Text, Boolean, TIMESTAMP, Integer, Date
)
from sqlalchemy.dialects.postgresql import UUID as PG_UUID, ARRAY
from backend.extensions import db

class Patent(db.Model):
    __tablename__ = 'patents'

    id = Column(PG_UUID(as_uuid=True), primary_key=True, nullable=False)
    reg_no = Column(Text)
    document_id = Column(Text)
    tro = Column(Boolean)
    inventors = Column(Text)
    assignee = Column(Text)
    applicant = Column(Text)
    patent_title = Column(Text)
    date_published = Column(Date)
    plaintiff_id = Column(Integer)
    patent_type = Column(Text)
    abstract = Column(Text)
    associated_patents = Column(ARRAY(Text))
    design_page_numbers = Column(ARRAY(Integer))
    pdf_source = Column(Text)
    image_source = Column(Text)
    certificate_source = Column(Text)
    loc_code = Column(Text)
    loc_edition = Column(Text)
    uspc_class = Column(Text)
    uspc_subclass = Column(Text)
    create_time = Column(TIMESTAMP, nullable=False)
    update_time = Column(TIMESTAMP, nullable=False)
    folder = Column(Text)
    fig_files = Column(ARRAY(Text))

    def __repr__(self):
        return f'<Patent {self.reg_no}>'