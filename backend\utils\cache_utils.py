import pandas as pd
import threading
from flask import current_app
from .db_utils import get_table_from_GZ

# In-memory cache for data from the GZ database and lightweight lists
_gz_cache = {
    "plaintiff": {
        "data": pd.DataFrame(),
        "lock": threading.Lock()
    },
    "case": {
        "data": pd.DataFrame(),
        "lock": threading.Lock()
    },
    # Lightweight cached distinct values, refreshed on demand
    "copyright_meta": {
        "data": {
            "types": [],   # list[str]
            "methods": []  # list[str]
        },
        "lock": threading.Lock()
    }
}

def get_plaintiff_df():
    """
    Safely gets the plaintiff DataFrame from the cache.
    If the cache is empty, it triggers a refresh.
    """
    with _gz_cache["plaintiff"]["lock"]:
        df = _gz_cache["plaintiff"]["data"]
        if df.empty:
            # Unlock to allow refresh_cache to acquire the lock
            _gz_cache["plaintiff"]["lock"].release()
            try:
                refresh_cache(cache_type='plaintiff')
            finally:
                # Re-acquire the lock before returning data
                _gz_cache["plaintiff"]["lock"].acquire()
            df = _gz_cache["plaintiff"]["data"]
        return df

def get_case_df():
    """
    Safely gets the case DataFrame from the cache.
    If the cache is empty, it triggers a refresh.
    """
    with _gz_cache["case"]["lock"]:
        df = _gz_cache["case"]["data"]
        if df.empty:
            # Unlock to allow refresh_cache to acquire the lock
            _gz_cache["case"]["lock"].release()
            try:
                refresh_cache(cache_type='case')
            finally:
                # Re-acquire the lock before returning data
                _gz_cache["case"]["lock"].acquire()
            df = _gz_cache["case"]["data"]
        return df

def refresh_cache(cache_type='all'):
    """
    Refreshes the specified data cache from the GZ database.

    Args:
        cache_type (str): 'plaintiff', 'case', or 'all'.

    Returns:
        dict: A dictionary with the status and timestamps of the refreshed caches.
    """
    results = {}
    
    def _refresh(name, table):
        current_app.logger.info(f"Refreshing {name} cache...")
        try:
            df = get_table_from_GZ(table)
            
            # Specifically handle data types for the case table to prevent auto-casting to float
            if name == 'case' and 'plaintiff_id' in df.columns:
                # Use pandas' nullable integer type to support potential NaN values
                df['plaintiff_id'] = df['plaintiff_id'].astype('Int64')
                
            with _gz_cache[name]["lock"]:
                _gz_cache[name]["data"] = df
            current_app.logger.info(f"Successfully refreshed {name} cache. Loaded {len(df)} records.")
            results[name] = {"status": "success", "refreshed_at": pd.Timestamp.now().isoformat(), "records": len(df)}
        except Exception as e:
            current_app.logger.error(f"Failed to refresh {name} cache: {e}")
            results[name] = {"status": "failed", "error": str(e)}

    if cache_type in ['plaintiff', 'all']:
        _refresh('plaintiff', 'tb_plaintiff')

    if cache_type in ['case', 'all']:
        _refresh('case', 'tb_case')

    # Note: copyright_meta (types/methods) is populated lazily via compute functions below.
    return results

# --------- Lightweight helpers for distinct values (types/methods) ---------

def set_copyright_meta(types_list=None, methods_list=None):
    """
    Atomically updates cached distinct copyright types/methods lists.
    """
    with _gz_cache["copyright_meta"]["lock"]:
        meta = _gz_cache["copyright_meta"]["data"]
        if types_list is not None:
            # Normalize to strings, non-empty, unique, sorted
            meta["types"] = sorted({str(x) for x in types_list if str(x).strip()})
        if methods_list is not None:
            meta["methods"] = sorted({str(x) for x in methods_list if str(x).strip()})

def get_copyright_meta():
    """
    Returns a shallow copy of cached distinct types and methods.
    Structure: { "types": [...], "methods": [...] }
    """
    with _gz_cache["copyright_meta"]["lock"]:
        meta = _gz_cache["copyright_meta"]["data"]
        # return copies to avoid external mutation
        return {"types": list(meta.get("types", [])), "methods": list(meta.get("methods", []))}

def set_cache(key: str, df: pd.DataFrame):
    """
    Safely sets a DataFrame in the cache. Intended for use by Gunicorn post_fork hook.
    """
    if key in _gz_cache:
        with _gz_cache[key]["lock"]:
            _gz_cache[key]["data"] = df
            current_app.logger.info(f"Cache for '{key}' has been set with {len(df)} records.")
    else:
        current_app.logger.warning(f"Attempted to set unknown cache key: {key}")
