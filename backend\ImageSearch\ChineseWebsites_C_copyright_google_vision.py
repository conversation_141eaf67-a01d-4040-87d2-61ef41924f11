import os
import json
import requests
from google.cloud import vision
import shutil
from PIL import Image, ImageChops
import io
import glob
import time
import torch
from torchvision import models
from torchvision.models import MobileNet_V2_Weights
import langfuse
import traceback
import argparse
from sklearn.metrics.pairwise import cosine_similarity
import re
from langfuse import observe


if os.path.exists(os.path.join(os.getcwd(), "AI", "GoogleCouldServiceAccountKey", "trodata-key.json")):
    os.environ["GOOGLE_APPLICATION_CREDENTIALS"]  = os.path.join(os.getcwd(), "AI", "GoogleCouldServiceAccountKey", "trodata-key.json")
elif os.path.exists(os.path.join(os.getcwd(), "backend", "AI", "GoogleCouldServiceAccountKey", "trodata-key.json")):
    os.environ["GOOGLE_APPLICATION_CREDENTIALS"]  = os.path.join(os.getcwd(), "backend", "AI", "GoogleCouldServiceAccountKey", "trodata-key.json")

# Global configuration
SIMILARITY_THRESHOLD = 0.3
BLACKLISTED_DOMAINS = [
    "qqdip.com",
    "sellerguard.com.cn", 
    "saibeiip.com",
    "maijiazhichi.com",
    "mooting.cn",
    "fangtion.com",
    "daxinfawu.com",
    "10100.com",
    "sellerdefense.cn"
]

def is_blacklisted_url(url):
    """Check if URL is from a blacklisted domain"""
    if not url:
        return False
        
    try:
        domain = re.sub(r'https?://', '', url.lower())
        domain = domain.split('/')[0]
        
        for blacklisted_domain in BLACKLISTED_DOMAINS:
            if blacklisted_domain in domain:
                print(f"Blacklisted domain detected: {domain} matches {blacklisted_domain}")
                return True
                
        return False
    except Exception as e:
        print(f"Error checking domain: {e}")
        return False

def extract_feature_vector(image_path):
    """Extract feature vector from image using MobileNetV2"""
    if not os.path.exists(image_path):
        print(f"Image file does not exist: {image_path}")
        return None
        
    if os.path.getsize(image_path) == 0:
        print(f"Image file is empty: {image_path}")
        return None
    
    try:
        with Image.open(image_path) as img:
            img.verify()
            if img.format not in ['JPEG', 'PNG', 'WEBP', 'BMP', 'GIF']:
                print(f"Unsupported image format in {image_path}: {img.format}")
                return None
    except Exception as e:
        print(f"Invalid image file {image_path}: {e}")
        return None
    
    weights = MobileNet_V2_Weights.DEFAULT
    model = models.mobilenet_v2(weights=weights).features.eval()
    preprocess = weights.transforms()
    
    try:
        image = Image.open(image_path).convert('RGB')
        if image.width <= 10 or image.height <= 10:
            print(f"Image too small to process: {image_path}")
            return None
            
        tensor = preprocess(image).unsqueeze(0)
        with torch.no_grad():
            output_tensor = model(tensor)
            squeezed_tensor = output_tensor.squeeze()
            numpy_features = squeezed_tensor.numpy()
        return numpy_features.flatten()
    except Exception as e:
        print(f"Error extracting features from {image_path}: {e}")
        return None

def compute_similarity(source_vector, target_vector):
    """Compute cosine similarity between two feature vectors"""
    if source_vector is None or target_vector is None:
        return 0
    return cosine_similarity([source_vector], [target_vector])[0][0]

def remove_white_background(image_path):
    """Remove white background from image"""
    try:
        if not os.path.exists(image_path) or os.path.getsize(image_path) == 0:
            print(f"Cannot process empty or non-existent file: {image_path}")
            return False
            
        try:
            original_img = Image.open(image_path)
            if original_img.format not in ['JPEG', 'PNG', 'WEBP']:
                print(f"Invalid image format for {image_path}: {original_img.format}")
                return False
        except Exception as e:
            print(f"Cannot open image file {image_path}: {e}")
            return False
            
        base, ext = os.path.splitext(image_path)
        temp_path = base + ".tempcopy" + ext

        original_img.save(temp_path)
        
        try:
            img = Image.open(temp_path).convert('RGBA')
        except Exception as e:
            print(f"Cannot convert image to RGBA: {e}")
            if os.path.exists(temp_path):
                os.remove(temp_path)
            return False
        
        data = img.getdata()
        new_data = []
        for item in data:
            if item[0] > 245 and item[1] > 245 and item[2] > 245:
                new_data.append((255, 255, 255, 0))
            else:
                new_data.append(item)
        
        img.putdata(new_data)
        
        bg = Image.new(img.mode, img.size, (0, 0, 0, 0))
        diff = ImageChops.difference(img, bg)
        bbox = diff.getbbox()
        
        if bbox:
            img = img.crop(bbox)
        
        try:
            _, ext = os.path.splitext(image_path)
            if ext.lower() in ['.jpg', '.jpeg']:
                img = img.convert('RGB')
                img.save(image_path, 'JPEG', quality=95)
            elif ext.lower() == '.png':
                img.save(image_path, 'PNG')
            else:
                img = img.convert('RGB')
                img.save(image_path, 'JPEG', quality=95)
                
            if os.path.exists(temp_path):
                os.remove(temp_path)
                
            return True
        except Exception as e:
            print(f"Failed to save processed image: {e}")
            if os.path.exists(temp_path):
                shutil.copy2(temp_path, image_path)
                os.remove(temp_path)
            return False
            
    except Exception as e:
        print(f"Error removing white background from {os.path.basename(image_path)}: {e}")
        return False

def download_image(url, folder, filename):
    """Download an image from URL with blacklist checking"""
    if is_blacklisted_url(url):
        print(f"Skipping blacklisted URL: {url}")
        return False
        
    os.makedirs(folder, exist_ok=True)
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        
        with open(os.path.join(folder, filename), 'wb') as f:
            f.write(response.content)
        return True
    except Exception as e:
        print(f"Failed to download {url}: {e}")
        return False

@observe()
def reverse_search_with_google_vision(image_path, output_folder="similar_images", final_folder="final_selection", max_images=10, similarity_threshold=0.3):
    """
    Use Google Vision API to find partial matches and select the best one based on similarity.
    Only saves images that meet the similarity threshold and are not from blacklisted domains.
    
    Args:
        image_path: Path to the input image
        output_folder: Folder to save downloaded images
        final_folder: Folder to save the best matching image
        max_images: Maximum number of images to download and compare
        similarity_threshold: Minimum similarity score to save images
    
    Returns:
        List of final selection details with path and similarity scores
    """
    print("Starting reverse search with Google Vision...")
    print(f"Similarity threshold set to: {similarity_threshold}")
    
    downloaded_image_paths = []
    final_selection_details = []
    similarities = []
    search_results_count = 0
    partial_matches_urls = []
    above_threshold_count = 0
    blacklisted_count = 0

    if not os.path.exists(image_path):
        print(f"Error: Image path does not exist: {image_path}")
        return []

    os.makedirs(output_folder, exist_ok=True)
    os.makedirs(final_folder, exist_ok=True)

    try:
        print(f"Extracting features from source image: {image_path}")
        
        source_features = extract_feature_vector(image_path)
        if source_features is None:
            print("Error: Could not extract features from source image")
            return []

        client = vision.ImageAnnotatorClient()
        
        with open(image_path, 'rb') as image_file:
            content = image_file.read()

        image = vision.Image(content=content)
        response = client.web_detection(image=image)
        annotations = response.web_detection

        if response.error.message:
            print(f"Google Vision API Error: {response.error.message}")
            raise Exception(f"Google Vision API Error: {response.error.message}")

        if annotations.best_guess_labels:
            print("Best guess labels:")
            for label in annotations.best_guess_labels:
                print(f"   - {label.label}")

        if annotations.pages_with_matching_images:
            print("Processing pages with matching images...")
            for i, page in enumerate(annotations.pages_with_matching_images):
                print(f"   Page {i+1}: {page.url}")
                
                if page.partial_matching_images:
                    print(f"      Found {len(page.partial_matching_images)} partial matches")
                    for j, image in enumerate(page.partial_matching_images):
                        if len(partial_matches_urls) >= max_images:
                            break
                        if not is_blacklisted_url(image.url):
                            partial_matches_urls.append(image.url)
                        else:
                            blacklisted_count += 1
                
                if len(partial_matches_urls) < max_images and page.full_matching_images:
                    print(f"      Found {len(page.full_matching_images)} full matches")
                    for j, image in enumerate(page.full_matching_images):
                        if len(partial_matches_urls) >= max_images:
                            break
                        if not is_blacklisted_url(image.url):
                            partial_matches_urls.append(image.url)
                        else:
                            blacklisted_count += 1
                
                if len(partial_matches_urls) >= max_images:
                    break

        if not partial_matches_urls and annotations.visually_similar_images:
            print("No partial/full matches found. Checking visually similar images...")
            for i, image in enumerate(annotations.visually_similar_images[:max_images]):
                if not is_blacklisted_url(image.url):
                    partial_matches_urls.append(image.url)
                else:
                    blacklisted_count += 1

        search_results_count = len(partial_matches_urls)
        print(f"Total valid URLs found: {search_results_count}")
        print(f"Blacklisted URLs skipped: {blacklisted_count}")

        if not partial_matches_urls:
            print("No valid matching or similar images found.")
            return []

        print(f"Starting download and similarity analysis of {len(partial_matches_urls)} images...")
        
        for idx, img_url in enumerate(partial_matches_urls):
            temp_image_path = None
            
            try:
                print(f"   Processing image {idx+1}/{len(partial_matches_urls)}: {img_url}")
                
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                }
                response = requests.get(img_url, headers=headers, timeout=10)
                response.raise_for_status()

                img_data = response.content
                img = Image.open(io.BytesIO(img_data))

                try:
                    url_part = img_url.split('/')[-1].split('?')[0]
                    sanitized_url_part = "".join(c if c.isalnum() or c in "._-" else "_" for c in url_part[:30])
                except:
                    sanitized_url_part = "image"
                
                filename = f"google_vision_match_{idx+1}_{sanitized_url_part}.jpg"
                temp_image_path = os.path.join(output_folder, f"temp_{filename}")
                
                if img.mode in ('RGBA', 'P', 'LA'):
                    img = img.convert('RGB')
                
                img.save(temp_image_path, 'JPEG', quality=95)
                
                try:
                    features = extract_feature_vector(temp_image_path)
                    if features is not None:
                        similarity = compute_similarity(source_features, features)
                        print(f"      Similarity score: {similarity:.4f} (threshold: {similarity_threshold})")
                        
                        if similarity >= similarity_threshold:
                            print(f"      Image meets threshold - keeping image")
                            above_threshold_count += 1
                            
                            final_save_path = os.path.join(output_folder, filename)
                            shutil.move(temp_image_path, final_save_path)
                            
                            try:
                                remove_white_background(final_save_path)
                            except Exception as e:
                                print(f"      White background removal failed: {e}")
                            
                            downloaded_image_paths.append(final_save_path)
                            similarities.append((final_save_path, similarity))
                        else:
                            print(f"      Image below threshold - discarding")
                            if os.path.exists(temp_image_path):
                                os.remove(temp_image_path)
                    else:
                        print(f"      Could not extract features - discarding image")
                        if os.path.exists(temp_image_path):
                            os.remove(temp_image_path)
                except Exception as e:
                    print(f"      Error calculating similarity: {e}")
                    if os.path.exists(temp_image_path):
                        os.remove(temp_image_path)

            except requests.exceptions.RequestException as e:
                print(f"      HTTP Error downloading {img_url}: {e}")
            except IOError as e:
                print(f"      Image processing error for {img_url}: {e}")
            except Exception as e:
                print(f"      Unexpected error for {img_url}: {e}")
            finally:
                if temp_image_path and os.path.exists(temp_image_path):
                    try:
                        os.remove(temp_image_path)
                    except:
                        pass
        
        print(f"Download and filtering phase completed.")
        print(f"   Total images processed: {len(partial_matches_urls)}")
        print(f"   Images above threshold: {above_threshold_count}")
        print(f"   Images saved: {len(downloaded_image_paths)}")

        if similarities:
            print(f"Selecting best match from {len(similarities)} qualifying images...")
            
            similarities.sort(key=lambda x: x[1], reverse=True)
            best_match_path, best_similarity = similarities[0]
            
            img_filename = f"best_match_{os.path.basename(best_match_path)}"
            dest_path = os.path.join(final_folder, img_filename)
            shutil.copy2(best_match_path, dest_path)
            
            original_url = "unknown"
            try:
                best_match_index = downloaded_image_paths.index(best_match_path)
                if best_match_index < len(partial_matches_urls):
                    original_url = partial_matches_urls[best_match_index]
            except:
                pass
            
            final_selection_details.append({
                "path": dest_path, 
                "similarity": float(best_similarity),
                "original_url": original_url
            })
            
            print(f"Best match selected: {img_filename} (similarity: {best_similarity:.4f})")
        else:
            print(f"No images met the similarity threshold of {similarity_threshold}")

    except Exception as e:
        print(f"Main execution error: {e}")
        error_trace = traceback.format_exc()
        print(f"Full traceback: {error_trace}")
        
        try:
            langfuse.get_client().update_current_span(
                metadata={
                    "Status": "Error",
                    "Error": str(e)
                }
            )
        except:
            pass
    
    finally:
        print(f"Function execution completed")
        print(f"   Total valid URLs processed: {search_results_count}")
        print(f"   Blacklisted URLs skipped: {blacklisted_count}")
        print(f"   Images above threshold: {above_threshold_count}")
        print(f"   Final selections: {len(final_selection_details)}")
        
        try:
            langfuse.get_client().update_current_span(
                output={
                    "SearchResultsNb": search_results_count,
                    "BlacklistedUrlsSkipped": blacklisted_count,
                    "ProcessedImages": len(partial_matches_urls) if partial_matches_urls else 0,
                    "ImagesAboveThreshold": above_threshold_count,
                    "SimilarityThreshold": similarity_threshold,
                    "DownloadedImagesWithSimilarity": similarities if similarities else [],
                    "FinalSelectionDetails": final_selection_details
                }
            )
        except:
            pass
    
    return final_selection_details

def detect_web_from_local_file(file_path, save_results=True):
    """Enhanced web detection with blacklist filtering"""
    client = vision.ImageAnnotatorClient()
    
    with open(file_path, 'rb') as image_file:
        content = image_file.read()
    
    image = vision.Image(content=content)
    response = client.web_detection(image=image)
    annotations = response.web_detection
    
    result_data = {
        "input_image_path": file_path,
        "best_guess_labels": [],
        "full_matching_images": [],
        "partial_matching_images": [],
        "visually_similar_images": [],
        "web_entities": [],
        "blacklisted_urls_filtered": 0
    }
    
    if annotations.best_guess_labels:
        for label in annotations.best_guess_labels:
            result_data["best_guess_labels"].append(label.label)
            print(f"Best guess label: {label.label}")
    
    if annotations.pages_with_matching_images:
        for i, page in enumerate(annotations.pages_with_matching_images):
            print(f"Page URL: {page.url}")
            
            if page.full_matching_images:
                for j, image in enumerate(page.full_matching_images):
                    url = image.url
                    if not is_blacklisted_url(url):
                        result_data["full_matching_images"].append(url)
                        download_image(url, "full_matches", f"full_match_{i}_{j}.jpg")
                    else:
                        result_data["blacklisted_urls_filtered"] += 1
            
            if page.partial_matching_images:
                for j, image in enumerate(page.partial_matching_images):
                    url = image.url
                    if not is_blacklisted_url(url):
                        result_data["partial_matching_images"].append(url)
                        download_image(url, "partial_matches", f"partial_match_{i}_{j}.jpg")
                    else:
                        result_data["blacklisted_urls_filtered"] += 1
    
    if annotations.visually_similar_images:
        for i, image in enumerate(annotations.visually_similar_images[:10]):
            url = image.url
            if not is_blacklisted_url(url):
                result_data["visually_similar_images"].append(url)
                download_image(url, "visually_similar", f"similar_{i}.jpg")
            else:
                result_data["blacklisted_urls_filtered"] += 1
    
    if annotations.web_entities:
        for entity in annotations.web_entities:
            result_data["web_entities"].append({
                "score": entity.score,
                "description": entity.description
            })
    
    if response.error.message:
        raise Exception(f"API Error: {response.error.message}")
    
    if save_results:
        with open("results.json", "w") as f:
            json.dump(result_data, f, indent=2)
        print("Results saved to results.json")
        print(f"Blacklisted URLs filtered: {result_data['blacklisted_urls_filtered']}")
    
    return result_data

def process_images_in_folder(input_folder, max_images=10, threshold=0.3):
    """Process all images in a folder using Google Vision API with similarity threshold and blacklist filtering"""
    global SIMILARITY_THRESHOLD
    SIMILARITY_THRESHOLD = threshold

    print(f"Starting batch processing of folder: {input_folder}")
    print(f"   Max images per source: {max_images}")
    print(f"   Similarity threshold: {threshold}")

    output_folder = os.path.join(input_folder, "similar_images")
    final_folder = os.path.join(input_folder, "final_selection")
    os.makedirs(output_folder, exist_ok=True)
    os.makedirs(final_folder, exist_ok=True)

    all_run_json = os.path.join(input_folder, "all_image_urls.json")
    all_run_data = {}

    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.webp', '*.bmp', '*.tiff']
    image_files = []
    for ext in image_extensions:
        image_files.extend(glob.glob(os.path.join(input_folder, ext)))
        image_files.extend(glob.glob(os.path.join(input_folder, ext.upper())))

    if not image_files:
        print(f"No images found in {input_folder}")
        return

    print(f"Found {len(image_files)} images. Processing...")

    successful_processes = 0
    failed_processes = 0
    total_blacklisted = 0
    
    for i, image_path in enumerate(image_files):
        print(f"  [{i+1}/{len(image_files)}] Processing: {os.path.basename(image_path)}")
        
        try:
            source_name = os.path.basename(image_path)
            all_run_data[source_name] = {
                "source_path": image_path,
                "processing_status": "processing",
                "similar_images": {},
                "final_selection": {},
                "statistics": {
                    "threshold_used": threshold,
                    "max_images_requested": max_images,
                    "blacklisted_urls_skipped": 0
                }
            }
            
            final_selection = reverse_search_with_google_vision(
                image_path, 
                output_folder=output_folder, 
                final_folder=final_folder, 
                max_images=max_images,
                similarity_threshold=threshold
            )
            
            all_run_data[source_name]["processing_status"] = "completed"
            all_run_data[source_name]["final_selection"] = final_selection
            
            if final_selection:
                all_run_data[source_name]["statistics"]["images_saved"] = len(final_selection)
                all_run_data[source_name]["statistics"]["best_similarity"] = max([item["similarity"] for item in final_selection])
            else:
                all_run_data[source_name]["statistics"]["images_saved"] = 0
                all_run_data[source_name]["statistics"]["best_similarity"] = 0.0
            
            successful_processes += 1
            print(f"    Completed processing {source_name}")
            
            if final_selection:
                print(f"       Found {len(final_selection)} qualifying images")
                for item in final_selection:
                    print(f"       - Similarity: {item['similarity']:.4f}")
            else:
                print(f"       No images met the threshold of {threshold}")
                
        except Exception as e:
            print(f"    Error processing {os.path.basename(image_path)}: {e}")
            all_run_data[source_name]["processing_status"] = "failed"
            all_run_data[source_name]["error"] = str(e)
            failed_processes += 1
        
        try:
            with open(all_run_json, 'w') as f:
                json.dump(all_run_data, f, indent=4)
        except Exception as e:
            print(f"    Failed to save results: {e}")
        
        if i < len(image_files) - 1:
            time.sleep(5)

    print(f"Batch processing complete!")
    print(f"   Total images processed: {len(image_files)}")
    print(f"   Successful: {successful_processes}")
    print(f"   Failed: {failed_processes}")
    print(f"   Results saved to: {all_run_json}")

    return all_run_data

def save_image_data_to_json(image_data, json_file):
    """Save image data to a JSON file"""
    try:
        if os.path.exists(json_file):
            with open(json_file, 'r') as f:
                existing_data = json.load(f)
        else:
            existing_data = {}
        
        existing_data.update(image_data)
        
        with open(json_file, 'w') as f:
            json.dump(existing_data, f, indent=4)
            
        print(f"Updated image data in {json_file}")
    except Exception as e:
        print(f"Error saving image data to JSON: {e}")

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Google Vision Reverse Image Search')
    parser.add_argument('--input_folder', type=str, required=True, help='Path to input folder containing images')
    parser.add_argument('--max_images', type=int, default=10, help='Maximum images per source (default: 10)')
    parser.add_argument('--threshold', type=float, default=0.3, help='Similarity threshold (default: 0.3)')
    
    args = parser.parse_args()
    
    if os.path.exists(args.input_folder):
        print(f"Starting batch processing...")
        print(f"   Input folder: {args.input_folder}")
        print(f"   Max images per source: {args.max_images}")
        print(f"   Similarity threshold: {args.threshold}")
        
        try:
            results = process_images_in_folder(
                input_folder=args.input_folder,
                max_images=args.max_images,
                threshold=args.threshold
            )
            
            if results:
                print(f"Batch processing completed successfully!")
                
                total_images = len(results)
                successful_images = sum(1 for data in results.values() if data.get("processing_status") == "completed")
                total_saved_images = sum(data.get("statistics", {}).get("images_saved", 0) for data in results.values())
                
                print(f"Final Statistics:")
                print(f"   - Source images processed: {total_images}")
                print(f"   - Successfully processed: {successful_images}")
                print(f"   - Total similar images saved: {total_saved_images}")
                print(f"   - Average images per source: {total_saved_images/successful_images if successful_images > 0 else 0:.2f}")
                
        except Exception as e:
            print(f"Batch processing failed: {e}")
            print(f"Traceback: {traceback.format_exc()}")
    else:
        print(f"Input folder not found: {args.input_folder}")
        print("Please check the path and try again.")