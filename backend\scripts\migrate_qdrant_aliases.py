import os
import json
import uuid
import logging
import re
from dotenv import load_dotenv
from qdrant_client import QdrantClient
from qdrant_client import models
from qdrant_client.http.exceptions import UnexpectedResponse

# --- Configuration ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Load environment variables from .env file
load_dotenv()

QDRANT_URL = os.environ.get("QDRANT_URL")
QDRANT_API_KEY = os.environ.get("QDRANT_API_KEY")
CONFIG_PATH = "models/config.json"
OLD_FORMAT_REGEX = re.compile(r"^(copyright|trademark|patent)_([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})$")

# --- Helper Function ---
def generate_uuid_from_model_id(model_id_str: str) -> uuid.UUID:
    """Generates a UUID5 based on the model ID string."""
    return uuid.uuid5(uuid.NAMESPACE_DNS, model_id_str)

# --- Main Logic ---
def main():
    """Finds old-format Qdrant collections and creates new aliases."""
    logging.info("Starting Qdrant alias migration script.")

    if not QDRANT_URL:
        logging.error("QDRANT_URL environment variable not set.")
        return
    # API Key is optional for some setups
    # if not QDRANT_API_KEY:
    #     logging.warning("QDRANT_API_KEY environment variable not set.")

    # --- Load Model Configuration ---
    uuid_to_model_name = {}
    try:
        with open(CONFIG_PATH, 'r') as f:
            model_config = json.load(f)
        for model_details in model_config: # Iterate directly over the list
            model_id = model_details.get("model_id")
            model_name = model_details.get("model_name")
            if model_id and model_name:
                generated_uuid = str(generate_uuid_from_model_id(model_id))
                uuid_to_model_name[generated_uuid] = model_name
                logging.debug(f"Mapped UUID {generated_uuid} to model '{model_name}'")
            else:
                 logging.warning(f"Skipping entry in config.json due to missing 'model_id' or 'model_name': {model_details}")

        if not uuid_to_model_name:
            logging.error(f"No valid model mappings found in {CONFIG_PATH}. Cannot proceed.")
            return
        logging.info(f"Loaded {len(uuid_to_model_name)} model UUID mappings from {CONFIG_PATH}.")

    except FileNotFoundError:
        logging.error(f"Configuration file not found: {CONFIG_PATH}")
        return
    except json.JSONDecodeError:
        logging.error(f"Error decoding JSON from {CONFIG_PATH}")
        return
    except Exception as e:
        logging.error(f"An unexpected error occurred while loading config: {e}")
        return

    # --- Initialize Qdrant Client ---
    try:
        client = QdrantClient(url=QDRANT_URL, api_key=QDRANT_API_KEY, timeout=60)
        # Basic check to see if connection is possible
        client.get_collections()
        logging.info(f"Successfully connected to Qdrant at {QDRANT_URL}.")
    except Exception as e:
        logging.error(f"Failed to connect to Qdrant at {QDRANT_URL}: {e}")
        return

    # --- Process Collections ---
    alias_operations = []
    try:
        collections_response = client.get_collections()
        all_collection_names = [col.name for col in collections_response.collections]
        logging.info(f"Found {len(all_collection_names)} collections in Qdrant.")

        for old_name in all_collection_names:
            match = OLD_FORMAT_REGEX.match(old_name)
            if match:
                ip_category = match.group(1)
                uuid_string = match.group(2)
                logging.debug(f"Collection '{old_name}' matches old format. UUID: {uuid_string}, Category: {ip_category}")

                model_name = uuid_to_model_name.get(uuid_string)
                if model_name:
                    # Generate new alias name according to the new format
                    new_alias_name = f"workbench_{ip_category}_{model_name}".replace(" ", "_").lower()
                    logging.info(f"Planning to alias '{old_name}' to '{new_alias_name}'")

                    # Add operation to create the new alias
                    alias_operations.append(
                        models.CreateAliasOperation(
                            create_alias=models.CreateAlias(collection_name=old_name, alias_name=new_alias_name)
                        )
                    )

                    # Optional: Add operation to delete the old name if it exists as an alias
                    # This prevents conflicts if the collection name itself was somehow aliased.
                    # Note: Deleting an alias that doesn't exist is usually safe.
                    alias_operations.append(
                         models.DeleteAliasOperation(
                             delete_alias=models.DeleteAlias(alias_name=old_name)
                         )
                    )
                else:
                    logging.warning(f"Collection '{old_name}' uses old format but UUID '{uuid_string}' not found in {CONFIG_PATH}. Skipping migration for this collection.")
            else:
                logging.debug(f"Collection '{old_name}' does not match old format. Skipping.")

    except UnexpectedResponse as e:
        logging.error(f"An error occurred while fetching collections from Qdrant: {e}")
        return
    except Exception as e:
        logging.error(f"An unexpected error occurred during collection processing: {e}")
        return

    # --- Apply Alias Changes ---
    if alias_operations:
        logging.info(f"Applying {len(alias_operations)} alias operations...")
        try:
            # Pass the list of operations directly as per documentation examples
            client.update_collection_aliases(change_aliases_operations=alias_operations, timeout=120) # Increased timeout for potentially long operations
            logging.info("Successfully applied alias operations.")
        except UnexpectedResponse as e:
            logging.error(f"Failed to apply alias operations to Qdrant: {e.status_code} - {e.content}")
            logging.error("Please check Qdrant logs for more details. Some aliases might not have been created.")
        except Exception as e:
            logging.error(f"An unexpected error occurred while applying aliases: {e}")
    else:
        logging.info("No collections found matching the old naming format requiring migration.")

    logging.info("Qdrant alias migration script finished.")

# --- Execution ---
if __name__ == "__main__":
    main()