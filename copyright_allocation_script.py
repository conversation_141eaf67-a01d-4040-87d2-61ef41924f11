#!/usr/bin/env python3
"""
One-off script to allocate copyright plaintiffs to team members based on case date.
Sorts plaintiffs by date_filed and allocates them round-robin to 8 team members.
Saves the allocation to copyright_allocation.xlsx
"""

import pandas as pd
from sqlalchemy import text
import sys
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the backend directory to the path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend import create_app
from backend.utils.cache_utils import get_plaintiff_df, get_case_df

def get_sorted_plaintiffs():
    """Get all plaintiffs sorted by most recent date_filed, similar to get_copyright_assets"""

    # Create Flask app to get database configuration
    app = create_app() # init_for_dev

    with app.app_context():
        # Get plaintiff and case data
        plaintiff_df = get_plaintiff_df()
        case_df = get_case_df()

        if plaintiff_df.empty:
            print("No plaintiff data found.")
            return []

        # Find plaintiffs that have copyright or cn_websites data
        from backend.extensions import db
        engine = db.get_engine(bind='maidalv_db')

        with engine.connect() as conn:
            # Get plaintiffs with copyright data
            copyright_rows = conn.execute(text("SELECT DISTINCT plaintiff_id FROM copyrights WHERE plaintiff_id IS NOT NULL")).fetchall()
            copyright_plaintiff_ids = {row[0] for row in copyright_rows if row[0] is not None}

            # Get plaintiffs with cn_websites data
            if not case_df.empty:
                cn_case_ids = conn.execute(text("SELECT DISTINCT case_id FROM cn_websites WHERE case_id IS NOT NULL AND copyright_reg_nos != '{}'")).fetchall()
                cn_case_ids = {row[0] for row in cn_case_ids}

                # Get plaintiff ids for cn_website cases
                if cn_case_ids:
                    matching_plaintiffs = case_df[case_df['id'].isin(cn_case_ids)]
                    cn_websites_plaintiff_ids = set(matching_plaintiffs['plaintiff_id'].dropna().astype(int))
                    relevant_plaintiff_ids = copyright_plaintiff_ids | cn_websites_plaintiff_ids
                else:
                    relevant_plaintiff_ids = copyright_plaintiff_ids
            else:
                relevant_plaintiff_ids = copyright_plaintiff_ids

        # Filter plaintiff_df to only include relevant plaintiffs
        relevant_plaintiffs = plaintiff_df[plaintiff_df['id'].isin(relevant_plaintiff_ids)].copy()

        if relevant_plaintiffs.empty:
            print("No plaintiffs with copyright data found.")
            return []

        # Build list with sorting criteria
        plaintiffs_with_files = []

        for _, plaintiff_row in relevant_plaintiffs.iterrows():
            plaintiff_id = int(plaintiff_row['id'])

            # Get most recent date_filed for this plaintiff for sorting
            most_recent_date = None
            if not case_df.empty:
                plaintiff_cases = case_df[case_df['plaintiff_id'] == plaintiff_id]
                if not plaintiff_cases.empty and 'date_filed' in plaintiff_cases.columns:
                    dates = pd.to_datetime(plaintiff_cases['date_filed'], errors='coerce')
                    valid_dates = dates.dropna()
                    if not valid_dates.empty:
                        most_recent_date = valid_dates.max()

            plaintiffs_with_files.append({
                "id": plaintiff_id,
                "name": plaintiff_row['plaintiff_name'],
                "most_recent_date_filed": most_recent_date
            })

        # Sort by date_filed (newest first), then by plaintiff_id
        plaintiffs_with_files.sort(key=lambda x: (
            x['most_recent_date_filed'] is None,
            -(x['most_recent_date_filed'].timestamp() if x['most_recent_date_filed'] else 0),
            x['id']
        ))

        return plaintiffs_with_files

def create_allocation_table(plaintiffs):
    """Create allocation table with round-robin assignment to team members"""

    team_members = [
        "Serge",
        "Anas",
        "Hamza",
        "Yutong",
        "Samreen",
        "Uzair",
        "XiChen",
        "QinYue"
    ]

    allocation_data = []

    for i, plaintiff in enumerate(plaintiffs, 1):
        team_member = team_members[(i - 1) % len(team_members)]

        allocation_data.append({
            'plaintiff_number': i,
            'plaintiff_id': plaintiff['id'],
            'plaintiff_name': plaintiff['name'],
            'most_recent_date_filed': plaintiff['most_recent_date_filed'].isoformat() if plaintiff['most_recent_date_filed'] else None,
            'assigned_to': team_member
        })

    return pd.DataFrame(allocation_data)

def main():
    """Main function to run the allocation process"""

    print("Getting sorted plaintiffs...")
    plaintiffs = get_sorted_plaintiffs()

    if not plaintiffs:
        print("No plaintiffs to allocate.")
        return

    print(f"Found {len(plaintiffs)} plaintiffs to allocate.")

    print("Creating allocation table...")
    allocation_df = create_allocation_table(plaintiffs)

    print("Saving to copyright_allocation.xlsx...")
    allocation_df.to_excel('copyright_allocation.xlsx', index=False)

    print("Allocation completed successfully!")
    print(f"Total plaintiffs allocated: {len(allocation_df)}")
    print("File saved as: copyright_allocation.xlsx")

    # Show sample of the allocation
    print("\nSample allocation:")
    print(allocation_df.head(10))

if __name__ == "__main__":
    main()