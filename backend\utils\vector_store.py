# backend/utils/vector_store.py
import os
import logging
from qdrant_client import QdrantClient, models as qdrant_models # Alias models to avoid potential conflicts
from dotenv import load_dotenv
from backend.utils.retry_utils import execute_with_retry, QDRANT_RETRYABLE_EXCEPTIONS

# Load environment variables from .env file
load_dotenv()

# Configure logging
logger = logging.getLogger(__name__)

# --- Qdrant Configuration ---
QDRANT_URL = os.environ.get("QDRANT_URL")
QDRANT_API_KEY = os.environ.get("QDRANT_API_KEY")

# Global Qdrant client instance (lazy initialized)
qdrant_client = None

def get_qdrant_client():
    """
    Initializes and returns a Qdrant client instance.
    Reads configuration from environment variables.
    Uses a global instance to avoid reconnecting repeatedly.
    """
    global qdrant_client
    if qdrant_client:
        try:
            # Perform a lightweight health check.
            # cluster_info() is a relatively lightweight call.
            # If this fails, it suggests the client is no longer healthy.
            # Use get_collections() as a health check for compatibility
            qdrant_client.get_collections() # This call will raise an exception on failure
            logger.debug("Qdrant client health check passed using get_collections().")
            return qdrant_client
        except Exception as e:
            logger.warning(f"Qdrant client health check (using get_collections) failed: {e}. Re-initializing client.", exc_info=True)
            qdrant_client = None # Force re-initialization

    if not QDRANT_URL:
        logger.error("QDRANT_URL environment variable is not set. Cannot initialize Qdrant client.")
        # Depending on requirements, could raise an error or return None
        # raise ValueError("QDRANT_URL environment variable is not set.")
        return None

    try:
        logger.info(f"Initializing Qdrant client with URL: {QDRANT_URL}")
        qdrant_client = QdrantClient(
            url=QDRANT_URL,
            api_key=QDRANT_API_KEY, # api_key can be None if not required
            timeout=60 # Increase timeout for potentially long operations
        )
        # Perform a quick check to see if the client can connect
        # qdrant_client.list_collections() # Example check - might be too slow/verbose
        logger.info("Qdrant client initialized successfully.")
        return qdrant_client
    except Exception as e:
        logger.error(f"Failed to initialize Qdrant client: {e}", exc_info=True)
        qdrant_client = None # Ensure client is None on failure
        # Depending on requirements, could raise an error or return None
        # raise ConnectionError(f"Failed to connect to Qdrant at {QDRANT_URL}") from e
        return None

# --- Example Usage (Placeholder Functions) ---

def ensure_collection_exists(client: QdrantClient, collection_name: str, vector_size: int, distance: qdrant_models.Distance = qdrant_models.Distance.COSINE):
    """
    Checks if a Qdrant collection exists. If it exists, verifies the vector size.
    If the size mismatches, deletes and recreates the collection.
    If it doesn't exist, creates it. Uses retry logic for robustness.
    """
    if not client:
        logger.error("Qdrant client not available.")
        return False

    needs_creation = False
    try:
        # 1. Check if collection exists using the dedicated method with retry
        exists = execute_with_retry(
            lambda: client.collection_exists(collection_name=collection_name),
            QDRANT_RETRYABLE_EXCEPTIONS,
            operation_name=f"check existence of collection '{collection_name}'"
        )

        if exists:
            logger.debug(f"Collection '{collection_name}' found. Verifying parameters...")
            # 2. If exists, get details to verify vector size
            collection_info = execute_with_retry(
                lambda: client.get_collection(collection_name=collection_name),
                QDRANT_RETRYABLE_EXCEPTIONS,
                operation_name=f"get details for collection '{collection_name}'"
            )

            # Safely access nested attributes for vector size
            actual_vector_size = None
            # Correctly and safely access nested attributes: collection_info.config.params.vectors.size
            if (collection_info and
                hasattr(collection_info, 'config') and collection_info.config and
                hasattr(collection_info.config, 'params') and collection_info.config.params and
                hasattr(collection_info.config.params, 'vectors')): # Check if vectors attribute exists

                vectors_config = collection_info.config.params.vectors

                # Check if it's a single VectorParams object (default vector)
                if isinstance(vectors_config, qdrant_models.VectorParams):
                    actual_vector_size = vectors_config.size
                # Check if it's a dictionary (named vectors) and contains the default '' key
                elif isinstance(vectors_config, dict) and '' in vectors_config and isinstance(vectors_config[''], qdrant_models.VectorParams):
                    actual_vector_size = vectors_config[''].size
                # Add checks for specific named vectors if needed, e.g.:
                # elif isinstance(vectors_config, dict) and 'my_vector_name' in vectors_config:
                #     actual_vector_size = vectors_config['my_vector_name'].size
                else:
                     logger.warning(f"Could not determine vector size from collection info structure for '{collection_name}'. Vectors config: {vectors_config}")

            # --- Decision Logic ---
            if actual_vector_size is not None: # Only proceed if we successfully found the actual size
                if actual_vector_size == vector_size:
                    logger.debug(f"Collection '{collection_name}' exists with correct vector size ({vector_size}).")
                    return True # Size matches, we are done.
                else:
                    # Size mismatch confirmed, proceed with deletion.
                    logger.warning(f"Collection '{collection_name}' exists but has WRONG vector size (Expected: {vector_size}, Actual: {actual_vector_size}). Deleting alias (if exists) and collection...")
                    try:
                        # Check if an alias exists with this name and delete it
                        global_aliases = execute_with_retry(
                            lambda: client.get_aliases(),
                            QDRANT_RETRYABLE_EXCEPTIONS,
                            operation_name="list global aliases (pre-delete check)"
                        )
                        alias_exists = any(alias.alias_name == collection_name for alias in global_aliases.aliases)
                        
                        if alias_exists:
                            alias = next((alias for alias in global_aliases.aliases if alias.alias_name == collection_name), None)
                            logger.info(f"Alias '{collection_name}' found. Deleting alias and collection...")
                            execute_with_retry(
                                 lambda: client.delete_collection(collection_name=alias.collection_name, timeout=60),
                                 QDRANT_RETRYABLE_EXCEPTIONS,
                                 operation_name=f"delete alias '{collection_name}'"
                            )
                            logger.info(f"Alias '{collection_name}' and collection '{alias.collection_name}' deleted.")
                        else:
                            logger.debug(f"No alias found with name '{collection_name}'. Proceeding to delete collection.")
                            # Delete the collection due to mismatch
                            execute_with_retry(
                                lambda: client.delete_collection(collection_name=collection_name, timeout=60), # Increased timeout for delete
                                QDRANT_RETRYABLE_EXCEPTIONS,
                                operation_name=f"delete mismatched collection '{collection_name}'"
                            )

                    except Exception as alias_err:
                         # Log error but proceed with collection deletion attempt, as alias might not be the issue
                         logger.error(f"Error checking/deleting alias '{collection_name}': {alias_err}. Attempting collection deletion anyway.")
                    
                    # Updated log message to be more specific about the deletion reason
                    logger.info(f"Mismatched collection '{collection_name}' deleted due to incorrect vector size (Expected: {vector_size}, Actual: {actual_vector_size}).")
                    needs_creation = True # Mark for recreation
            else:
                # Could not determine the actual size from the collection info.
                # DO NOT DELETE. Log an error and potentially abort or handle as needed.
                logger.error(f"Could not verify vector size for existing collection '{collection_name}'. Aborting modification to prevent accidental deletion.")
                return False # Abort the process for this collection

        else: # Collection does not exist (This 'else' corresponds to 'if exists:')
            logger.info(f"Collection '{collection_name}' does not exist. Attempting creation...")
            needs_creation = True

    except Exception as check_err: # This 'except' corresponds to the 'try' block starting at line 70
        # Catch errors during existence check, details fetch, or deletion
        logger.error(f"Error during check/delete for collection '{collection_name}': {check_err}", exc_info=True)
        return False # Abort if we can't reliably check or delete

    # 4. Create the collection if needed
    if needs_creation:
        try:
            created = execute_with_retry(
                lambda: client.create_collection(
                    collection_name=collection_name,
                    vectors_config=qdrant_models.VectorParams(size=vector_size, distance=distance)
                ),
                QDRANT_RETRYABLE_EXCEPTIONS,
                operation_name=f"create collection '{collection_name}'"
            )
            if created:
                logger.info(f"Collection '{collection_name}' created successfully.")
                return True
            else:
                # Should not happen if execute_with_retry works correctly, but handle defensively
                logger.error(f"Failed to create collection '{collection_name}' after retries (returned {created}).")
                return False
        except Exception as create_e:
            logger.error(f"Failed to create collection '{collection_name}' due to exception: {create_e}", exc_info=True)
            return False

    # Should technically not be reached if logic is correct, but return False as a fallback
    return False

def store_vector(client: QdrantClient, collection_name: str, point_id: str, vector: list, payload: dict = None):
    """
    Stores or updates a single vector point in a Qdrant collection.
    Uses point_id derived from image_id/model_id combination or similar unique identifier.
    """
    if not client:
        logger.error("Qdrant client not available.")
        return False

    try:
        upsert_result = client.upsert(
            collection_name=collection_name,
            points=[
                qdrant_models.PointStruct(
                    id=point_id,
                    vector={"siglip_vector": vector},
                    payload=payload or {}
                )
            ],
            wait=True  # Wait for operation to complete for consistency
        )

        if upsert_result and upsert_result.status == qdrant_models.UpdateStatus.COMPLETED:
            logger.info(f"Upserted point ID {point_id} into collection '{collection_name}' successfully.")
            return True
        else:
            status = upsert_result.status if upsert_result else "N/A (failed)"
            logger.error(f"Failed to upsert point ID {point_id} into collection '{collection_name}'. Status: {status}")
            return False

    except Exception as e:
        logger.error(f"Exception during upsert of point ID {point_id} into collection '{collection_name}': {e}", exc_info=True)
        return False

def delete_vector(client: QdrantClient, collection_name: str, point_id: str):
    """
    Deletes a single vector point from a Qdrant collection by its point_id.
    """
    if not client:
        logger.error("Qdrant client not available.")
        return False
    
    try:
        delete_result = client.delete(
            collection_name=collection_name,
            points_selector=qdrant_models.PointIdsList(points=[point_id]),
            wait=True
        )

        if delete_result and delete_result.status == qdrant_models.UpdateStatus.COMPLETED:
            logger.debug(f"Deleted point ID {point_id} from collection '{collection_name}' successfully.")
            return True
        else:
            status = delete_result.status if delete_result else "N/A (failed)"
            logger.error(f"Failed to delete point ID {point_id} from collection '{collection_name}'. Status: {status}")
            return False

    except Exception as e:
        logger.error(f"Failed to delete point ID {point_id} from collection '{collection_name}' due to exception: {e}", exc_info=True)
        return False

def get_all_vectors_in_collection(client: QdrantClient, collection_name: str, with_payload: bool = False, with_vectors: bool = False):
    """
    Retrieves all points from a collection, handling pagination.

    Args:
        client: The Qdrant client.
        collection_name: The name of the collection.
        with_payload: Whether to include the payload.
        with_vectors: Whether to include the vectors.

    Returns:
        A list of all points in the collection.
    """
    if not client:
        logger.error("Qdrant client not available.")
        return []
    
    all_points = []
    next_page_offset = None
    
    try:
        while True:
            points, next_page_offset = execute_with_retry(
                lambda: client.scroll(
                    collection_name=collection_name,
                    limit=250,  # Adjust limit as needed
                    offset=next_page_offset,
                    with_payload=with_payload,
                    with_vectors=with_vectors,
                ),
                QDRANT_RETRYABLE_EXCEPTIONS,
                operation_name=f"scroll collection '{collection_name}'"
            )
            
            if points:
                all_points.extend(points)
            
            if next_page_offset is None:
                break # No more pages
                
        logger.info(f"Scrolled and retrieved {len(all_points)} points from '{collection_name}'.")
        return all_points

    except Exception as e:
        logger.error(f"Failed to scroll collection '{collection_name}': {e}", exc_info=True)
        return []

# Add other Qdrant related utility functions here (e.g., search, delete)

if __name__ == '__main__':
    # Example of how to use the client (for testing purposes)
    print("Attempting to initialize Qdrant client...")
    client = get_qdrant_client()
    if client:
        print("Qdrant client obtained.")
        # Example: List collections
        try:
            # List collections using retry logic
            collections_result = execute_with_retry(
                lambda: client.get_collections(),
                QDRANT_RETRYABLE_EXCEPTIONS,
                operation_name="list collections"
            )
            if collections_result:
                print("Existing collections:", collections_result.collections)
                # Example: Ensure a test collection exists
                # ensure_collection_exists(client, "test_collection", vector_size=768)
            else:
                print("Failed to list collections after retries.")
        except Exception as e:
            print(f"Error interacting with Qdrant (outside retry): {e}")
    else:
        print("Failed to obtain Qdrant client. Check environment variables (QDRANT_URL) and connection.")