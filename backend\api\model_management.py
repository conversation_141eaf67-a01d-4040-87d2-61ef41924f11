from flask import Blueprint, jsonify, current_app
from backend.models.registration import sync_models_from_config
from backend.extensions import db  # Import the db instance from extensions module
from backend.database.models import ModelTestsModel # Import the model
import logging

model_management_bp = Blueprint('model_management_bp', __name__)

logger = logging.getLogger(__name__)

@model_management_bp.route('/', methods=['GET'])
def list_models():
    """
    API endpoint to list all active models registered in the database.
    Handles GET requests at the blueprint's root (/api/models).
    """
    logger.info("Received request to list active models.")
    try:
        # Query for active models, ordered by name
        active_models = db.session.query(ModelTestsModel).filter_by(is_active=True).order_by(ModelTestsModel.model_name).all()

        model_list = [
            {
                "model_id": str(model.model_id),
                "model_name": model.model_name,
                "model_type": model.model_type,
                "applicable_ip_category": model.applicable_ip_category,
                "description": model.description,
                "is_active": model.is_active,
                "created_at": model.created_at.isoformat() if model.created_at else None,
                "updated_at": model.updated_at.isoformat() if model.updated_at else None,
            } for model in active_models
        ]

        logger.info(f"Returning {len(model_list)} active models.")
        return jsonify(model_list), 200

    except Exception as e:
        logger.error(f"Error retrieving active models: {e}", exc_info=True)
        return jsonify({"error": "An internal error occurred while retrieving models."}), 500


@model_management_bp.route('/refresh', methods=['POST'])
def refresh_models():
    """
    API endpoint to manually trigger the synchronization of models
    from the configuration file (models/config.json) to the database.
    """
    logger.info("Received request to refresh models from config.")
    try:
        # Use the session from the application context
        sync_models_from_config(db.session)
        logger.info("Manual model synchronization completed successfully.")
        return jsonify({"message": "Model list refreshed successfully from config."}), 200
    except FileNotFoundError:
        logger.error("Model configuration file not found during manual refresh.")
        return jsonify({"error": "Model configuration file not found."}), 500
    except Exception as e:
        logger.error(f"Error during manual model refresh: {e}", exc_info=True)
        # Avoid leaking detailed errors to the client in production
        return jsonify({"error": "An internal error occurred during model refresh."}), 500