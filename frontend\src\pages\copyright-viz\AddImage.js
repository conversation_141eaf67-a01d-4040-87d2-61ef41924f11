import React, { useState, useEffect } from 'react';
import { addCopyrightAssetWithProgress, getCopyrightTypes, getCopyrightMethods } from '../../services/api_copyright_viz';
import ProgressToast from '../../components/ProgressToast';

const fieldStyle = { display: 'flex', flexDirection: 'column', gap: 6, marginBottom: 14 };
const labelStyle = { fontWeight: 600, fontSize: 13, color: '#334155' };
const inputStyle = { padding: '10px 12px', borderRadius: 8, border: '1px solid #cbd5e1', fontSize: 14, outline: 'none' };
const selectStyle = inputStyle;
const buttonPrimary = { background: '#2563eb', color: 'white', border: 'none', padding: '10px 16px', borderRadius: 8, cursor: 'pointer', fontWeight: 600 };
const cardStyle = { maxWidth: 720, margin: '24px auto', background: 'white', border: '1px solid #e2e8f0', borderRadius: 12, boxShadow: '0 6px 20px rgba(2,6,23,0.06)' };
const headerStyle = { padding: '18px 20px', borderBottom: '1px solid #e2e8f0' };
const bodyStyle = { padding: 20, display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 20 };
const fullRow = { gridColumn: '1 / -1' };
const dropZoneStyle = { border: '2px dashed #94a3b8', borderRadius: 12, padding: 26, textAlign: 'center', color: '#475569', background: '#f8fafc' };
const helperStyle = { fontSize: 12, color: '#64748b' };
const errorStyle = { color: '#dc2626', background: '#fef2f2', border: '1px solid #fecaca', padding: '8px 10px', borderRadius: 8, margin: '0 20px 12px' };
const successStyle = { color: '#166534', background: '#ecfdf5', border: '1px solid #bbf7d0', padding: '8px 10px', borderRadius: 8, margin: '0 20px 12px' };

const AddImage = () => {
    const [files, setFiles] = useState([]);
    const [registrationNumber, setRegistrationNumber] = useState('');
    const [plaintiffId, setPlaintiffId] = useState('');
    const [method, setMethod] = useState('');
    const [type, setType] = useState('');
    const [methodOptions, setMethodOptions] = useState([]);
    const [typeOptions, setTypeOptions] = useState([]);
    const [isProduction, setIsProduction] = useState(false);
    const [certificateStatus, setCertificateStatus] = useState('');
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [success, setSuccess] = useState(null);

    // State for progress toast
    const [progressToast, setProgressToast] = useState({
        isVisible: false,
        progress: { current: 0, total: 0, successful: 0, failed: 0 },
        uploadProgress: 0, // Track upload progress separately
        title: '',
        showDetails: false,
        logs: []
    });
    const [currentEventSource, setCurrentEventSource] = useState(null);

  useEffect(() => {
    const loadOptions = async () => {
      try {
        const [typesRes, methodsRes] = await Promise.all([
          getCopyrightTypes(),
          getCopyrightMethods()
        ]);
        setTypeOptions(typesRes.data || []);
        setMethodOptions(methodsRes.data || []);
      } catch (e) {
        console.error('Failed to load options', e);
      }
    };
    loadOptions();
  }, []);

  const handleFileDrop = (e) => {
    e.preventDefault();
    const droppedFiles = Array.from(e.dataTransfer.files).filter(file =>
      file.type.startsWith('image/')
    );
    if (droppedFiles.length > 0) setFiles(droppedFiles);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    console.log('[DEBUG] Form submission started');

    if (files.length === 0) {
      console.log('[DEBUG] Validation failed: No files selected');
      setError('Please select at least one file.');
      return;
    }
    if (!plaintiffId) {
      console.log('[DEBUG] Validation failed: No plaintiff_id');
      setError('plaintiff_id is required.');
      return;
    }

    console.log(`[DEBUG] Starting upload for ${files.length} files`);
    console.log(`[DEBUG] Files:`, files.map(f => ({ name: f.name, size: f.size, type: f.type })));
    console.log(`[DEBUG] Form data:`, {
      registrationNumber,
      plaintiffId,
      method,
      type,
      isProduction,
      certificateStatus
    });

    setLoading(true);
    setError(null);
    setSuccess(null);

    const formData = new FormData();
    // Add all files to FormData
    files.forEach((file, index) => {
      console.log(`[DEBUG] Adding file ${index + 1}: ${file.name} (${file.size} bytes, ${file.type})`);
      formData.append('files', file);
    });

    if (registrationNumber) {
      console.log(`[DEBUG] Adding registration_number: ${registrationNumber}`);
      formData.append('registration_number', registrationNumber);
    }
    console.log(`[DEBUG] Adding plaintiff_id: ${String(plaintiffId)}`);
    formData.append('plaintiff_id', String(plaintiffId));
    if (method) {
      console.log(`[DEBUG] Adding method: ${method}`);
      formData.append('method', method);
    }
    if (type) {
      console.log(`[DEBUG] Adding type: ${type}`);
      formData.append('type', type);
    }
    console.log(`[DEBUG] Adding production: ${isProduction ? 'true' : 'false'}`);
    formData.append('production', isProduction ? 'true' : 'false');
    if (certificateStatus) {
      console.log(`[DEBUG] Adding certificate_status: ${certificateStatus}`);
      formData.append('certificate_status', certificateStatus);
    }

    // Close any existing event source
    if (currentEventSource) {
      currentEventSource.close();
    }

    // Initialize progress toast
    setProgressToast({
      isVisible: true,
      progress: { current: 0, total: files.length, successful: 0, failed: 0 },
      uploadProgress: 0, // Initialize upload progress
      title: 'Uploading Images...',
      showDetails: false,
      logs: []
    });

    const onProgress = (data) => {
      console.log('[DEBUG] onProgress callback called with:', data);

      setProgressToast(prev => {
        const newLogs = [...prev.logs];

        if (data.type === 'upload_progress') {
          console.log(`[DEBUG] Upload progress: ${data.progress}% (${data.loaded}/${data.total} bytes)`);
          // Handle upload progress
          return {
            ...prev,
            uploadProgress: data.progress,
            title: `Uploading Images... ${data.progress}%`
          };
        } else if (data.phase === 'processing') {
          console.log(`[DEBUG] Processing progress:`, data);
          // Handle processing progress (existing logic)
          if (data.type === 'progress') {
            return {
              ...prev,
              progress: {
                current: data.current,
                total: data.total,
                successful: data.successful,
                failed: data.failed
              },
              title: 'Processing Images...'
            };
          } else if (data.type === 'success') {
            console.log(`[DEBUG] Processing success: ${data.filename} - ${data.message}`);
            newLogs.push({
              type: 'success',
              time: new Date().toLocaleTimeString(),
              message: `✓ ${data.filename}: ${data.message}`
            });
            return { ...prev, logs: newLogs };
          } else if (data.type === 'error') {
            console.log(`[DEBUG] Processing error: ${data.filename} - ${data.message}`);
            newLogs.push({
              type: 'error',
              time: new Date().toLocaleTimeString(),
              message: `✗ ${data.filename}: ${data.message}`
            });
            return { ...prev, logs: newLogs };
          } else if (data.type === 'log') {
            console.log(`[DEBUG] Processing log: ${data.message}`);
            newLogs.push({
              type: 'info',
              time: new Date().toLocaleTimeString(),
              message: data.message
            });
            return { ...prev, logs: newLogs };
          }
        }

        return prev;
      });
    };

    const onComplete = (data) => {
      console.log('[DEBUG] onComplete callback called with:', data);

      setProgressToast(prev => ({
        ...prev,
        title: 'Image Addition Complete',
        uploadProgress: 100, // Ensure upload progress shows 100%
        logs: [...prev.logs, {
          type: 'progress',
          time: new Date().toLocaleTimeString(),
          message: `Completed: ${data.successful} successful, ${data.failed} failed`
        }]
      }));

      // Clear form on success
      if (data.successful > 0) {
        console.log(`[DEBUG] Upload successful: ${data.successful} files uploaded, clearing form`);
        setSuccess(`${data.successful} file(s) uploaded successfully`);
        setFiles([]);
        setRegistrationNumber('');
        setPlaintiffId('');
        setMethod('');
        setType('');
        setIsProduction(false);
        setCertificateStatus('');
      } else {
        console.log('[DEBUG] No successful uploads, keeping form data');
      }

      setCurrentEventSource(null);
      setLoading(false);
      console.log('[DEBUG] Upload process completed');
    };

    const onError = (error) => {
      console.error('[DEBUG] onError callback called with:', error);
      console.error('[DEBUG] Error details:', {
        message: error.message,
        stack: error.stack,
        name: error.name
      });

      setProgressToast(prev => ({
        ...prev,
        title: 'Image Addition Failed',
        uploadProgress: 0, // Reset upload progress on error
        logs: [...prev.logs, {
          type: 'error',
          time: new Date().toLocaleTimeString(),
          message: `Error: ${error.message || 'Unknown error occurred'}`
        }]
      }));
      setCurrentEventSource(null);
      setLoading(false);
      setError('Failed to upload images');
      console.log('[DEBUG] Error handling completed');
    };

    const eventSource = addCopyrightAssetWithProgress(formData, onProgress, onComplete, onError);
    setCurrentEventSource(eventSource);
  };

  /**
   * Closes the progress toast
   */
  const closeProgressToast = () => {
    if (currentEventSource) {
      currentEventSource.close();
      setCurrentEventSource(null);
    }
    setProgressToast(prev => ({ ...prev, isVisible: false }));
  };

  /**
   * Toggles the progress toast details
   */
  const toggleProgressDetails = () => {
    setProgressToast(prev => ({ ...prev, showDetails: !prev.showDetails }));
  };

  return (
    <div style={{ padding: 16 }}>
      <div style={cardStyle}>
        <div style={headerStyle}>
          <h1 style={{ margin: 0, fontSize: 20 }}>Add Image</h1>
        </div>
        {error && <div style={errorStyle}>{error}</div>}
        {success && <div style={successStyle}>{success}</div>}
        <form onSubmit={handleSubmit} style={{ paddingBottom: 16 }}>
          <div style={bodyStyle}>
            <div style={{ ...fieldStyle, ...fullRow }}>
              <label htmlFor="file-input" style={labelStyle}>Image files</label>
              <div
                className="drop-zone"
                onDrop={handleFileDrop}
                onDragOver={(e) => e.preventDefault()}
                style={dropZoneStyle}
                onClick={() => document.getElementById('file-input')?.click()}
              >
                {files.length > 0 ? (
                  <div>
                    <p>{files.length} file(s) selected:</p>
                    {files.map((file, index) => (
                      <p key={index} style={{ fontSize: 12, margin: '2px 0' }}>
                        • {file.name}
                      </p>
                    ))}
                  </div>
                ) : (
                  <p>Drop files here or click to select</p>
                )}
              </div>
              <input
                type="file"
                id="file-input"
                style={{ display: 'none' }}
                onChange={(e) => {
                  const selectedFiles = Array.from(e.target.files).filter(file =>
                    file.type.startsWith('image/')
                  );
                  setFiles(selectedFiles);
                }}
                accept="image/*"
                multiple
              />
              <div style={helperStyle}>Supported formats such as webp, jpg, png. Multiple images allowed.</div>
            </div>

            <div style={fieldStyle}>
              <label style={labelStyle}>Registration Number</label>
              <input
                type="text"
                placeholder="Optional; autogenerated MD-... if empty"
                value={registrationNumber}
                onChange={(e) => setRegistrationNumber(e.target.value)}
                style={inputStyle}
              />
            </div>

            <div style={fieldStyle}>
              <label style={labelStyle}>Plaintiff ID</label>
              <input
                type="text"
                placeholder="Must exist in plaintiff cache"
                value={plaintiffId}
                onChange={(e) => setPlaintiffId(e.target.value)}
                style={inputStyle}
                required
              />
              <span style={helperStyle}>Enter an existing plaintiff_id from cache.</span>
            </div>

            <div style={fieldStyle}>
              <label style={labelStyle}>Method (Optional)</label>
              <select value={method} onChange={(e) => setMethod(e.target.value)} style={selectStyle}>
                <option value="" disabled>Select method</option>
                {methodOptions.map((m) => (
                  <option key={m.name} value={m.name}>{m.name}</option>
                ))}
              </select>
            </div>

            <div style={fieldStyle}>
              <label style={labelStyle}>Type (Optional)</label>
              <select value={type} onChange={(e) => setType(e.target.value)} style={selectStyle}>
                <option value="" disabled>Select type</option>
                {typeOptions.map((t) => (
                  <option key={t.name} value={t.name}>{t.name}</option>
                ))}
              </select>
            </div>

            <div style={fieldStyle}>
              <label style={labelStyle}>Certificate Status (Optional)</label>
              <select value={certificateStatus} onChange={(e) => setCertificateStatus(e.target.value)} style={selectStyle}>
                <option value="">None</option>
                <option value="missing">Missing</option>
                <option value="fetched">Fetched</option>
                <option value="failed">Failed</option>
              </select>
            </div>

            <div style={{ ...fieldStyle, alignItems: 'flex-start' }}>
              <label style={labelStyle}>Production</label>
              <label style={{ display: 'flex', gap: 8, alignItems: 'center', fontSize: 14, color: '#334155' }}>
                <input
                  type="checkbox"
                  checked={isProduction}
                  onChange={(e) => setIsProduction(e.target.checked)}
                />
                Approve and generate vector immediately
              </label>
            </div>

            <div style={{ ...fullRow, display: 'flex', justifyContent: 'flex-end', marginTop: 4 }}>
              <button type="submit" style={buttonPrimary} disabled={loading}>
                {loading ? 'Submitting...' : 'Submit'}
              </button>
            </div>
          </div>
        </form>
      </div>

      {/* Progress Toast */}
      <ProgressToast
        isVisible={progressToast.isVisible}
        progress={progressToast.progress}
        uploadProgress={progressToast.uploadProgress}
        title={progressToast.title}
        showDetails={progressToast.showDetails}
        logs={progressToast.logs}
        onClose={closeProgressToast}
        onToggleDetails={toggleProgressDetails}
      />
    </div>
  );
};

export default AddImage;