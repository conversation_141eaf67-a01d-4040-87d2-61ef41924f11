from backend.startup import load_data_and_warmup_model

# Global variable to hold the pre-loaded cache data
preloaded_cache = {}

def on_starting(server):
    """
    Called in the master process when it starts.
    We pre-load the large, read-only data from the slow GZ database
    and concurrently warm up the Siglip model to optimize startup time.
    """
    global preloaded_cache
    server.log.info("Master process starting. Kicking off concurrent data and model loading...")
    
    try:
        # Use the centralized startup function
        loaded_data = load_data_and_warmup_model(server.log)
        preloaded_cache.update(loaded_data)
        server.log.info("Concurrent data and model loading complete. Master process proceeding.")
    except Exception as e:
        server.log.critical("Startup failed: %s", e, exc_info=True)
        # Re-raise the exception to prevent <PERSON><PERSON> from starting
        raise


def post_fork(server, worker):
    """
    Called in the worker process after it has been forked.
    We populate the worker's cache with the pre-loaded data.
    This requires an app context for logging.
    """
    worker.log.info("Worker process forked (PID: %s). Populating cache from pre-loaded data.", worker.pid)
    try:
        from backend import create_app
        from backend.utils.cache_utils import set_cache

        # Create a temporary app context to make the logger and cache accessible
        app = create_app()
        with app.app_context():
            # Set the cache for this worker using the data from the master process
            set_cache('plaintiff', preloaded_cache.get('plaintiff'))
            set_cache('case', preloaded_cache.get('case'))
            worker.log.info("Cache successfully populated for worker (PID: %s).", worker.pid)

    except Exception as e:
        worker.log.critical(
            "Failed to populate cache in worker (PID: %s): %s",
            worker.pid, e, exc_info=True
        )
