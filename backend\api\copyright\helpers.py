"""
Helper functions and utilities for copyright API
"""
import os
import pandas as pd
import requests
from flask import current_app, g
from sqlalchemy import text
from backend.extensions import db
from backend.AI.shared_models import EMBEDDING_VECTOR_SIZE
from backend.utils.cache_utils import get_case_df
from backend.utils.db_utils import safe_transaction
import re
import datetime

# Constants
COS_BASE_URL = "http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images"
QDRANT_COLLECTION_NAME = "IP_Assets_Optimized"
QDRANT_VECTOR_SIZE = EMBEDDING_VECTOR_SIZE

def get_low_res_path(plaintiff_id, filename, update_time=None):
    """Gets the full URL for a low-res image."""
    if not plaintiff_id or not filename:
        return None
    base_url = f"{COS_BASE_URL}/{plaintiff_id}/low/{filename}"
    if update_time:
        return f"{base_url}?t={update_time}"
    return base_url

def get_high_res_path(plaintiff_id, filename, update_time=None):
    """Gets the full URL for a high-res image."""
    if not plaintiff_id or not filename:
        return None
    base_url = f"{COS_BASE_URL}/{plaintiff_id}/high/{filename}"
    if update_time:
        return f"{base_url}?t={update_time}"
    return base_url

def get_certificate_path(plaintiff_id, filename):
    """Gets the full URL for a certificate image."""
    if not plaintiff_id or not filename:
        return None
    # Assuming certificate has a suffix convention. Adjust if necessary.
    base, ext = os.path.splitext(filename)
    cert_filename = f"{base}_full{ext}"
    return f"{COS_BASE_URL}/{plaintiff_id}/high/{cert_filename}"

def generate_md_registration_number(plaintiff_id: int) -> str:
    """
    Generate a new MD registration number for unknown copyrights.
    Format: MDxxxxyyyy where xxxx is plaintiff_id 0-padded to 4 digits, yyyy is consecutive number.
    """
    engine = db.get_engine(bind='maidalv_db')
    plaintiff_str = f"{plaintiff_id:04d}"
    pattern = f"MD{plaintiff_str}%"

    with engine.connect() as conn:
        # Query for the maximum consecutive number for this plaintiff
        result = conn.execute(
            text("""
                SELECT MAX(CAST(SUBSTRING(registration_number, 7, 4) AS INTEGER))
                FROM copyrights
                WHERE plaintiff_id = :plaintiff_id AND registration_number LIKE :pattern
            """),
            {"plaintiff_id": plaintiff_id, "pattern": pattern}
        ).scalar()

    next_num = (result or 0) + 1
    return f"MD{plaintiff_str}{next_num:04d}"

def sort_by_reg_no(items, reg_no_key='reg_no'):
    """Sort items by registration number, putting MD-prefixed ones last."""
    def sort_key(item):
        reg_no = item.get(reg_no_key, '') or ''
        if reg_no.startswith('MD'):
            return (1, reg_no)  # MD goes last
        return (0, reg_no)  # Others go first
    return sorted(items, key=sort_key)

def download_image_bytes(url: str) -> bytes | None:
    if not url:
        return None
    try:
        resp = requests.get(url, timeout=30)
        if resp.status_code == 200:
            return resp.content
        return None
    except Exception:
        current_app.logger.exception(f"Failed to download image from {url}")
        return None

def sanitize_name(name):
    """Sanitize a name for use in file paths"""
    if not name:
        return ""
    # Replace problematic characters
    import re
    sanitized = re.sub(r'[<>:"/\\|?*]', '_', str(name))
    sanitized = re.sub(r'\s+', '_', sanitized)
    return sanitized.strip('_')


def get_latest_case_for_plaintiff(plaintiff_id):
    # Filters a DataFrame of cases to find the most recent one for a specific plaintiff.
    case_df = get_case_df()
    
    # Filter the DataFrame for the given plaintiff_id
    plaintiff_cases = case_df[case_df['plaintiff_id'] == plaintiff_id].copy()

    # If there are no cases for this plaintiff, return None
    if plaintiff_cases.empty:
        return None

    # Convert 'date_filed' to datetime objects for proper sorting
    plaintiff_cases['date_filed'] = pd.to_datetime(plaintiff_cases['date_filed'])

    # Sort by 'date_filed' in descending order and get the first row (the latest)
    latest_case = plaintiff_cases.sort_values(by='date_filed', ascending=False).iloc[0]

    return latest_case


def zero_pad_reg_no(chart: str, number: str) -> str:
    """Zero pad the registration number based on its chart type.
    Args:
        chart (str): The chart type (e.g., 'MD', 'VA').
        number (str): The number to zero pad.
    Returns:
        str: The zero-padded registration number.
    """
    if chart == 'MD':  # Internally generated number: MD + 4 digit plaintiff id + 4 digit copyright count
        formated_reg_no = chart + number.zfill(8)
    elif len(chart) == 3:  # e.g. VAu123456789
        if len(number) > 9:
            number = number[-9:]  # Ensure we only take the last 9 digits
        if chart[2] == 'U':
            chart = chart[:2] + 'u'
    
        formated_reg_no = chart + number.zfill(9)
    else:  # e.g. VA0123456789
        if len(number) > 10:
            number = number[-10:]  # Ensure we only take the last 10 digits
        formated_reg_no = chart + number.zfill(10)
    
    return formated_reg_no


@safe_transaction(bind='maidalv_db')
def check_registration_number_exists(registration_number: str) -> bool:
    """
    Check if a registration number already exists in the copyrights table.
    Args:
        registration_number (str): The registration number to check.
    Returns:
        bool: True if exists, False otherwise.
    """
    result = g.db_conn.execute(
        text("""
            SELECT COUNT(*)
            FROM copyrights
            WHERE registration_number = :reg_no
        """),
        {"reg_no": registration_number}
    ).scalar()
    return result > 0


def parse_registration_number(reg_no: str) -> tuple:
    """
    Parse a registration number string to extract chart and number parts.
    Args:
        reg_no (str): The registration number string (e.g., "VA123456", "VAu123456789", "MD1234")
    Returns:
        tuple: (chart, number) where chart is the prefix and number is the numeric part
    """
    if not reg_no:
        return None, None
    
    # Match the chart part (letters at the beginning)
    match = re.match(r'^([A-Za-z]+)(\d*)$', reg_no)
    if match:
        chart = match.group(1).upper()
        number = match.group(2)
        return chart, number
    
    return None, None


@safe_transaction(bind='maidalv_db')
def upsert_copyrights_table(plaintiff_id: int, upsert_data: dict) -> bool:
    """Upsert a record into the copyrights table."""
    try:
        # Parse dates
        try:
            clean_date_of_creation = int(upsert_data.get("date_of_creation", None))
        except (ValueError, TypeError):
            clean_date_of_creation = None

        raw_pub_date = upsert_data.get("date_of_publication", None)
        clean_date_of_publication = parse_publication_date(str(raw_pub_date)) if raw_pub_date else None

        # Filter out None and empty values
        filtered_data = {k: v for k, v in upsert_data.items() if v is not None and v != ""}

        insert_value = [
            True,  # tro
            filtered_data['registration_number'],
            filtered_data.get("registration_date", None),
            filtered_data.get("type_of_work", None),
            filtered_data.get("title", None),
            clean_date_of_creation,
            clean_date_of_publication,
            filtered_data.get("copyright_claimant", None),
            filtered_data.get("authorship_on_application", None),
            filtered_data.get("rights_and_permissions", None),
            filtered_data.get("description", None),
            filtered_data.get("nation_of_first_publication", None),
            filtered_data.get("names", None),
            plaintiff_id
        ]

        # Column names for copyrights table
        COPYRIGHT_COLUMNS = [
            'tro', 'registration_number', 'registration_date', 'type_of_work', 'title',
            'date_of_creation', 'date_of_publication', 'copyright_claimant',
            'authorship_on_application', 'rights_and_permissions', 'description',
            'nation_of_first_publication', 'names', 'plaintiff_id'
        ]

        insert_columns_str = ", ".join(COPYRIGHT_COLUMNS)
        placeholders = ", ".join(["%s"] * len(COPYRIGHT_COLUMNS))
        update_set_str = ", ".join([f'"{col}" = EXCLUDED."{col}"' for col in COPYRIGHT_COLUMNS if col != 'registration_number'])
        update_set_str += ", update_time = NOW()"

        upsert_query = f"""
            INSERT INTO copyrights ({insert_columns_str})
            VALUES ({placeholders})
            ON CONFLICT (registration_number) DO UPDATE SET
                {update_set_str}
        """
        
        g.db_conn.execute(text(upsert_query), tuple(insert_value))
        g.db_conn.commit()
        
        return True
    except Exception as e:
        print(f"Error upserting to copyrights table: {e}")
        return False


def parse_publication_date(value):
    if not value:
        return None

    try:
        # Try full date first
        return datetime.strptime(value, "%Y-%m-%d").date()
    except:
        pass

    try:
        # Try year-month
        return datetime.strptime(value, "%Y-%m").date()
    except:
        pass

    try:
        # Try year only
        if len(value) == 4 and value.isdigit():
            return datetime.strptime(value, "%Y").date()
    except:
        pass

    return None  # Fallback if parsing fails
