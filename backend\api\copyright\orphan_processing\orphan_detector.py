"""
Orphan Detector Class

This module contains the OrphanDetector class which handles all orphan detection operations.
"""

from flask import current_app
from backend.extensions import db
from backend.database.copyrights_file_model import CopyrightsFiles
from backend.utils.cache_utils import get_case_df
from qdrant_client import models
from datetime import datetime
import traceback

from .orphan_helpers import (
    find_knn_points, get_plaintiff_lookup, generate_file_paths, extract_court_filenames,
    get_qdrant_points_batch, apply_pagination, find_most_similar_points,
)
from .orphan_types import OrphanData


class OrphanDetector:
    """Class for detecting different types of orphan records."""

    def __init__(self):
        self.plaintiff_lookup = None
        self.case_df = None
        self._init_cache()

    def _init_cache(self):
        """Initialize cached data."""
        try:
            self.plaintiff_lookup = get_plaintiff_lookup()
            self.case_df = get_case_df()
        except Exception as e:
            current_app.logger.error(f"Error initializing cache: {str(e)}")

    def _get_all_qdrant_points_with_vectors(self, force_refresh=False):
        """Get all Qdrant points with vectors using file-based caching."""
        cache_dir = "temp_vectors"
        all_points = self._fetch_qdrant_points_with_vectors()
        return all_points

    def _fetch_qdrant_points_with_vectors(self):
        """Fetch all Qdrant points with vectors from the database."""
        try:
            from backend.utils.vector_store import get_qdrant_client
            from backend.api.copyright.helpers import QDRANT_COLLECTION_NAME

            client = get_qdrant_client()
            if not client:
                current_app.logger.error("Failed to get Qdrant client")
                return []

            copyright_filter = models.Filter(
                must=[models.FieldCondition(key="ip_type", match=models.MatchValue(value="Copyright"))]
            )

            all_points = []
            offset = None

            while True:
                scroll_result = client.scroll(
                    collection_name=QDRANT_COLLECTION_NAME,
                    scroll_filter=copyright_filter,
                    limit=100000,  # Smaller batch size for vectors
                    offset=offset,
                    with_payload=True,
                    with_vectors=True  # Include vectors
                )

                points_batch = scroll_result[0]
                if not points_batch:
                    break

                all_points.extend(points_batch)
                offset = scroll_result[1]
                if offset is None:
                    break

            current_app.logger.info(f"Fetched {len(all_points)} Qdrant points with vectors from database")
            all_points.sort(key=lambda point: point.payload.get('plaintiff_id', ''))
            return all_points

        except Exception as e:
            current_app.logger.error(f"Error fetching Qdrant points with vectors: {str(e)}")
            return []

    def get_court_only_orphans(self, page=1, per_page=50):
        """Get orphan copyrights that exist in court records but not in database."""
        try:
            if self.case_df.empty:
                return [], {'total_count': 0}

            court_filenames = extract_court_filenames(self.case_df)

            if not court_filenames:
                return [], {'total_count': 0}

            # Get filenames that exist in CopyrightsFiles table
            existing_files_query = db.session.query(CopyrightsFiles.filename).filter(
                CopyrightsFiles.filename.in_(court_filenames)
            ).all()
            existing_filenames = {row.filename for row in existing_files_query}

            # Find filenames that exist in court but not in CopyrightsFiles
            missing_filenames = court_filenames - existing_filenames

            if not missing_filenames:
                return [], {'total_count': 0}

            # Convert missing filenames to list for pagination
            missing_filenames_list = list(missing_filenames)

            # Apply pagination
            pagination_result = apply_pagination(missing_filenames_list, page, per_page)
            paginated_filenames = pagination_result.items

            # Format the results
            orphans = []
            for filename in paginated_filenames:
                # Find the case that contains this filename
                case_info = None
                for _, case in self.case_df.iterrows():
                    if (case.get('images') and isinstance(case['images'], dict) and
                        filename.data in case['images'].get('copyrights', {})):
                        case_info = case
                        break

                if case_info is not None:
                    plaintiff_id = case_info.get('plaintiff_id')
                    plaintiff_name = self.plaintiff_lookup.get(plaintiff_id, 'Unknown')

                    # Generate paths
                    high_res_path, low_res_path = generate_file_paths(plaintiff_id, filename.data)

                    # Extract full_filename from the images data
                    full_filename = None
                    if case_info.get('images') and isinstance(case_info['images'], dict):
                        copyright_data = case_info['images'].get('copyrights', {})
                        if filename.data in copyright_data:
                            full_filename_data = copyright_data[filename.data].get('full_filename', [])
                            if full_filename_data:
                                full_filename = full_filename_data[0]

                    orphans.append({
                        'id': filename.data,  # Use filename as ID since there's no UUID
                        'data': {
                            'filename': filename.data,
                            'full_filename': full_filename,
                            'plaintiff_id': plaintiff_id,
                            'plaintiff_name': plaintiff_name,
                            'docket': case_info.get('docket', ''),
                            'case_number': case_info.get('case_number', ''),
                            'high_res_path': high_res_path,
                            'low_res_path': low_res_path,
                            'court_case_id': case_info.get('id', '')
                        }
                    })

            return orphans, {
                'page': page,
                'per_page': per_page,
                'total_count': pagination_result.pagination.total_count,
                'total_pages': pagination_result.pagination.total_pages,
                'has_next': pagination_result.pagination.has_next,
                'has_prev': pagination_result.pagination.has_prev
            }

        except Exception as court_error:
            current_app.logger.error(f"Court records error in court-only: {str(court_error)}")
            return [], {'total_count': 0}

    def get_db_only_orphans(self, page=1, per_page=50):
        """Get orphan copyrights that exist in database but not in court records."""
        try:
            from backend.database.copyright_model import Copyrights

            # Get case DataFrame to extract court filenames
            court_filenames = extract_court_filenames(self.case_df)

            # Get all files from CopyrightsFiles table
            db_files_query = db.session.query(
                CopyrightsFiles.id,
                CopyrightsFiles.filename,
                CopyrightsFiles.registration_number,
                CopyrightsFiles.type,
                CopyrightsFiles.method,
                CopyrightsFiles.create_time,
                CopyrightsFiles.update_time,
                Copyrights.plaintiff_id,
            ).join(
                Copyrights, CopyrightsFiles.registration_number == Copyrights.registration_number
            ).all()

            # Find files that exist in DB but not in court records
            db_filenames = {record.filename for record in db_files_query}
            missing_from_court = db_filenames - court_filenames

            if not missing_from_court:
                return [], {'total_count': 0}

            # Convert missing filenames to list for pagination and get full records
            missing_records = [record for record in db_files_query if record.filename in missing_from_court]

            # Apply pagination
            pagination_result = apply_pagination(missing_records, page, per_page)
            paginated_records = pagination_result.items

            # Format the results
            orphans = []
            for record in paginated_records:
                high_res_path, low_res_path = generate_file_paths(
                    record.data.plaintiff_id, record.data.filename,
                    record.data.update_time.isoformat() if record.data.update_time else None
                )
                plaintiff_name = self.plaintiff_lookup.get(record.data.plaintiff_id, 'Unknown')

                orphans.append({
                    'id': str(record.data.id),
                    'data': {
                        'registration_number': record.data.registration_number,
                        'filename': record.data.filename,
                        'type': record.data.type,
                        'method': record.data.method,
                        'plaintiff_id': record.data.plaintiff_id,
                        'plaintiff_name': plaintiff_name,
                        'high_res_path': high_res_path,
                        'low_res_path': low_res_path,
                        'create_time': record.data.create_time.isoformat() if record.data.create_time else None,
                        'update_time': record.data.update_time.isoformat() if record.data.update_time else None
                    }
                })

            return orphans, {
                'page': page,
                'per_page': per_page,
                'total_count': pagination_result.pagination.total_count,
                'total_pages': pagination_result.pagination.total_pages,
                'has_next': pagination_result.pagination.has_next,
                'has_prev': pagination_result.pagination.has_prev
            }

        except Exception as db_error:
            current_app.logger.error(f"Database error in db-only: {str(db_error)}")
            return [], {'total_count': 0}

    def get_prod_no_qdrant_orphans(self, page=1, per_page=50):
        """Get production files that don't exist in Qdrant."""
        try:
            from backend.database.copyright_model import Copyrights

            # Get all production files from database
            production_records = db.session.query(
                CopyrightsFiles.id,
                CopyrightsFiles.filename,
                CopyrightsFiles.registration_number,
                CopyrightsFiles.type,
                CopyrightsFiles.method,
                CopyrightsFiles.create_time,
                CopyrightsFiles.update_time,
                Copyrights.plaintiff_id,
            ).join(
                Copyrights, CopyrightsFiles.registration_number == Copyrights.registration_number
            ).filter(
                CopyrightsFiles.production == True
            ).all()

            if not production_records:
                return [], {'total_count': 0}

            # Get Qdrant points
            all_qdrant_points = get_qdrant_points_batch()

            if not all_qdrant_points:
                # No items in Qdrant, so all production files are missing
                missing_records = production_records
            else:
                # Extract filenames from Qdrant points
                qdrant_filenames = {point.payload.get('filename') for point in all_qdrant_points if point.payload.get('filename')}

                # Find production files that don't exist in Qdrant
                missing_records = [record for record in production_records if record.filename not in qdrant_filenames]

            if not missing_records:
                return [], {'total_count': 0}

            # Apply pagination
            pagination_result = apply_pagination(missing_records, page, per_page)
            paginated_records = pagination_result.items

            # Format the results
            orphans = []
            for record in paginated_records:
                high_res_path, low_res_path = generate_file_paths(
                    record.data.plaintiff_id, record.data.filename,
                    record.data.update_time.isoformat() if record.data.update_time else None
                )
                plaintiff_name = self.plaintiff_lookup.get(record.data.plaintiff_id, 'Unknown')

                orphans.append({
                    'id': str(record.data.id),
                    'data': {
                        'registration_number': record.data.registration_number,
                        'filename': record.data.filename,
                        'type': record.data.type,
                        'method': record.data.method,
                        'plaintiff_id': record.data.plaintiff_id,
                        'plaintiff_name': plaintiff_name,
                        'high_res_path': high_res_path,
                        'low_res_path': low_res_path,
                        'create_time': record.data.create_time.isoformat() if record.data.create_time else None,
                        'update_time': record.data.update_time.isoformat() if record.data.update_time else None
                    }
                })

            return orphans, {
                'page': page,
                'per_page': per_page,
                'total_count': pagination_result.pagination.total_count,
                'total_pages': pagination_result.pagination.total_pages,
                'has_next': pagination_result.pagination.has_next,
                'has_prev': pagination_result.pagination.has_prev
            }

        except Exception as db_error:
            current_app.logger.error(f"Database error in prod-no-qdrant: {str(db_error)}")
            return [], {'total_count': 0}

    def get_qdrant_only_orphans(self, page=1, per_page=50):
        """Get items that exist in Qdrant but not in CopyrightsFiles table with similarity matrix."""
        try:
            from backend.database.copyright_model import Copyrights

            # Get Qdrant points with vectors
            all_qdrant_points = self._get_all_qdrant_points_with_vectors()

            if not all_qdrant_points:
                return [], {'total_count': 0}

            # Extract filenames from Qdrant points
            qdrant_filenames = {point.payload.get('filename') for point in all_qdrant_points if point.payload.get('filename')}

            # Get filenames that exist in CopyrightsFiles table
            existing_files_query = db.session.query(CopyrightsFiles.filename).filter(
                CopyrightsFiles.filename.in_(qdrant_filenames)
            ).all()
            existing_filenames = {row.filename for row in existing_files_query}

            # Find filenames that exist in Qdrant but not in CopyrightsFiles
            missing_filenames = qdrant_filenames - existing_filenames

            # Also find registration numbers in Copyrights with NULL plaintiff_id
            null_plaintiff_reg_nos_query = db.session.query(Copyrights.registration_number).filter(
                Copyrights.plaintiff_id.is_(None)
            ).all()
            null_plaintiff_reg_nos = {row.registration_number for row in null_plaintiff_reg_nos_query}

            # Get Qdrant points for missing filenames (with vectors)
            missing_filename_points = [point for point in all_qdrant_points
                                      if point.payload.get('filename') in missing_filenames]

            # Find Qdrant points with reg_no in null plaintiff list
            null_plaintiff_points = [point for point in all_qdrant_points
                                    if point.payload.get('reg_no') in null_plaintiff_reg_nos]

            # Combine both sets of orphan points
            all_orphan_points = missing_filename_points + null_plaintiff_points

            # Remove duplicates based on point.id
            seen_ids = set()
            unique_orphan_points = []
            for point in all_orphan_points:
                if point.id not in seen_ids:
                    unique_orphan_points.append(point)
                    seen_ids.add(point.id)

            if not unique_orphan_points:
                return [], {'total_count': 0}

            missing_points = unique_orphan_points

            # Apply pagination
            pagination_result = apply_pagination(missing_points, page, per_page)
            paginated_points = pagination_result.items

            # Compute similarity matrix for paginated points
            similar_points_dict = {}
            if paginated_points:
                similar_points_dict = find_most_similar_points(paginated_points, all_qdrant_points, similarity_threshold=0.90)
                # knn_points_dict = find_knn_points(paginated_points,2,0.9)

            # Format the results
            orphans = []
            for orphan_item in paginated_points:
                # Extract the actual Qdrant point from OrphanData
                point = orphan_item.data if hasattr(orphan_item, 'data') else orphan_item
                payload = point.payload
                filename = payload.get('filename', '')
                plaintiff_id = payload.get('plaintiff_id')
                plaintiff_name = payload.get('plaintiff_name')
                docket = payload.get('docket', '')
                registration_number = payload.get('reg_no')
                point_id = str(point.id)

                # Get database plaintiff information
                db_plaintiff_id = None
                db_plaintiff_name = None
                if registration_number:
                    try:
                        copyright_record = Copyrights.query.filter_by(registration_number=registration_number).first()
                        if copyright_record:
                            db_plaintiff_id = copyright_record.plaintiff_id
                            db_plaintiff_name = self.plaintiff_lookup.get(db_plaintiff_id, 'Unknown')
                    except Exception as db_error:
                        current_app.logger.warning(f"Error fetching copyright record for reg_no {registration_number}: {str(db_error)}")

                # Generate paths
                high_res_path, low_res_path = generate_file_paths(plaintiff_id, filename)

                # Get similar point data
                similar_point_data = similar_points_dict.get(point_id, {})
                # knn_point_data = knn_points_dict.get(point_id, {})
                orphan_data = {
                    'filename': filename,
                    'plaintiff_id': plaintiff_id,
                    'plaintiff_name': plaintiff_name,
                    'registration_number': registration_number,
                    'docket': docket,
                    'high_res_path': high_res_path,
                    'low_res_path': low_res_path,
                    'qdrant_id': point_id,
                    'db_plaintiff_id': db_plaintiff_id,
                    'db_plaintiff_name': db_plaintiff_name
                }

                # Add similar_point data if available
                if similar_point_data:
                    orphan_data['similar_point'] = {
                        'filename': similar_point_data.get('filename', ''),
                        'plaintiff_id': similar_point_data.get('plaintiff_id'),
                        'plaintiff_name': similar_point_data.get('plaintiff_name', ''),
                        'registration_number': similar_point_data.get('registration_number', ''),
                        'id': similar_point_data.get('id', ''),
                        'high_res_path': similar_point_data.get('high_res_path'),
                        'low_res_path': similar_point_data.get('low_res_path')
                    }
                # if knn_point_data:
                #     orphan_data['knn'] = {
                #         'filename': knn_point_data.get('filename', ''),
                #         'plaintiff_id': knn_point_data.get('plaintiff_id'),
                #         'plaintiff_name': knn_point_data.get('plaintiff_name', ''),
                #         'registration_number': knn_point_data.get('registration_number', ''),
                #         'high_res_path': knn_point_data.get('high_res_path'),
                #         'low_res_path': knn_point_data.get('low_res_path')
                #     }
                orphans.append({
                    'id': point_id,
                    'data': orphan_data
                })

            return orphans, {
                'page': page,
                'per_page': per_page,
                'total_count': pagination_result.pagination.total_count,
                'total_pages': pagination_result.pagination.total_pages,
                'has_next': pagination_result.pagination.has_next,
                'has_prev': pagination_result.pagination.has_prev
            }

        except Exception as qdrant_error:
            current_app.logger.error(f"Qdrant error in qdrant-only: {str(qdrant_error)}")
            return [], {'total_count': 0}

    def get_missing_cos_orphans(self, page=1, per_page=50):
        """Get items missing from Tencent COS storage."""
        # TODO: Implement missing COS logic
        return [], {'total_count': 0}

    def get_duplicate_reg_no_orphans(self, page=1, per_page=50):
        """Get items with duplicate registration numbers."""
        # TODO: Implement duplicate registration number logic
        return [], {'total_count': 0}
