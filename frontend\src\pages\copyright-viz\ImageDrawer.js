import React, { useState, useEffect, useRef } from 'react';
import 'react-image-crop/dist/ReactCrop.css';
import { getCopyrightTypes, updateCopyrightsFile, changeRegNo, getCopyrightMethods, splitCopyrightFile, getPlaintiffRegistrations, setCopyrightFileProductionStatus } from '../../services/api_copyright_viz';
import './ImageDrawer.css';
import MultiCropImage from './multicrop';


// Info tab for copyrights_files
const CopyrightsFilesInfoTab = ({ asset, onUpdate }) => {
    const [types, setTypes] = useState([]);
    const [methods, setMethods] = useState([]);
    const [editableAsset, setEditableAsset] = useState(asset);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [imageDimensions, setImageDimensions] = useState(null);

    useEffect(() => {
        const fetchData = async () => {
            try {
                const [typesResult, methodsResult] = await Promise.all([
                    getCopyrightTypes(),
                    getCopyrightMethods()
                ]);
                setTypes(typesResult.data || []);
                setMethods(methodsResult.data || []);
            } catch (err) {
                console.error("Failed to fetch types/methods", err);
            }
        };
        fetchData();

        // Fetch image dimensions
        if (asset.high_res_path) {
            const img = new Image();
            img.onload = () => {
                setImageDimensions(`${img.naturalWidth} x ${img.naturalHeight}`);
            };
            img.onerror = () => {
                setImageDimensions('Unable to load');
            };
            img.src = asset.high_res_path;
        }
    }, [asset.high_res_path]);

    useEffect(() => {
        setEditableAsset(asset);
    }, [asset]);

    const handleInputChange = (e) => {
        const { name, value, type, checked } = e.target;
        setEditableAsset(prev => ({
            ...prev,
            [name]: type === 'checkbox' ? checked : value
        }));
    };

    const handleUpdate = async (fieldName, value) => {
        setLoading(true);
        setError(null);
        try {
            const payload = { [fieldName]: value };
            // The `setCopyrightFileProductionStatus` is a dedicated endpoint.
            // All other updates for `copyrights_files` go through `updateCopyrightsFile`.
            const result = fieldName === 'production'
                ? await setCopyrightFileProductionStatus(asset.id, value)
                : await updateCopyrightsFile(asset.id, payload);

            onUpdate(result.data); // Pass the full updated asset from the response
        } catch (err) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="info-tab-content">
            {error && <p className="error-message">{error}</p>}

            <div className="form-group">
                <label>Registration Number</label>
                <div className="reg-no-display">
                    <input type="text" value={editableAsset.reg_no || editableAsset.registration_number || ''} readOnly disabled />
                </div>
            </div>

            <div className="form-group">
                <label>Filename</label>
                <input type="text" value={editableAsset.filename || ''} readOnly disabled />
            </div>

            <div className="form-group">
                <label>Title</label>
                <input type="text" value={editableAsset.title || ''} readOnly disabled />
            </div>

            <div className="form-group">
                <label>Dimensions</label>
                <input type="text" value={imageDimensions || 'Loading...'} readOnly disabled />
            </div>

            <div className="form-group">
                <label>Created At</label>
                <input type="text" value={editableAsset.create_time || 'N/A'} readOnly disabled />
            </div>

            <div className="form-group">
                <label>Updated At</label>
                <input type="text" value={editableAsset.update_time || 'N/A'} readOnly disabled />
            </div>

            <div className="form-group">
                <label htmlFor="production">Production Flag</label>
                <input
                    type="checkbox"
                    id="production"
                    name="production"
                    checked={editableAsset.production || false}
                    onChange={(e) => {
                        handleInputChange(e);
                        handleUpdate('production', e.target.checked);
                    }}
                    disabled={loading}
                />
            </div>

            <div className="form-group">
                <label htmlFor="method">Method</label>
                <select
                    id="method"
                    name="method"
                    value={editableAsset.method || ''}
                    onChange={(e) => {
                        handleInputChange(e);
                        handleUpdate('method', e.target.value);
                    }}
                    disabled={loading}
                >
                    <option value="">Select Method</option>
                    {methods.map((m) => (
                        <option key={m.name} value={m.name}>{m.name}</option>
                    ))}
                </select>
            </div>

            <div className="form-group">
                <label htmlFor="type">Type</label>
                <select
                    id="type"
                    name="type"
                    value={editableAsset.type || ''}
                    onChange={(e) => {
                        handleInputChange(e);
                        handleUpdate('type', e.target.value);
                    }}
                    disabled={loading}
                >
                    <option value="">Select Type</option>
                    {types.map((t) => (
                        <option key={t.name} value={t.name}>{t.name}</option>
                    ))}
                </select>
            </div>
        </div>
    );
};

// Info tab for cn_websites_files
const CnWebsitesFilesInfoTab = ({ asset, onClose, onMoveFileWithProgress }) => {
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const handleMoveTocopyrights = () => {
        if (onMoveFileWithProgress) {
            onMoveFileWithProgress([asset.id]);
            if (onClose) {
                onClose();
            }
        } else {
            console.error("onMoveFileWithProgress is not available.");
            setError("Move operation is currently unavailable.");
        }
    };

    const copyToClipboard = (text) => {
        navigator.clipboard.writeText(text);
    };

    return (
        <div className="info-tab-content">
            {error && <p className="error-message">{error}</p>}

            <div className="cn-websites-info">
                <div className="info-row">
                    <label>Registration Number:</label>
                    <span>{asset.reg_no || asset.registration_number || 'N/A'}
                        <button onClick={() => copyToClipboard(asset.reg_no || asset.registration_number || '')} className="copy-btn">📋</button>
                    </span>
                </div>

                <div className="info-row">
                    <label>Docket in Title:</label>
                    <span>{asset.docket_in_title || 'N/A'}
                        <button onClick={() => copyToClipboard(asset.docket_in_title || '')} className="copy-btn">📋</button>
                    </span>
                </div>

                <div className="info-row">
                    <label>Docket Formatted:</label>
                    <span>{asset.docket_formatted || 'N/A'}
                        <button onClick={() => copyToClipboard(asset.docket_formatted || '')} className="copy-btn">📋</button>
                    </span>
                </div>

                <div className="info-row">
                    <label>Case ID:</label>
                    <span>{asset.case_id || 'N/A'}
                        <button onClick={() => copyToClipboard(asset.case_id?.toString() || '')} className="copy-btn">📋</button>
                    </span>
                </div>

                <div className="info-row">
                    <label>URL:</label>
                    <span className="url-text">
                        {asset.url ? (
                            <a href={asset.url} target="_blank" rel="noopener noreferrer" style={{ color: '#0066cc', textDecoration: 'underline' }}>
                                {asset.url}
                            </a>
                        ) : 'N/A'}
                        <button onClick={() => copyToClipboard(asset.url || '')} className="copy-btn">📋</button>
                    </span>
                </div>

                <div className="info-row">
                    <label>Type:</label>
                    <span>{asset.type || 'N/A'}
                        <button onClick={() => copyToClipboard(asset.type || '')} className="copy-btn">📋</button>
                    </span>
                </div>

                <div className="info-row">
                    <label>Website:</label>
                    <span>{asset.website || 'N/A'}
                        <button onClick={() => copyToClipboard(asset.website || '')} className="copy-btn">📋</button>
                    </span>
                </div>

                <div className="info-row">
                    <label>Site Domain:</label>
                    <span>{asset.site_domain || 'N/A'}
                        <button onClick={() => copyToClipboard(asset.site_domain || '')} className="copy-btn">📋</button>
                    </span>
                </div>

                <div className="info-row">
                    <label>Post Date:</label>
                    <span>{asset.post_date || 'N/A'}
                        <button onClick={() => copyToClipboard(asset.post_date || '')} className="copy-btn">📋</button>
                    </span>
                </div>
            </div>

            <div className="move-action">
                <button
                    onClick={handleMoveTocopyrights}
                    disabled={loading}
                    className="move-button primary"
                >
                    {loading ? 'Moving...' : 'Move to Copyrights Files'}
                </button>
            </div>
        </div>
    );
};

const CopyrightsFilesToolsTab = ({ asset, plaintiffId, onUpdate }) => {
    const [splitMode, setSplitMode] = useState(false);
    const [cropPreviews, setCropPreviews] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const proxiedImageUrl = asset.high_res_path
        ? `/api/v1/copyright/image-proxy?url=${encodeURIComponent(asset.high_res_path)}`
        : '';

    const handleSplitClick = () => {
        setSplitMode(true);
        // Reset previews when starting a new split session
        setCropPreviews([]);
        setError(null);
    };
    const blobToBase64 = (blob) => {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsDataURL(blob);
            reader.onloadend = () => {
                resolve(reader.result);
            };
            reader.onerror = (error) => {
                reject(error);
            };
        });
    };

    const handleCancelSplit = () => {
        setSplitMode(false);
        setCropPreviews([]);
        setError(null);
    };

    // This handler now receives the full preview objects with blobs from MultiCropImage
    const handleCropChange = (newCropPreviews) => {
        setCropPreviews(newCropPreviews);
    };
    const handleSplitSave = async () => {
        if (cropPreviews.length === 0) {
            setError("Please create at least one crop area.");
            return;
        }
        const isEveryCropLinked = cropPreviews.every(p => p.registrationIdentifier || p.action);
        console.log('isEveryCropLinked:', isEveryCropLinked);
        if (!isEveryCropLinked) {
            setError("Please link every cropped snippet to a registration before saving.");
            return;
        }

        console.log('here are the crop previews:', cropPreviews);
        setLoading(true);
        setError(null);

        try {
            // 1. Convert all blob previews to Base64 strings in parallel
            const payloadPromises = cropPreviews.map(async (preview) => {
                const base64String = await blobToBase64(preview.blob);
                return {
                    image_data: base64String, // e.g., "data:image/png;base64,iVBORw0KGgo..."
                    registration_id: preview.registrationIdentifier,
                    action: preview.action
                };
            });

            // Wait for all conversions to finish
            const cropsPayload = await Promise.all(payloadPromises);

            // 2. Prepare the final JSON body
            const finalBody = {
                asset_id: asset.id,
                crops: cropsPayload
            };
            const response = await splitCopyrightFile(finalBody, asset.id);
            onUpdate({
                asset_to_add: response.assets_added || [],
                asset_to_update: response.assets_updated[0] || []
            });
            setSplitMode(false);
            setCropPreviews([]);
        } catch (err) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };
    return (
        <div className="tools-tab-content">
            {error && <p className="error-message">{error}</p>}

            <div className="tool-section">
                <h4>Split Tool</h4>
                <p>Define multiple regions to create new child files from this image.</p>
                {!splitMode && (
                    <button
                        onClick={handleSplitClick}
                        disabled={loading}
                        className="tool-button"
                    >
                        Start Split
                    </button>
                )}

                {splitMode && (
                    <div className="split-editor">
                        <div className="crop-tab-content">
                            <p>Create multiple crops. Saving will generate new assets from the cropped areas.</p>
                            <MultiCropImage
                                proxiedImageUrl={proxiedImageUrl}
                                onCropChange={handleCropChange}
                                plaintiffId={plaintiffId}
                            />
                            <div className="split-actions">
                                <button onClick={handleSplitSave} disabled={loading || cropPreviews.length === 0}>
                                    {loading ? 'Saving...' : `Save Split ${cropPreviews.length} Crop(s)`}
                                </button>
                                <button onClick={handleCancelSplit} disabled={loading}>
                                    Cancel
                                </button>
                            </div>
                        </div>
                    </div>
                )}
            </div>

            <div className="tool-section">
                <h4>Remove Watermark</h4>
                <button
                    disabled
                    className="tool-button disabled"
                    title="Coming soon"
                >
                    Remove Watermark (Coming Soon)
                </button>
            </div>
        </div>
    );
};

// Registration Number Allocation Tool
const RegNumberAllocationTool = ({ asset, plaintiffId, onUpdate, plaintiffs }) => {
    const [registrations, setRegistrations] = useState([]);
    const [selectedRegNo, setSelectedRegNo] = useState('');
    const [searchTerm, setSearchTerm] = useState('');
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [dropdownHeight, setDropdownHeight] = useState(5); // Default size
    const [existingImages, setExistingImages] = useState([]);

    // Sort function similar to the one used in cards
    const sortRegistrations = (items) => {
        return items.sort((a, b) => {
            const aRegNo = a.reg_no || '';
            const bRegNo = b.reg_no || '';

            // MD-prefixed items go last
            const aIsMD = aRegNo.startsWith('MD');
            const bIsMD = bRegNo.startsWith('MD');

            if (aIsMD && !bIsMD) return 1;
            if (!aIsMD && bIsMD) return -1;

            // Alphabetical sort within each group
            return aRegNo.localeCompare(bRegNo);
        });
    };

    useEffect(() => {
        const fetchRegistrations = async () => {
            if (!plaintiffId) return;
            try {
                const result = await getPlaintiffRegistrations(plaintiffId);
                const sortedRegistrations = sortRegistrations(result.data || []);
                setRegistrations(sortedRegistrations);
            } catch (err) {
                console.error("Failed to fetch registrations", err);
            }
        };
        fetchRegistrations();
    }, [plaintiffId]);

    useEffect(() => {
        if (selectedRegNo && plaintiffs) {
            const currentPlaintiff = plaintiffs.find(p => p.plaintiff.id === plaintiffId);
            if (currentPlaintiff) {
                const images = currentPlaintiff.copyrights_files.filter(file => {
                    const regNo = selectedRegNo;
                    const method = asset.method;
                    const baseFilename = `${regNo}_${method}.webp`;
                    const indexedFilenameRegex = new RegExp(`^${regNo}_${method}_(\\d+)\\.webp$`);

                    return file.filename === baseFilename || indexedFilenameRegex.test(file.filename);
                });
                setExistingImages(images);
            }
        } else {
            setExistingImages([]);
        }
    }, [selectedRegNo, plaintiffs, plaintiffId, asset.method]);

    const filteredRegistrations = sortRegistrations(
        registrations.filter(reg =>
            reg.reg_no.toLowerCase().includes(searchTerm.toLowerCase()) ||
            reg.title.toLowerCase().includes(searchTerm.toLowerCase())
        )
    );

    const handleAssignRegNo = async (replaceFilename = null, keepExisting = false) => {
        if (!selectedRegNo) return;

        setLoading(true);
        setError(null);
        try {
            const result = await changeRegNo(asset.id, selectedRegNo, replaceFilename, keepExisting);
            // The backend now returns a more complex object for replacements
            console.log('result:', result);
            if (result.asset_to_update || result.asset_to_delete || result.asset_to_add) {
                const updatePayload = {
                    asset_to_delete: result.asset_to_delete,
                    asset_to_update: result.asset_to_update,
                    asset_to_add: result.asset_to_add
                };
                onUpdate(updatePayload);
            }
        } catch (err) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="reg-allocation-tool">
            <h4>Assign Registration Number</h4>
            {error && <p className="error-message">{error}</p>}

            <div className="current-assignment">
                <label>Current Assignment:</label>
                <span>{asset.reg_no || asset.registration_number || 'None'}</span>
            </div>

            <div className="search-select">
                <input
                    type="text"
                    placeholder="Search registrations..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="search-input"
                />

                <div className="select-container">
                    <select
                        value={selectedRegNo}
                        onChange={(e) => setSelectedRegNo(e.target.value)}
                        className="reg-select"
                        size={Math.min(dropdownHeight, filteredRegistrations.length + 1)}
                        style={{
                            minHeight: '120px',
                            maxHeight: '300px',
                            width: '100%',
                            whiteSpace: 'normal',
                            wordWrap: 'break-word'
                        }}
                    >
                        <option value="">Select a registration number</option>
                        {filteredRegistrations.map((reg) => (
                            <option
                                key={reg.reg_no}
                                value={reg.reg_no}
                                style={{
                                    whiteSpace: 'normal',
                                    wordWrap: 'break-word',
                                    padding: '4px',
                                    lineHeight: '1.2'
                                }}
                                title={`${reg.reg_no} - ${reg.title}`} // Tooltip for full text
                            >
                                {reg.reg_no} - {reg.title}
                            </option>
                        ))}
                    </select>

                    <div className="resize-controls">
                        <button
                            type="button"
                            onClick={() => setDropdownHeight(prev => Math.min(prev + 2, 15))}
                            className="resize-btn"
                            title="Expand dropdown"
                        >
                            ▼
                        </button>
                        <button
                            type="button"
                            onClick={() => setDropdownHeight(prev => Math.max(prev - 2, 3))}
                            className="resize-btn"
                            title="Shrink dropdown"
                        >
                            ▲
                        </button>
                    </div>
                </div>

                <div className="image-preview-container">
                    {existingImages.length > 0 ? (
                        existingImages.map(image => (
                            <div key={image.id} className="image-preview-item">
                                <img src={image.low_res_path} alt={`Preview of ${image.filename}`} />
                                <button
                                    key={`replace-${image.id}`}
                                    onClick={() => handleAssignRegNo(image.filename, false)}
                                    disabled={loading || !selectedRegNo}
                                    className="tool-button"
                                >
                                    {loading ? 'Assigning...' : `Assign and Replace ${image.filename}`}
                                </button>
                                <div style={{ height: '1rem' }} />
                            </div>
                        ))
                    ) : (
                        selectedRegNo && <p>No existing images for this registration number / method combination.</p>
                    )}
                </div>

                <div className="action-buttons">
                    {existingImages.length > 0 ? (
                        <>
                            <button
                                onClick={() => handleAssignRegNo(null, true)}
                                disabled={loading || !selectedRegNo}
                                className="tool-button"
                            >
                                {loading ? 'Assigning...' : 'Assign and Keep Existing'}
                            </button>
                        </>
                    ) : (
                        <button
                            onClick={() => handleAssignRegNo()}
                            disabled={loading || !selectedRegNo}
                            className="tool-button"
                        >
                            {loading ? 'Assigning...' : 'Assign Registration Number'}
                        </button>
                    )}
                </div>
            </div>
        </div>
    );
};



const ImageDrawer = ({ asset, onClose, onUpdate, plaintiffId, onMoveFileWithProgress, plaintiffs }) => {
    const [activeTab, setActiveTab] = useState('info');

    if (!asset) return null;

    // Determine if this is a copyrights_files or cn_websites_files asset
    const iscopyrightFile = asset.hasOwnProperty('production');
    const isCnWebsiteFile = asset.hasOwnProperty('docket_in_title');

    const handleInfoTabUpdate = (updatedAsset) => {
        onUpdate(updatedAsset);
    };

    const handleTabClick = (tabName) => {
        setActiveTab(tabName);
    };

    const renderTabContent = () => {
        switch (activeTab) {
            case 'info':
                if (iscopyrightFile) {
                    return <CopyrightsFilesInfoTab asset={asset} onUpdate={handleInfoTabUpdate} />;
                } else if (isCnWebsiteFile) {
                    return <CnWebsitesFilesInfoTab asset={asset} onClose={onClose} onMoveFileWithProgress={onMoveFileWithProgress} />;
                }
                return <div>Unknown asset type</div>;

            case 'tools':
                if (iscopyrightFile) {
                    return <CopyrightsFilesToolsTab asset={asset} plaintiffId={plaintiffId} onUpdate={onUpdate} />;
                }
                return <div>Tools not available for this asset type</div>;

            case 'preview':
                return (
                    <div className="preview-tab-content">
                        {/* High-quality image preview */}
                        <div className="image-preview">
                            {asset.high_res_path ? (
                                <img src={asset.high_res_path} alt={`High-res preview of ${asset.reg_no || asset.registration_number}`} style={{ maxWidth: '100%' }} />
                            ) : (
                                <p>No high-resolution image available.</p>
                            )}
                        </div>

                        {/* Certificate preview (only for copyrights_files) */}
                        {iscopyrightFile && (
                            <div className="certificate-preview">
                                <h4>Certificate</h4>
                                {asset.certificate_path ? (
                                    <img src={asset.certificate_path} alt={`Certificate for ${asset.reg_no}`} style={{ maxWidth: '100%' }} />
                                ) : (
                                    <div className="empty-state">
                                        <p>No certificate available.</p>
                                        <button className="attach-certificate-btn">Attach Certificate</button>
                                    </div>
                                )}
                            </div>
                        )}
                    </div>
                );

            case 'reg-allocation':
                if (iscopyrightFile) {
                    return <RegNumberAllocationTool asset={asset} plaintiffId={plaintiffId} onUpdate={onUpdate} plaintiffs={plaintiffs} />;
                }
                return <div>Registration allocation not available for this asset type</div>;

            default:
                return null;
        }
    };

    const getTabsForAssetType = () => {
        if (iscopyrightFile) {
            return [
                { key: 'info', label: 'Info' },
                { key: 'tools', label: 'Tools' },
                { key: 'preview', label: 'Preview' },
                { key: 'reg-allocation', label: 'Assign RegNo' }
            ];
        } else if (isCnWebsiteFile) {
            return [
                { key: 'info', label: 'Info' }
            ];
        }
        return [{ key: 'info', label: 'Info' }];
    };

    const tabs = getTabsForAssetType();

    return (
        <div className="drawer-overlay" onClick={onClose}>
            <div className="drawer-panel" onClick={(e) => e.stopPropagation()}>
                <div className="drawer-header">
                    <h3>{asset.reg_no || asset.registration_number || 'Unknown'}</h3>
                    <button onClick={onClose} className="drawer-close-btn">&times;</button>
                </div>
                <div className="drawer-tabs">
                    {tabs.map(tab => (
                        <div
                            key={tab.key}
                            className={`drawer-tab ${activeTab === tab.key ? 'active' : ''}`}
                            onClick={() => handleTabClick(tab.key)}
                        >
                            {tab.label}
                        </div>
                    ))}
                </div>
                <div className="drawer-content">
                    {renderTabContent()}
                </div>
            </div>
        </div>
    );
};

export default ImageDrawer;