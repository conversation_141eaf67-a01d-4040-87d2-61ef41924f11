# Utility functions for querying data from the database, integrated with Flask-SQLAlchemy.
import uuid

import pandas as pd
from sqlalchemy import text
from flask import current_app
from sqlalchemy.exc import SQLAlchemyError
from backend.extensions import db
import time
import threading
import os
import json
import base64
import zlib
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
import mysql.connector
import uuid
from functools import wraps
from flask import g
import pyarrow.feather as feather
import threading

# Create a global lock for feather file operations
feather_lock = threading.Lock()

### Postgresql German server's maidalv_db database

def get_data_as_dataframe(asset_ids: list, table_name: str):
    """
    Get metadata for IP assets from a specified table using Flask-SQLAlchemy.
    
    Args:
        asset_ids (list): A list of IP asset IDs (UUIDs).
        table_name (str): The name of the table to query (e.g., 'patents', 'trademarks').
        
    Returns:
        pd.DataFrame: A pandas DataFrame containing the metadata for the requested assets.
    """
    if not asset_ids:
        return pd.DataFrame()

    # Ensure table_name is a safe value to prevent SQL injection.
    allowed_tables = ['patents', 'trademarks', 'copyrights']
    if table_name not in allowed_tables:
        raise ValueError(f"Table '{table_name}' is not a permitted table for querying.")

    uuids = list(set(asset_ids))
    # TODO: Improve DB Fetching preferably apply Repository Pattern
    # Use the 'maidalv_db' bind, which is configured for the MAIDALV database.
    engine = db.get_engine(bind='maidalv_db')
    uuids = [uuid.UUID(u) for u in uuids]
    query = text(f"SELECT * FROM {table_name} WHERE id = ANY(:asset_uuids)")

    try:
        with engine.connect() as connection:
            result = connection.execute(query, {"asset_uuids": uuids})
            
            # Fetch all records and create a DataFrame
            df = pd.DataFrame(result.fetchall(), columns=result.keys())

            if df.shape[0] != len(uuids):
                print(f"⚠️ Warning: Expected {len(uuids)} unique assets from '{table_name}', but found {df.shape[0]} in the database.")
            
            return df

    except SQLAlchemyError as e:
        # Log the error appropriately in a real application
        print(f"❌ Database error occurred while querying '{table_name}': {e}")
        raise RuntimeError(f"Database error occurred: {e}") from e
    except Exception as e:
        print(f"❌ An unexpected error occurred while querying '{table_name}': {e}")
        raise RuntimeError(f"An unexpected error occurred: {e}") from e

def get_patent_data_as_dataframe(asset_ids):
    """
    Get metadata for patent assets from PostgreSQL.
    This is a convenience wrapper around get_data_as_dataframe.
    """
    return get_data_as_dataframe(asset_ids, 'patents')

def get_trademark_data_as_dataframe(asset_ids):
    """
    Get metadata for trademark assets from PostgreSQL.
    This is a convenience wrapper around get_data_as_dataframe.
    """
    return get_data_as_dataframe(asset_ids, 'trademarks')

def get_copyright_data_as_dataframe(
    copyright_ids: list = None,
    registration_number: str = None,
    plaintiff_id: int = None,
    method: str = None,
    page: int = None,
    per_page: int = None
):
    """
    Get metadata for copyright assets from the external `maidalv_db`.

    This function can filter by a list of IDs, registration number, plaintiff_id,
    and supports pagination.

    Args:
        copyright_ids (list, optional): A list of copyright UUIDs to filter by.
        registration_number (str, optional): A registration number to filter by (case-insensitive like).
        plaintiff_id (int, optional): A plaintiff ID to filter by.
        method (str, optional): The method to filter by.
        page (int, optional): The page number for pagination.
        per_page (int, optional): The number of items per page for pagination.

    Returns:
        tuple[pd.DataFrame, int]: A tuple containing:
            - A pandas DataFrame with the metadata for the requested assets.
            - An integer with the total count of records matching the filter.
    """
    # Use the 'maidalv_db' bind.
    engine = db.get_engine(bind='maidalv_db')
    
    # --- Build Filter Conditions ---
    where_clauses = []
    params = {}

    if copyright_ids is not None:
        # If the list is empty, no results should be returned.
        if not copyright_ids:
            return pd.DataFrame(), 0
        where_clauses.append("id = ANY(:copyright_ids)")
        params['copyright_ids'] = list(set(copyright_ids))
    
    if registration_number:
        where_clauses.append("registration_number ILIKE :reg_num")
        params['reg_num'] = f"%{registration_number}%"

    if plaintiff_id:
        where_clauses.append("plaintiff_id = :plaintiff_id")
        params['plaintiff_id'] = plaintiff_id
    
    if method:
        where_clauses.append("method = :method")
        params['method'] = method

    where_sql = " AND ".join(where_clauses) if where_clauses else "1=1"

    try:
        with engine.connect() as connection:
            # --- Get Total Count ---
            count_query_str = f"SELECT COUNT(*) FROM copyrights WHERE {where_sql}"
            count_query = text(count_query_str)
            total_count = connection.execute(count_query, params).scalar_one()

            if total_count == 0:
                return pd.DataFrame(), 0

            # --- Get Paginated Data ---
            data_query_str = f"SELECT * FROM copyrights WHERE {where_sql} ORDER BY id" # Consistent ordering is important
            
            if page and per_page:
                data_query_str += " LIMIT :limit OFFSET :offset"
                params['limit'] = per_page
                params['offset'] = (page - 1) * per_page
            
            data_query = text(data_query_str)
            result = connection.execute(data_query, params)
            df = pd.DataFrame(result.fetchall(), columns=result.keys())
            
            return df, total_count

    except SQLAlchemyError as e:
        current_app.logger.error(f"Database error occurred while querying copyrights: {e}")
        raise RuntimeError(f"Database error occurred: {e}") from e
    
    
### Mysql: Guangzhou server's database 
    
_custom_pools = {}

class LazyConnectionPool:
    def __init__(self, pool_name, pool_size, **db_config):
        self.pool_name = pool_name
        self.max_pool_size = pool_size
        self.db_config = db_config
        self.connections = []  # Available connections
        self.in_use = {}    # Currently in-use connections with timestamps {conn: checkout_time}
        self.lock = threading.RLock()  # For thread safety
        self.initialize_in_progress = False
        self.last_cleanup = time.time()  # Track last cleanup time

    @retry(
        stop=stop_after_attempt(3),  # Retry up to 3 times
        wait=wait_exponential(multiplier=1, min=2, max=10),  # Wait 2s, 4s, 8s between retries
        retry=retry_if_exception_type(mysql.connector.Error) # Only retry on mysql connector errors
    )
    def _create_connection(self):
        """Creates a new database connection with retry logic."""
        conn = mysql.connector.connect(**self.db_config)
        return conn

    def _cleanup_stale_connections(self):
        """Clean up connections that have been idle for more than 10 hours"""
        current_time = time.time()
        
        # Only run cleanup every 30 minutes to avoid overhead
        if current_time - self.last_cleanup < 30 * 60:  # 30 minutes
            return
            
        self.last_cleanup = current_time
        stale_connections = []
        
        # Find connections idle for more than 10 hours
        with self.lock:
            for conn, checkout_time in list(self.in_use.items()):
                if current_time - checkout_time > 10 * 60 * 60:  # 1 hour
                    stale_connections.append(conn)
        
        # Force return stale connections
        for conn in stale_connections:
            print(f"Auto-cleaning stale connection (idle for >1 hour)")
            self.return_connection_to_the_pool(conn, force_cleanup=True)

    def initialize_background(self):
        """Initialize remaining connections in background"""
        if self.initialize_in_progress:
            return

        def create_connections():
            self.initialize_in_progress = True
            try:
                with self.lock:
                    current_count = len(self.connections) + len(self.in_use)
                    to_create = self.max_pool_size - current_count

                print(f"Creating {to_create} additional connections in background")
                for _ in range(to_create):
                    try:
                        # Use the retry-enabled helper method
                        conn = self._create_connection()
                        with self.lock:
                            self.connections.append(conn)
                        print(f"Added background connection to pool (total: {len(self.connections) + len(self.in_use)})")
                    except Exception as e:
                        # Log error if retries ultimately fail
                        print(f"Error creating background connection after retries: {e}")
            finally:
                self.initialize_in_progress = False

        # Start background thread
        bg_thread = threading.Thread(target=create_connections)
        bg_thread.daemon = True  # Allow program to exit even if thread is running
        bg_thread.start()

    def get_connection(self):
        """Get a connection from the pool or create a new one if needed"""
        # Run cleanup check
        self._cleanup_stale_connections()

        with self.lock:
            # If we have available connections, use one
            if self.connections:
                conn = self.connections.pop()
                try:
                    # Verify connection still works (it might have timed out)
                    if not is_connection_alive(conn):
                        print("Stale connection detected, creating new one.")
                        conn = self._create_connection() # Use helper
                except Exception as e:
                    # If verification fails or creating new fails after retries
                    print(f"Error verifying/replacing connection: {e}. Creating new one.")
                    try:
                        conn = self._create_connection() # Use helper
                    except Exception as final_e:
                         print(f"Failed to create connection even after retries: {final_e}")
                         raise final_e # Re-raise if all retries fail

                self.in_use[conn] = time.time()  # Store checkout time
                return conn

            # If no available connections but we haven't reached max, create new one
            current_count = len(self.connections) + len(self.in_use)
            if current_count < self.max_pool_size:
                try:
                    conn = self._create_connection() # Use helper
                    self.in_use[conn] = time.time()  # Store checkout time

                    # Start background initialization if this is the first connection
                    if current_count == 0 and not self.initialize_in_progress:
                        bg_thread = threading.Thread(target=self.initialize_background)
                        bg_thread.daemon = True
                        bg_thread.start()

                    return conn
                except Exception as e:
                    print(f"Failed to create initial/new connection after retries: {e}")
                    raise # Re-raise if all retries fail

            # If we're at max pool size, try one more cleanup and check again
            print(f"Pool exhausted, attempting emergency cleanup...")
            self._emergency_cleanup()

            # Check if cleanup freed up any connections
            current_count = len(self.connections) + len(self.in_use)
            if current_count < self.max_pool_size:
                try:
                    conn = self._create_connection()
                    self.in_use[conn] = time.time()
                    print("Created connection after emergency cleanup")
                    return conn
                except Exception as e:
                    print(f"Failed to create connection after emergency cleanup: {e}")

            print(f"Connection pool {self.pool_name} exhausted (max size: {self.max_pool_size}). Waiting might be needed.")
            raise Exception(f"Connection pool exhausted (max size: {self.max_pool_size})")


    def _emergency_cleanup(self):
        """Emergency cleanup for connections idle for more than 30 minutes"""
        current_time = time.time()
        stale_connections = []
        
        with self.lock:
            for conn, checkout_time in list(self.in_use.items()):
                if current_time - checkout_time > 1800:  # 30 minutes
                    stale_connections.append(conn)
        
        for conn in stale_connections:
            print(f"Emergency cleanup: forcing return of connection idle for >30 minutes")
            self.return_connection_to_the_pool(conn, force_cleanup=True)


    def return_connection_to_the_pool(self, connection, force_cleanup=False):
        """Return a connection to the pool"""
        with self.lock:
            if connection in self.in_use or force_cleanup:
                if connection in self.in_use:
                    del self.in_use[connection]

                if is_connection_alive(connection):
                    # Only add back if pool is not full (shouldn't happen often, but safety)
                    if len(self.connections) < self.max_pool_size - len(self.in_use):
                         self.connections.append(connection)
                    else:
                         try:
                              connection.close() # Close surplus connection
                         except: pass # Ignore errors closing
                else:
                    print("Returned connection is dead. Attempting to replace.")
                    # If connection is dead, try to create a replacement if pool below max
                    if len(self.connections) + len(self.in_use) < self.max_pool_size:
                        try:
                            # Use a separate thread to avoid blocking the return call
                            def replace_conn():
                                try:
                                    new_conn = self._create_connection() # Use helper
                                    with self.lock:
                                         # Check again in case state changed
                                         if len(self.connections) + len(self.in_use) < self.max_pool_size:
                                              self.connections.append(new_conn)
                                              print("Successfully replaced dead connection.")
                                         else:
                                              try: new_conn.close()
                                              except: pass
                                except Exception as e:
                                    print(f"Failed to replace dead connection after retries: {e}")

                            replace_thread = threading.Thread(target=replace_conn)
                            replace_thread.daemon = True
                            replace_thread.start()

                        except Exception as e:
                            # This catch is mainly for thread creation errors, unlikely
                            print(f"Error initiating replacement for dead connection: {e}")
            # else: connection not part of 'in_use', maybe already returned or invalid handle

# def get_table_from_GZ(table_name, force_refresh=True, chunk_size=1000, where_clause: str = None):
#     start_time = time.time()

#     all_rows = []
#     columns = []
#     df = pd.DataFrame() # Initialize df to ensure it's always defined

#     try:
#         with get_gz_connection() as gz_connection:
#             with gz_connection.cursor() as gz_cursor:
#                 print(f"Fetching '{table_name}' from GZ: ", end="", flush=True)
#                 # Read data from US database
#                 if where_clause and where_clause.strip():
#                     query = f"SELECT * FROM {table_name} WHERE {where_clause}"
#                     # print(f" with WHERE clause: {where_clause}", end="", flush=True) # Optional logging
#                 else:
#                     query = f"SELECT * FROM {table_name}"
#                 gz_cursor.execute(query)
#                 columns = [desc[0] for desc in gz_cursor.description] # Get columns after execute

#                 fetched_count = 0
#                 while True:
#                     fetch_start_time = time.time()
#                     # Fetch data in chunks
#                     chunk = gz_cursor.fetchmany(chunk_size)
#                     if not chunk:
#                         break # No more data

#                     all_rows.extend(chunk) # More efficient than appending lists
#                     fetched_count += len(chunk)
#                     fetch_duration = time.time() - fetch_start_time
#                     total_duration = time.time() - start_time
#                     print(f" | {fetched_count/1000:.0f}k ({fetch_duration:.1f}s)", end="", flush=True)

#         # Convert list of rows to DataFrame *after* closing the connection
#         if not all_rows:
#              print(f"Warning: No data fetched for table {table_name}.")
#              df = pd.DataFrame([], columns=columns) # Create empty DataFrame with correct columns
#         else:
#             df = pd.DataFrame(all_rows, columns=columns)
#             all_rows = [] # Free memory

#         print(f" | DONE ({time.time() - start_time:.2f}s)")

#         # Automatically decompress and parse the 'images' column for tb_case table
#         if table_name == "tb_case" and 'images' in df.columns:
#             df['images'] = df['images'].apply(lambda x: json.loads(zlib.decompress(base64.b64decode(x)).decode('utf-8')) if pd.notna(x) and isinstance(x, str) else x)

#     except Exception as e:
#         print(f"An error occurred while fetching or processing table {table_name}: {e}")

#     return df


def get_table_from_GZ(table_name, force_refresh=True, chunk_size=1000, where_clause: str = None):
    start_time = time.time()
    tables_folder = os.path.join(os.getcwd(), "tables")
    os.makedirs(tables_folder, exist_ok=True)
    file_path = os.path.join(tables_folder, f"{table_name}.feather")

    # If where_clause is provided and is not empty, skip feather cache reading
    if not (where_clause and where_clause.strip()):
        if os.path.exists(file_path) and not force_refresh:
            try:
                # Check file modification time if needed for more robust caching
                df = feather.read_feather(file_path)
                if table_name == "tb_case" and 'images' in df.columns:
                    df["images"] = df["images"].apply(lambda x: json.loads(zlib.decompress(base64.b64decode(x)).decode('utf-8')) if pd.notna(x) and isinstance(x, str) else x)
                print(f"!!! {table_name} loaded from feather file in {time.time() - start_time:.2f} seconds")
                return df
            except Exception as e:
                print(f"Warning: Error reading feather file {file_path}, refreshing data. Error: {e}")
    else:
        # Optional: Log that feather read is skipped
        print(f"Skipping feather cache read for {table_name} due to where_clause.")


    all_rows = []
    columns = []
    df = pd.DataFrame() # Initialize df to ensure it's always defined

    try:
        with get_gz_connection() as gz_connection:
            with gz_connection.cursor() as gz_cursor:
                print(f"Fetching '{table_name}' from GZ: ", end="", flush=True)
                # Read data from US database
                if where_clause and where_clause.strip():
                    query = f"SELECT * FROM {table_name} WHERE {where_clause}"
                    # print(f" with WHERE clause: {where_clause}", end="", flush=True) # Optional logging
                else:
                    query = f"SELECT * FROM {table_name}"
                gz_cursor.execute(query)
                columns = [desc[0] for desc in gz_cursor.description] # Get columns after execute

                fetched_count = 0
                while True:
                    fetch_start_time = time.time()
                    # Fetch data in chunks
                    chunk = gz_cursor.fetchmany(chunk_size)
                    if not chunk:
                        break # No more data

                    all_rows.extend(chunk) # More efficient than appending lists
                    fetched_count += len(chunk)
                    fetch_duration = time.time() - fetch_start_time
                    total_duration = time.time() - start_time
                    print(f" | {fetched_count/1000:.0f}k ({fetch_duration:.1f}s)", end="", flush=True)

        # Convert list of rows to DataFrame *after* closing the connection
        if not all_rows:
             print(f"Warning: No data fetched for table {table_name}.")
             df = pd.DataFrame([], columns=columns) # Create empty DataFrame with correct columns
        else:
            df = pd.DataFrame(all_rows, columns=columns)
            all_rows = [] # Free memory

        print(f" | DONE ({time.time() - start_time:.2f}s)")

        # Save to feather file only if no where_clause was used (or if it was empty/None)
        if not (where_clause and where_clause.strip()):
            with feather_lock: # Use the global lock for feather file operations
                feather.write_feather(df, file_path)
        else:
            # Optional: Log that feather write is skipped
            print(f" | Skipping feather write for {table_name} due to where_clause.", end="")

        # Automatically decompress and parse the 'images' column for tb_case table
        if table_name == "tb_case" and 'images' in df.columns:
            df['images'] = df['images'].apply(lambda x: json.loads(zlib.decompress(base64.b64decode(x)).decode('utf-8')) if pd.notna(x) and isinstance(x, str) else x)

    except Exception as e:
        print(f"An error occurred while fetching or processing table {table_name}: {e}")

    return df






@retry(
    stop=stop_after_attempt(3),  # Retry up to 3 times
    wait=wait_exponential(multiplier=1, min=2, max=10),  # Wait 2s, 4s, 8s between retries
    retry=retry_if_exception_type((mysql.connector.Error, OSError, ConnectionError)) # Retry on mysql errors and network issues
)
def get_gz_connection(host=os.getenv("MYSQL_HOST"), user=os.getenv("MYSQL_USER"),
                        password=os.getenv("MYSQL_PASSWORD"), database=os.getenv("MYSQL_DATABASE"),
                        port=3306):
    """
    Get a connection from a custom pool that lazily creates connections as needed.
    This creates just one connection initially and starts a background thread for others.
    Enhanced with retry logic for network failures and connection timeouts.
    """
    base_key = f"{host}_{user}_{database}_{port}"

    # Create the pool if it doesn't exist yet
    if base_key not in _custom_pools:
        try:
            # Generate a unique pool name
            pool_name = f"lazy_pool_{base_key}"
            dbconfig = {
                'host': host,
                'port': port,
                'user': user,
                'password': password,
                'database': database,
                # 'use_pure': True,  # Not compatible with compression
                'autocommit': True,
                # 'buffered': True,
                'charset': 'utf8mb4',
                'collation': 'utf8mb4_unicode_ci',
                'consume_results': True,
                'connect_timeout': 30,  # Increased timeout for network issues
                'connection_timeout': 30,  # Increased timeout for network issues
                'ssl_disabled': True,  # Disable SSL to avoid SSL version mismatch issues
            }
            if os.name == 'nt':
                dbconfig['compress'] = True       # Enable network compression, but leads to error: unpack requires a buffer of 4 bytes if used with 'use_pure'

            # Create our custom pool with increased size for better concurrency
            start_time = time.time()
            pool = LazyConnectionPool(pool_name=pool_name, pool_size=5, **dbconfig)
            end_time = time.time()

            _custom_pools[base_key] = pool
            print(f"Created lazy connection pool manager: {pool_name} in {end_time - start_time:.1f} seconds")
        except Exception as e:
            error_msg = f"Error creating connection pool for GZ database: {e}"
            print(error_msg)
            current_app.logger.error(error_msg)
            raise RuntimeError(error_msg) from e

    # Get connection from pool with enhanced error handling
    try:
        start_time = time.time()
        # Retrieve the pool object from the dictionary first
        pool = _custom_pools[base_key]
        connection = pool.get_connection()
        end_time = time.time()
        print(f"Retrieved connection from pool {pool.pool_name} in {end_time - start_time:.1f} seconds")

        # Verify connection health before returning
        if not is_connection_alive(connection):
            print(f"Connection from pool is not healthy, attempting to replace...")
            try:
                connection.close()
            except Exception:
                pass  # Ignore close errors
            # Try to get a fresh connection
            connection = mysql.connector.connect(**pool.db_config)

        # Create a wrapper that returns the connection to the pool when closed
        original_close = connection.close
        def close_and_return():
            try:
                _custom_pools[base_key].return_connection_to_the_pool(connection)
            except Exception as e:
                print(f"Error returning connection to pool: {e}")
                # If we can't return to pool, try to close the connection
                try:
                    original_close()
                except Exception:
                    pass

        connection.close = close_and_return

        return connection
    except (mysql.connector.Error, OSError, ConnectionError) as e:
        error_msg = f"Network or MySQL error getting connection from pool: {e}"
        print(error_msg)
        current_app.logger.error(error_msg)
        raise RuntimeError(error_msg) from e
    except Exception as e:
        error_msg = f"Unexpected error getting connection from pool: {e}"
        print(error_msg)
        current_app.logger.error(error_msg)
        raise RuntimeError(error_msg) from e
    
def is_connection_alive(connection):
    """Check if the database connection is still alive with timeout protection"""
    if connection is None:
        return False

    try:
        # Set a timeout for the connection check to prevent hanging
        original_timeout = getattr(connection, '_read_timeout', None)
        if hasattr(connection, '_read_timeout'):
            connection._read_timeout = 5  # 5 second timeout for health check

        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()

        # Restore original timeout
        if original_timeout is not None and hasattr(connection, '_read_timeout'):
            connection._read_timeout = original_timeout

        return result is not None
    except (mysql.connector.Error, OSError, ConnectionError) as e:
        # These are expected network/connection errors
        print(f"Connection health check failed with network error: {e}")
        return False
    except Exception as e:
        # Log unexpected errors but still return False
        print(f"Connection health check failed with unexpected error: {e}")
        return False
    
# Inserts in batch but does not return the IDs
@retry(
    stop=stop_after_attempt(3),  # Retry up to 3 times
    wait=wait_exponential(multiplier=1, min=2, max=10),  # Wait 2s, 4s, 8s between retries
    retry=retry_if_exception_type((mysql.connector.Error, OSError, ConnectionError)) # Retry on mysql errors and network issues
)
def insert_and_update_df_to_GZ_batch(df, table_name, key_column, conn=None):
    if len(df) == 0:
        return df

    # Track if we created our own connection
    own_connection = conn is None
    if own_connection:
        conn = get_gz_connection()

    cursor = None
    try:
        cursor = conn.cursor()
        print(f"Updating table ({len(df)} records): {table_name}", end="", flush=True)

        df = df.copy() # Avoid SettingWithCopyWarning
        df.drop(['status', 'creator', 'create_time', 'updater', 'update_time', 'deleted', 'tenant_id'], axis=1, inplace=True, errors='ignore')

        batch_size = 500
        total_processed = 0

        for start in range(0, len(df), batch_size):
            end = min(start + batch_size, len(df))
            batch = df.iloc[start:end].reset_index(drop=True)

            # Convert columns with mixed type but at least one string to string type
            string_column_fixed = []
            for col in batch.columns:
                if batch[col].dtype == 'object' and any(isinstance(x, str) for x in batch[col].dropna()):
                    batch[col] = batch[col].apply(lambda x: str(x) if x is not None and not pd.isna(x) else None)
                    string_column_fixed.append(col)

            columns_str = ', '.join(batch.columns)

            # Update string to only update if values are different
            update_str = ', '.join([
                f"{col} = CASE WHEN new.{col} <> {table_name}.{col} OR (new.{col} IS NULL AND {table_name}.{col} IS NOT NULL) OR (new.{col} IS NOT NULL AND {table_name}.{col} IS NULL) THEN new.{col} ELSE {table_name}.{col} END"
                for col in batch.columns if col not in [key_column]
            ])

            insert_query = f"""
            INSERT INTO {table_name} ({columns_str})
            VALUES ({', '.join(['%s' for _ in batch.columns])}) AS new
            ON DUPLICATE KEY UPDATE
            {update_str}
            """

            # Prepare values list for batch insert
            values = []
            for _, row in batch.iterrows():
                row_values = [prepare_value(row[col], col, table_name) for col in batch.columns]
                values.append(tuple(row_values))

            # Execute the query with values
            cursor.executemany(insert_query, values)
            total_processed += len(batch)
            print(f" | Batch {end} done", end="", flush=True)

        conn.commit()
        print(f" | ✓ Database update completed! [{total_processed} records]", flush=True)

    except (mysql.connector.Error, OSError, ConnectionError) as e:
        error_msg = f"MySQL network error during batch update of {table_name}: {e}"
        print(f" | ✗ {error_msg}", flush=True)
        current_app.logger.error(error_msg)
        if conn:
            try:
                conn.rollback()
            except Exception:
                pass  # Ignore rollback errors
        raise RuntimeError(error_msg) from e

    except Exception as e:
        error_msg = f"Unexpected error during batch update of {table_name}: {e}"
        print(f" | ✗ {error_msg}", flush=True)
        current_app.logger.error(error_msg)
        if conn:
            try:
                conn.rollback()
            except Exception:
                pass  # Ignore rollback errors
        raise RuntimeError(error_msg) from e

    finally:
        # Clean up cursor
        if cursor:
            try:
                cursor.close()
            except Exception:
                pass  # Ignore cursor close errors

        # Close connection only if we created it
        if own_connection and conn:
            try:
                conn.close()
            except Exception:
                pass  # Ignore connection close errors

def prepare_value(value, column_name, table_name):
    if pd.isna(value):
        if column_name == 'images':
            print(f"\033[91m!!! Image is None\033[0m")
        return None
    elif isinstance(value, pd.Timestamp):
        return value.to_pydatetime()
    elif isinstance(value, dict) and column_name == 'images':
        jsonstr =  json.dumps(value)
        if len(jsonstr) < 50 and "check" not in table_name:
            print(f"!!! \033[91m{jsonstr} is too short: {len(jsonstr)}\033[0m")
        compressed = zlib.compress(jsonstr.encode('utf-8')) # The .encode('utf-8') part is crucial; zlib works on bytes, not strings. 
        return base64.b64encode(compressed).decode('utf-8') #  Base64 converts binary data into a text format that can be safely stored in databases.  The final .decode('utf-8') converts the Base64 encoded bytes back into a string.
    elif isinstance(value, dict):
        return json.dumps(value)
    elif isinstance(value, str) and column_name == 'images' and not is_compressed(value):
        if len(value) < 50 and "check" not in table_name:
            print(f"!!! \033[91m{value} is too short: {len(value)}\033[0m")
        compressed = zlib.compress(value.encode('utf-8'))
        return base64.b64encode(compressed).decode('utf-8')
    elif 'int' in str(type(value)):  # Handles numpy.int64, numpy.int32, etc.
        return int(value)
    return value

def is_compressed(data):
    try:
        # Try to decompress and decode
        zlib.decompress(base64.b64decode(data.encode('utf-8')))
        return True
    except (zlib.error, base64.binascii.Error, TypeError):
        return False




def safe_transaction(bind='maidalv_db'):
    """
    A decorator to handle database transactions safely using the Flask application context.
    It begins a transaction, commits on success, and rolls back on any exception.
    The connection is stored in `g.db_conn`.
    !!! This is not compatible with streaming functions. Not sure why, but this is what the AI said.
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            engine = db.get_engine(bind=bind)
            with engine.connect() as connection:
                trans = connection.begin()
                try:
                    g.db_conn = connection  # Store connection in Flask's g
                    result = func(*args, **kwargs)
                    trans.commit()
                    return result
                except Exception as e:
                    current_app.logger.error(f"Transaction failed, rolling back. Error: {e}")
                    trans.rollback()
                    raise e
                finally:
                    if 'db_conn' in g:
                        del g.db_conn  # Clean up to avoid leaving stale connections
        return wrapper
    return decorator


def safe_transaction_new(bind='maidalv_db'):
    """
    A decorator to handle database transactions safely using the Flask application context.
    It begins a transaction, commits on success, and rolls back on any exception.
    The connection is stored in `g.db_conn`.
    !!! This is not compatible with streaming functions. Not sure why, but this is what the AI said.
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            engine = db.get_engine(bind=bind)
            connection = None
            trans = None
            try:
                connection = engine.connect()

                # Set statement timeout for the entire transaction (30 seconds)
                connection.execute(text("SET statement_timeout = '30000'"))
                print(f"✅ Statement timeout set to 30 seconds for {bind}")

                trans = connection.begin()

                # Check connection health before proceeding
                try:
                    connection.execute(text("SELECT 1"))
                    print(f"✅ Connection health check passed for {bind}")
                except Exception as health_e:
                    print(f"⚠️ Connection health check failed: {health_e}")
                    raise RuntimeError(f"Database connection is unhealthy: {health_e}")

                g.db_conn = connection  # Store connection in Flask's g
                result = func(*args, **kwargs)
                trans.commit()
                print(f"✅ Transaction committed successfully for {bind}")
                return result
            except Exception as e:
                error_msg = f"Transaction failed for {bind}, rolling back. Error: {e}"
                current_app.logger.error(error_msg)
                print(f"❌ {error_msg}")

                # Check if it's a deadlock or timeout
                if "deadlock detected" in str(e).lower():
                    print("🔄 Deadlock detected - transaction will be retried by PostgreSQL")
                elif "statement timeout" in str(e).lower():
                    print("⏰ Statement timeout occurred")

                if trans:
                    try:
                        trans.rollback()
                        print(f"✅ Transaction rolled back successfully for {bind}")
                    except Exception as rollback_e:
                        print(f"⚠️ Error during rollback: {rollback_e}")
                raise e
            finally:
                if 'db_conn' in g:
                    del g.db_conn  # Clean up to avoid leaving stale connections
                if connection:
                    try:
                        connection.close()
                        print(f"✅ Connection closed for {bind}")
                    except Exception as close_e:
                        print(f"⚠️ Error closing connection: {close_e}")
        return wrapper
    return decorator

def cleanup_stuck_transactions(bind='maidalv_db', max_age_minutes=30):
    """
    Clean up stuck/long-running transactions that might be causing deadlocks.
    This function should be called periodically to maintain database health.
    """
    print(f"🧹 Starting cleanup of stuck transactions (older than {max_age_minutes} minutes)")

    try:
        engine = db.get_engine(bind=bind)
        with engine.connect() as conn:
            # Find and terminate stuck transactions
            result = conn.execute(text(f"""
                SELECT
                    pid,
                    usename,
                    application_name,
                    now() - xact_start as duration,
                    query
                FROM pg_stat_activity
                WHERE state = 'active'
                AND now() - xact_start > interval '{max_age_minutes} minutes'
                AND query NOT LIKE '%pg_stat_activity%'
                ORDER BY duration DESC
            """))

            stuck_transactions = result.fetchall()

            if not stuck_transactions:
                print("✅ No stuck transactions found")
                return True

            print(f"⚠️ Found {len(stuck_transactions)} stuck transactions:")

            for row in stuck_transactions:
                pid, user, app, duration, query = row
                print(f"   PID: {pid}, User: {user}, Duration: {duration}")
                print(f"   Query: {query[:100]}...")

                # Terminate the stuck transaction
                try:
                    conn.execute(text(f"SELECT pg_terminate_backend({pid})"))
                    print(f"✅ Terminated stuck transaction PID {pid}")
                except Exception as term_e:
                    print(f"❌ Failed to terminate PID {pid}: {term_e}")

            return True

    except Exception as e:
        print(f"❌ Error during cleanup: {e}")
        return False

def get_database_health_status(bind='maidalv_db'):
    """
    Get comprehensive database health status including:
    - Connection status
    - Long-running transactions
    - Lock conflicts
    - Table sizes
    """
    print("🏥 Checking database health status...")

    health_status = {
        'connection': False,
        'long_transactions': [],
        'locks': [],
        'table_sizes': {},
        'timestamp': None
    }

    try:
        engine = db.get_engine(bind=bind)
        with engine.connect() as conn:
            health_status['connection'] = True
            health_status['timestamp'] = time.time()

            # Check for long-running transactions
            result = conn.execute(text("""
                SELECT
                    pid,
                    usename,
                    application_name,
                    now() - xact_start as duration,
                    query
                FROM pg_stat_activity
                WHERE state = 'active'
                AND now() - xact_start > interval '30 seconds'
                ORDER BY duration DESC
            """))
            health_status['long_transactions'] = [dict(row._mapping) for row in result]

            # Check for locks (simplified version to avoid JOIN issues)
            try:
                result = conn.execute(text("""
                    SELECT
                        l.pid,
                        a.usename,
                        a.query
                    FROM pg_locks l
                    JOIN pg_stat_activity a ON a.pid = l.pid
                    WHERE l.granted = false
                """))
            except Exception:
                # Fallback if the query fails
                result = conn.execute(text("SELECT pid FROM pg_locks WHERE granted = false LIMIT 5"))
            health_status['locks'] = [dict(row._mapping) for row in result]

            # Get table sizes
            for table in ['copyrights', 'copyrights_files']:
                try:
                    result = conn.execute(text(f"SELECT COUNT(*) as count FROM {table}"))
                    count = result.fetchone()[0]
                    health_status['table_sizes'][table] = count
                except Exception as e:
                    health_status['table_sizes'][table] = f"Error: {e}"

        # Print summary
        print(f"✅ Connection: {'OK' if health_status['connection'] else 'FAILED'}")
        print(f"⚠️ Long transactions: {len(health_status['long_transactions'])}")
        print(f"🔒 Lock conflicts: {len(health_status['locks'])}")
        print(f"📊 Table sizes: {health_status['table_sizes']}")

        return health_status

    except Exception as e:
        print(f"❌ Health check failed: {e}")
        health_status['error'] = str(e)
        return health_status