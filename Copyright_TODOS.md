1. We should never need to download from COS. We should have all the files locally on the server!
2. The qdrant point id is based on the filename before the migration. So now it is based on nothing. In the future we should base it on the registration number (like trademark)
   1. => the copyright qdrant databaset should be recreated post migration
3. When doing the case scaping we should:
   1. Filename should be {reg_no}_{method}.webp
   2. Save the files in the IP/copyrights folder same like the trademarks that are expired. But all in 1 single folder
   3. Add the record in the copyrights table (same like the trademarks that are expired)
4. Until we have the point_id properly configure, the production flag cannot be tested (as it checks existance and add or remove the vector from the vectorstore)
