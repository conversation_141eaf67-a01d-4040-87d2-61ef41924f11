
import os
import cv2

THUMBNAIL_MAX_SIZE = 40 * 1024  # 40 KB
HIGH_RES_MAX_SIZE = 300 * 1024  # 300 KB
THUMBNAIL_WIDTH = 300
HIGH_RES_WIDTH = 1600

def create_resized_image(images_directory, image_filename, output_path):
    try:
        image_path = os.path.join(images_directory, image_filename)
        img = cv2.imread(image_path, cv2.IMREAD_UNCHANGED)
        
        if img is None:
            raise ValueError(f"Failed to read image: {image_path}")

        low_path = os.path.join(output_path, "images", "low", os.path.splitext(image_filename)[0] + '.webp')
        high_path = os.path.join(output_path, "images", "high", os.path.splitext(image_filename)[0] + '.webp')
        
        os.makedirs(os.path.dirname(low_path), exist_ok=True)
        os.makedirs(os.path.dirname(high_path), exist_ok=True)
        
        process_image(img, THUMBNAIL_WIDTH, THUMBNAIL_MAX_SIZE, low_path)
        process_image(img, HIGH_RES_WIDTH, HIGH_RES_MAX_SIZE, high_path)

        return os.path.splitext(image_filename)[0] + '.webp'
    except Exception as e:
        print(f"Failed to process image {image_path}: {e}")
        return None

def process_image(img, target_width, max_size, output_path):
    height, width = img.shape[:2]
    if width > target_width:
        scale = target_width / width
        new_height = int(height * scale)
        img = cv2.resize(img, (target_width, new_height), interpolation=cv2.INTER_AREA)

    for quality in range(95, 0, -10):
        encode_param = [int(cv2.IMWRITE_WEBP_QUALITY), quality]
        result, encoded_img = cv2.imencode('.webp', img, encode_param)
        
        if not result:
            continue

        size = len(encoded_img)
        if size <= max_size or quality <= 25:
            output_path = os.path.splitext(output_path)[0] + '.webp'
            with open(output_path, 'wb') as f:
                f.write(encoded_img)
            break