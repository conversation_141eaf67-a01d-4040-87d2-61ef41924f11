# backend/run_task.py
### This file is for manually running the embedding and comparison tasks
import sys
import os
sys.path.append(os.getcwd())
import logging

# Configure logging early
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

try:
    logger.info("Importing necessary modules...")
    from backend import create_app
    from backend.tasks.feature_computation import compute_features_task
    from backend.tasks.comparison_execution import comparison_task
    # db might not be explicitly needed here if ContextTask handles it,
    # but importing ensures it's known.
    from backend.extensions import db
    logger.info("Modules imported successfully.")

    logger.info("Creating Flask app...")
    app = create_app()
    logger.info("Flask app created.")

    logger.info("Pushing application context...")
    with app.app_context():
        logger.info("Application context pushed.")
        logger.info("Running compute_features_task('copyright')...")
        try:
            result = compute_features_task("copyright")  # compute_features_task will call comparison_task at the end of it
            # result = comparison_task("copyright")  # will only run comparison_task
            logger.info("Task completed successfully.")
            logger.info(f"Result: {result}") # Optional: print result if any
        except Exception as e:
            logger.error(f"Error running task: {e}", exc_info=True) # Log full traceback
    logger.info("Application context popped.")

except ImportError as e:
    logger.error(f"ImportError occurred: {e}", exc_info=True)
    logger.error("Please ensure the script is run from the project root directory (ModelTestsWorkbench) or that the backend package is correctly installed/discoverable.")
except Exception as e:
    logger.error(f"An unexpected error occurred during setup or execution: {e}", exc_info=True)