import React, { useState, useEffect, useCallback } from 'react';
import {
    Paper, Grid, TextField, Select, MenuItem, FormControl,
    InputLabel, Checkbox, ListItemText, Button
} from '@mui/material';
import { DayPicker } from 'react-day-picker';
import debounce from 'lodash.debounce';
import 'react-day-picker/style.css';

// Sample options - you should replace with real values
const TRO_STATUS_OPTIONS = ['All', 'True', 'False'];
const MARK_STANDARD_CHARACTER_OPTIONS = ['All', 'Yes', 'No'];
const INT_CLASS_OPTIONS = ['1', '2', '3', '4'];
const COUNTRY_CODE_OPTIONS = ['US', 'EU', 'JP', 'CN'];

function TrademarkFilterControls({
    current_filters,
    on_filter_change,
    available_columns,
    on_column_selection_change
}) {
    const [localFilters, setLocalFilters] = useState(current_filters);

    useEffect(() => {
        setLocalFilters(current_filters);
    }, [current_filters]);

    const debouncedFilterChange = useCallback(
        debounce((updated) => {
            on_filter_change(updated);
        }, 400),
        [on_filter_change]
    );

    const handleInputChange = (field, value) => {
        const updated = { ...localFilters, [field]: value };
        setLocalFilters(updated);
        debouncedFilterChange({ [field]: value });
    };

    const handleDateRangeChange = (range) => {
        const from = range?.from || null;
        const to = range?.to || null;

        const updated = {
            filing_date_range: { from, to },
            filing_date_start: from,
            filing_date_end: to,
        };
        setLocalFilters((prev) => ({ ...prev, ...updated }));
        on_filter_change(updated);
    };

    const handleColumnSelectChange = (event) => {
        const {
            target: { value }
        } = event;
        const selected = typeof value === 'string' ? value.split(',') : value;

        const alwaysVisible = available_columns
            .filter((col) => col.alwaysVisible)
            .map((col) => col.field);

        const newSelection = Array.from(new Set([...alwaysVisible, ...selected]));
        on_column_selection_change(newSelection);
    };

    const handleResetFilters = () => {
        const reset = Object.fromEntries(
            Object.entries(current_filters).map(([key, value]) => {
                if (key === 'filing_date_range') return [key, { from: null, to: null }];
                if (Array.isArray(value)) return [key, []];
                return [key, ''];
            })
        );
        setLocalFilters(reset);
        on_filter_change(reset);
    };

    const formatDate = (date) => (date ? date.toLocaleDateString() : '');

    const { from, to } = localFilters.filing_date_range || {};
    let footer = <p>Select a date range.</p>;
    if (from && !to) footer = <p>Start: {formatDate(from)}</p>;
    else if (from && to) footer = <p>{formatDate(from)} – {formatDate(to)}</p>;

    return (
        <Paper sx={{ p: 2, mb: 2 }} elevation={2}>
            <Grid container spacing={2}>

                {/* Dynamically render filters */}
                {Object.entries(localFilters).map(([field, value]) => {
                    if (field === 'selected_columns' || field === 'page' || field === 'per_page' || field === 'sort_by' || field === 'sort_dir') {
                        return null; // skip non-filter controls
                    }

                    // Filing Date Range
                    if (field === 'filing_date_range') {
                        return (
                            <Grid item xs={12} sm={12} md={4} key={field}>
                                <InputLabel shrink sx={{ mb: 1 }}>Filing Date Range</InputLabel>
                                <DayPicker
                                    mode="range"
                                    selected={value}
                                    onSelect={handleDateRangeChange}
                                    footer={footer}
                                    showOutsideDays
                                    fixedWeeks
                                    captionLayout="dropdown-buttons"
                                    fromYear={1900}
                                    toYear={new Date().getFullYear() + 5}
                                />
                            </Grid>
                        );
                    }

                    // Dropdown for TRO Status
                    if (field === 'tro') {
                        return (
                            <Grid item xs={12} sm={6} md={3} key={field}>
                                <FormControl fullWidth size="small">
                                    <InputLabel>TRO Status</InputLabel>
                                    <Select
                                        value={value || 'All'}
                                        onChange={(e) => handleInputChange(field, e.target.value)}
                                        label="TRO Status"
                                    >
                                        {TRO_STATUS_OPTIONS.map((opt) => (
                                            <MenuItem key={opt} value={opt}>{opt}</MenuItem>
                                        ))}
                                    </Select>
                                </FormControl>
                            </Grid>
                        );
                    }

                    // Dropdown for Standard Character
                    if (field === 'mark_standard_character_indicator') {
                        return (
                            <Grid item xs={12} sm={6} md={3} key={field}>
                                <FormControl fullWidth size="small">
                                    <InputLabel>Standard Character?</InputLabel>
                                    <Select
                                        value={value || 'All'}
                                        onChange={(e) => handleInputChange(field, e.target.value)}
                                        label="Standard Character?"
                                    >
                                        {MARK_STANDARD_CHARACTER_OPTIONS.map((opt) => (
                                            <MenuItem key={opt} value={opt}>{opt}</MenuItem>
                                        ))}
                                    </Select>
                                </FormControl>
                            </Grid>
                        );
                    }

                    // Multi-select: int_cls
                    if (field === 'int_cls') {
                        return (
                            <Grid item xs={12} sm={6} md={3} key={field}>
                                <FormControl fullWidth size="small">
                                    <InputLabel>Int. Class</InputLabel>
                                    <Select
                                        multiple
                                        value={value}
                                        onChange={(e) => handleInputChange(field, e.target.value)}
                                        renderValue={(selected) => selected.join(', ')}
                                    >
                                        {INT_CLASS_OPTIONS.map((item) => (
                                            <MenuItem key={item} value={item}>
                                                <Checkbox checked={value.includes(item)} />
                                                <ListItemText primary={item} />
                                            </MenuItem>
                                        ))}
                                    </Select>
                                </FormControl>
                            </Grid>
                        );
                    }

                    // Multi-select: country_codes
                    if (field === 'country_codes') {
                        return (
                            <Grid item xs={12} sm={6} md={3} key={field}>
                                <FormControl fullWidth size="small">
                                    <InputLabel>Country Codes</InputLabel>
                                    <Select
                                        multiple
                                        value={value}
                                        onChange={(e) => handleInputChange(field, e.target.value)}
                                        renderValue={(selected) => selected.join(', ')}
                                    >
                                        {COUNTRY_CODE_OPTIONS.map((item) => (
                                            <MenuItem key={item} value={item}>
                                                <Checkbox checked={value.includes(item)} />
                                                <ListItemText primary={item} />
                                            </MenuItem>
                                        ))}
                                    </Select>
                                </FormControl>
                            </Grid>
                        );
                    }

                    // Default to TextField
                    return (
                        <Grid item xs={12} sm={6} md={3} key={field}>
                            <TextField
                                label={field.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                                fullWidth
                                size="small"
                                value={value || ''}
                                onChange={(e) => handleInputChange(field, e.target.value)}
                            />
                        </Grid>
                    );
                })}

                {/* Column Selection */}
                {current_filters.selected_columns && (
                    <Grid item xs={12} sm={6} md={4}>
                        <FormControl fullWidth size="small">
                            <InputLabel>Select Columns</InputLabel>
                            <Select
                                multiple
                                value={current_filters.selected_columns}
                                onChange={handleColumnSelectChange}
                                label="Select Columns"
                                renderValue={(selected) =>
                                    selected
                                        .map(
                                            (field) =>
                                                available_columns.find((col) => col.field === field)?.headerName || field
                                        )
                                        .join(', ')
                                }
                            >
                                {available_columns.map((column) => (
                                    <MenuItem
                                        key={column.field}
                                        value={column.field}
                                        disabled={column.alwaysVisible}
                                    >
                                        <Checkbox
                                            checked={current_filters.selected_columns.includes(column.field)}
                                            disabled={column.alwaysVisible}
                                        />
                                        <ListItemText primary={column.headerName} />
                                    </MenuItem>
                                ))}
                            </Select>
                        </FormControl>
                    </Grid>
                )}

                {/* Reset Button */}
                <Grid item xs={12} sm={6} md={2}>
                    <Button fullWidth variant="outlined" onClick={handleResetFilters}>
                        Reset Filters
                    </Button>
                </Grid>
            </Grid>
        </Paper>
    );
}

export default TrademarkFilterControls;
