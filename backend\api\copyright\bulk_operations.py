"""
Bulk operations for copyright assets
"""
from flask import request, jsonify, current_app, Response, g
from sqlalchemy import text
import json
import time

import uuid
from backend.utils.vector_store import get_qdrant_client, delete_vector
from backend.api.copyright.helpers import QDRANT_COLLECTION_NAME

from backend.extensions import db
from backend.utils.db_utils import safe_transaction


def delete_copyright_files(copyright_files: list, conn):
    """
    Deletes copyright files from the database and Qdrant if they are in production.
    Also handles orphaned copyright records.

    Args:
        copyright_files (list): A list of copyright file objects (named tuples or similar),
                                each with id, registration_number, filename, and production attributes.
        conn: The database connection object.
    """
    if not copyright_files:
        return

    qdrant_client = get_qdrant_client()
    file_ids = [file.id for file in copyright_files]
    
    # Handle Qdrant deletion for production files
    for file in copyright_files:
        if file.production and qdrant_client:
            try:
                point_id = str(uuid.uuid5(uuid.NAMESPACE_DNS, file.filename))
                delete_vector(qdrant_client, QDRANT_COLLECTION_NAME, point_id)
            except Exception as e:
                current_app.logger.error(f"Error deleting vector for {file.filename}: {e}")

    # Delete all files from the database in one go
    conn.execute(
        text("DELETE FROM copyrights_files WHERE id = ANY(ARRAY[:ids]::uuid[])"),
        {"ids": file_ids}
    )

    # Identify registration numbers to check for orphans
    reg_nos_to_check = list(set(file.registration_number for file in copyright_files if file.registration_number and file.registration_number.startswith('MD')))

    # Delete orphaned copyright records in a single operation
    if reg_nos_to_check:
        conn.execute(
            text("""
                DELETE FROM copyrights
                WHERE registration_number = ANY(:reg_nos)
                AND registration_number NOT IN (
                    SELECT DISTINCT registration_number FROM copyrights_files
                    WHERE registration_number = ANY(:reg_nos)
                )
            """),
            {"reg_nos": reg_nos_to_check}
        )


@safe_transaction(bind='maidalv_db')
def bulk_update_copyrights_files():
    """Bulk actions for copyrights_files: set prod and delete."""
    try:
        data = request.get_json()
        op = data.get('op')
        ids = data.get('ids', [])
        
        if not op or not ids:
            return jsonify({"success": False, "error": "Missing operation or IDs"}), 400
        
        conn = g.db_conn
        if op == 'set_type':
            value = data.get('value')
            if not value:
                return jsonify({"success": False, "error": "Type value required"}), 400
            conn.execute(
                text("UPDATE copyrights_files SET type = :value WHERE id = ANY(ARRAY[:ids]::uuid[])"),
                {"value": value, "ids": ids}
            )
        elif op == 'delete':
            # Fetch all necessary file details in one query
            files_to_delete_query = text("SELECT id, registration_number, filename, production FROM copyrights_files WHERE id = ANY(ARRAY[:ids]::uuid[])")
            copyright_files_to_delete = conn.execute(files_to_delete_query, {"ids": ids}).fetchall()

            # Call the helper function with the fetched data
            delete_copyright_files(copyright_files_to_delete, conn)
        else:
            return jsonify({"success": False, "error": "Invalid operation"}), 400
        
        return jsonify({"success": True, "data": {"updated_count": len(ids)}})
        
    except Exception as e:
        current_app.logger.error(f"Error in bulk update copyrights files: {e}")
        raise e


def bulk_set_prod_progress_generator(asset_ids: list, production_value: bool):
    """
    Generator function that yields SSE progress data for bulk set_prod operations.
    """
    import logging
    logger = logging.getLogger(__name__)
    
    try:
        engine = db.get_engine(bind='maidalv_db')
        total_assets = len(asset_ids)
        processed = 0
        successful = 0
        failed = 0
        
        action = "setting" if production_value else "unsetting"

        # Send initial progress
        yield f"data: {json.dumps({'type': 'progress', 'current': 0, 'total': total_assets, 'successful': 0, 'failed': 0, 'action': action})}\n\n"

        # Fetch all asset details in a single batch
        with engine.connect() as conn:
            assets_results = conn.execute(
                text("SELECT id, registration_number, filename FROM copyrights_files WHERE id = ANY(ARRAY[:ids]::uuid[])"),
                {"ids": asset_ids}
            ).fetchall()
            assets_map = {asset.id: asset for asset in assets_results}

        from backend.api.copyright.handle_prod import process_copyright_production

        for asset_id in asset_ids:
            try:
                result = assets_map.get(uuid.UUID(asset_id))

                if not result:
                    failed += 1
                    processed += 1
                    yield f"data: {json.dumps({'type': 'error', 'asset_id': asset_id, 'message': 'Asset not found'})}\n\n"
                    yield f"data: {json.dumps({'type': 'progress', 'current': processed, 'total': total_assets, 'successful': successful, 'failed': failed})}\n\n"
                    continue

                file_id_db, reg_no, filename = result

                # Process the asset using the synchronous function
                success, message, _ = process_copyright_production(file_id_db, reg_no, production_value, filename)

                if success:
                    successful += 1
                    yield f"data: {json.dumps({'type': 'success', 'asset_id': asset_id, 'reg_no': reg_no, 'message': message})}\n\n"
                else:
                    failed += 1
                    yield f"data: {json.dumps({'type': 'error', 'asset_id': asset_id, 'reg_no': reg_no, 'message': message})}\n\n"

                processed += 1

                # Send progress update
                yield f"data: {json.dumps({'type': 'progress', 'current': processed, 'total': total_assets, 'successful': successful, 'failed': failed})}\n\n"

                # Small delay to prevent overwhelming the client
                time.sleep(0.1)

            except Exception as e:
                failed += 1
                processed += 1
                logger.error(f"Error processing asset {asset_id}: {e}")
                yield f"data: {json.dumps({'type': 'error', 'asset_id': asset_id, 'message': str(e)})}\n\n"
                yield f"data: {json.dumps({'type': 'progress', 'current': processed, 'total': total_assets, 'successful': successful, 'failed': failed})}\n\n"

        # Send completion message
        action_past = "set" if production_value else "unset"
        yield f"data: {json.dumps({'type': 'complete', 'total': total_assets, 'successful': successful, 'failed': failed, 'action': action_past})}\n\n"

    except Exception as e:
        action = "setting" if production_value else "unsetting"
        logger.error(f"Error in bulk production {action}: {e}")
        yield f"data: {json.dumps({'type': 'error', 'message': f'Bulk operation failed: {str(e)}'})}\n\n"