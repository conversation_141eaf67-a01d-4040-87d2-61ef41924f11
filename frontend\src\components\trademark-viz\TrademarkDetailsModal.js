import React from 'react';
import {
    <PERSON><PERSON>,
    <PERSON>alog<PERSON><PERSON>le,
    DialogContent,
    DialogActions,
    Button,
    CircularProgress,
    Alert,
    Typography,
    Grid,
    Box,
    Paper,
    Card,
    CardMedia,
} from '@mui/material';

const formatLabel = (key) => {
    return key
        .replace(/_/g, ' ')
        .replace(/\b\w/g, (char) => char.toUpperCase()); // Capitalize each word
};

const renderValue = (value) => {
    if (value === null || value === undefined || value === '') {
        return <Typography variant="body2" color="text.secondary">N/A</Typography>;
    }
    if (Array.isArray(value)) {
        return value.length > 0 ? value.join(', ') : <Typography variant="body2" color="text.secondary">N/A</Typography>;
    }
    if (typeof value === 'boolean') {
        return value ? 'Yes' : 'No';
    }
    if (typeof value === 'object') {
        return <pre>{JSON.stringify(value, null, 2)}</pre>;
    }
    return String(value);
};

const DetailItem = ({ label, value }) => (
    <Grid item xs={12} sm={6} md={4}>
        <Paper elevation={0} sx={{ p: 1.5, border: '1px solid #eee', height: '100%' }}>
            <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                {formatLabel(label)}
            </Typography>
            <Typography variant="body2" component="div" sx={{ wordBreak: 'break-word' }}>
                {renderValue(value)}
            </Typography>
        </Paper>
    </Grid>
);

const SectionTitle = ({ children }) => (
    <Typography variant="h6" component="h3" sx={{ mt: 3, mb: 2, borderBottom: '1px solid #ddd', pb: 1 }}>
        {children}
    </Typography>
);

function TrademarkDetailModal({ is_open, on_close, trademark_data, is_loading, error }) {
    const generalInfoFields = [
        'reg_no',
        'ser_no',
        'applicant_name',
        'int_cls',
        'plaintiff_id',
        'nb_suits',
        'tro',
        'filing_date',
    ];

    const certificateImages = trademark_data?.certificate?.image_files || [];

    return (
        <Dialog open={is_open} onClose={on_close} fullWidth maxWidth="lg" scroll="paper">
            <DialogTitle sx={{ borderBottom: '1px solid #ccc' }}>
                Trademark Details: {trademark_data ? trademark_data.mark_text : 'Loading...'}
            </DialogTitle>
            <DialogContent dividers>
                {is_loading && (
                    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 200 }}>
                        <CircularProgress />
                    </Box>
                )}

                {error && !is_loading && (
                    <Alert severity="error" sx={{ mt: 2 }}>{error}</Alert>
                )}

                {!is_loading && !error && trademark_data && (
                    <Box>
                        {/* General Info Section */}
                        <Box>
                            <SectionTitle>General Information</SectionTitle>
                            <Grid container spacing={3}>
                                {generalInfoFields.map((key) =>
                                    trademark_data.hasOwnProperty(key) ? (
                                        <DetailItem key={key} label={key} value={trademark_data[key]} />
                                    ) : null
                                )}
                            </Grid>
                        </Box>

                        {/* Mark Details Section */}
                        <Box sx={{ mt: 4 }}>
                            <SectionTitle>Mark Details</SectionTitle>
                            <Grid container spacing={3}>
                                {['mark_text', 'mark_feature_code', 'mark_current_status_code'].map((key) =>
                                    trademark_data.hasOwnProperty(key) ? (
                                        <DetailItem key={key} label={key} value={trademark_data[key]} />
                                    ) : null
                                )}
                            </Grid>
                        </Box>

                        {/* Certificate Images Section */}
                        {trademark_data.certificate && (
                            <Box sx={{ mt: 4 }}>
                                <SectionTitle>Certificate Details</SectionTitle>
                                {Array.isArray(certificateImages) && certificateImages.length > 0 ? (
                                    <Grid container spacing={2} sx={{ p: 1 }}>
                                        {certificateImages.map((image_path, index) => (
                                            <Grid item xs={12} sm={6} md={4} lg={3} key={index}>
                                                <Card raised sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                                                    <CardMedia
                                                        component="img"
                                                        image={image_path}
                                                        alt={`Certificate Image ${index + 1}`}
                                                        sx={{
                                                            objectFit: 'contain',
                                                            maxHeight: 300,
                                                            p: 0.5
                                                        }}
                                                    />
                                                </Card>
                                            </Grid>
                                        ))}
                                    </Grid>
                                ) : (
                                    <Alert severity="info">No certificate images found.</Alert>
                                )}
                            </Box>
                        )}
                    </Box>
                )}

                {!is_loading && !error && !trademark_data && (
                    <Alert severity="info" sx={{ mt: 2 }}>No trademark data to display.</Alert>
                )}
            </DialogContent>
            <DialogActions sx={{ borderTop: '1px solid #ccc', p: '16px 24px' }}>
                <Button onClick={on_close} variant="outlined">
                    Close
                </Button>
            </DialogActions>
        </Dialog>
    );
}

export default TrademarkDetailModal;