from backend.extensions import db
from sqlalchemy.exc import SQLAlchemyError
import uuid

def create_record(model, **kwargs):
    """
    Creates a new record for a given model.
    """
    try:
        new_record = model(**kwargs)
        db.session.add(new_record)
        db.session.commit()
        return new_record
    except SQLAlchemyError as e:
        db.session.rollback()
        raise e

def get_record_by_id(model, record_id):
    """
    Retrieves a single record by its primary key.
    """
    return db.session.get(model, record_id)

def get_all_records(model, page=1, per_page=20, **filters):
    """
    Retrieves all records for a given model with optional filtering and pagination.
    """
    query = model.query
    
    if filters:
        # Filter based on provided keyword arguments
        for key, value in filters.items():
            if hasattr(model, key):
                query = query.filter(getattr(model, key) == value)

    if page and per_page:
        pagination = query.paginate(page=page, per_page=per_page, error_out=False)
        return pagination.items, pagination.total
    
    return query.all(), query.count()


def update_record(model, record_id, **kwargs):
    """
    Updates a record for a given model.
    """
    record = get_record_by_id(model, record_id)
    if not record:
        return None
    
    try:
        for key, value in kwargs.items():
            if hasattr(record, key):
                setattr(record, key, value)
        db.session.commit()
        return record
    except SQLAlchemyError as e:
        db.session.rollback()
        raise e

def delete_record(model, record_id):
    """
    Deletes a record for a given model.
    """
    record = get_record_by_id(model, record_id)
    if not record:
        return False
        
    try:
        db.session.delete(record)
        db.session.commit()
        return True
    except SQLAlchemyError as e:
        db.session.rollback()
        raise e
