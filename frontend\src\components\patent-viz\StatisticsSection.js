import React from 'react';
import { Grid, Paper, Typography } from '@mui/material';
import SingleStatisticDisplay from './SingleStatisticDisplay'; // Will be created
import PieChartDisplay from './PieChartDisplay'; // Will be created
import MissingDataDisplay from './MissingDataDisplay'; // Will be created

// Helper function (can be moved to a utils file)
const formatPercentage = (number) => {
  if (number === null || number === undefined || isNaN(Number(number))) {
    return 'N/A';
  }
  return `${Number(number).toFixed(2)}%`;
};

// TDD: TEST: StatisticsSection renders title correctly (Title is now rendered by parent)
// TDD: TEST: StatisticsSection renders SingleStatisticDisplay for total_records
// TDD: TEST: StatisticsSection renders PieChartDisplay for patent_type_distribution
// TDD: TEST: StatisticsSection renders PieChartDisplay for each classification type
// TDD: TEST: StatisticsSection renders MissingDataDisplay for missing_data_percentages
const StatisticsSection = ({ statsData }) => {
  if (!statsData) {
    return (
      <Paper elevation={2} sx={{ p: 2, mb: 3, textAlign: 'center' }}>
        <Typography variant="body1">No statistics data available for this section.</Typography>
      </Paper>
    );
  }

  return (
    <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
      <Grid container spacing={3}>
        {/* Top stats row */}
        <Grid item container xs={12} spacing={2}>
          {/* Total Records */}
          <Grid item xs={12} sm={6} md={4}>
            <SingleStatisticDisplay
              label="Total Patent Records"
              value={statsData.total_records !== undefined ? statsData.total_records : 'N/A'}
            />
          </Grid>

          {/* TRO True Percentage (only if explicitly present) */}
          {statsData.tro_true_percentage !== undefined && (
            <Grid item xs={12} sm={6} md={4}>
              <SingleStatisticDisplay
                label="% TRO == True"
                value={formatPercentage(statsData.tro_true_percentage)}
              />
            </Grid>
          )}
        </Grid>

        {/* Patent Type Distribution */}
        {/* TDD: TEST: PieChartDisplay for patent_type_distribution receives correct data */}
        <Grid item xs={12} sx={{ mb: 4 }}>
          <PieChartDisplay
            title="Patent Type Distribution"
            data={statsData.patent_type_distribution}
          />
        </Grid>

        {/* Classification Stats - USPC */}
        <Grid item xs={12} sx={{ mb: 4 }}>
          <PieChartDisplay
            title="USPC Classification Distribution"
            data={statsData.classification_stats_uspc}
          />
        </Grid>

        {/* Classification Stats - LOC */}
        <Grid item xs={12} sx={{ mb: 4 }}>
          <PieChartDisplay
            title="LOC Classification Distribution"
            data={statsData.classification_stats_loc}
          />
        </Grid>

        {/* Classification Stats - CPC */}
        <Grid item xs={12} sx={{ mb: 4 }}>
          <PieChartDisplay
            title="CPC Classification Distribution"
            data={statsData.classification_stats_cpc}
          />
        </Grid>

        {/* Missing Data Percentages */}
        {/* TDD: TEST: MissingDataDisplay receives correct data */}
        <Grid item xs={12}>
          <MissingDataDisplay
            title="Percentage of Records with Missing Data"
            data={statsData.missing_data_percentages}
          />
        </Grid>
      </Grid>
    </Paper>
  );
};

export default StatisticsSection;