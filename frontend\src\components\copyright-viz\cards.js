import React from 'react';
import './cards.css';

// Default Card Component
export const DefaultCard = ({ item, isSelected, onToggleSelect, onImageClick }) => (
    <div
        className={`orphan-card ${isSelected ? 'selected' : ''}`}
        onClick={(e) => {
            if (e.target.tagName !== 'IMG') {
                onToggleSelect(item.id);
            }
        }}
    >
        <div className="card-image-container">
            {item.data.low_res_path ? (
                <img
                    src={item.data.low_res_path}
                    alt={item.data.filename || 'Orphan image'}
                    className="card-thumbnail"
                    onClick={() => onImageClick(item.data)}
                    onError={(e) => {
                        e.target.alt = 'Image not available';
                    }}
                />
            ) : (
                <div className="no-image">
                    <span>No Image</span>
                </div>
            )}
        </div>

        <div className="card-info">
            <div className="info-row">
                <label>ID:</label>
                <span>{item.id}</span>
            </div>
            <div className="info-row">
                <label>Filename:</label>
                <span>{item.data.filename || 'N/A'}</span>
            </div>
        </div>
    </div>
);

// ProdNoQdrant Card Component
export const ProdNoQdrantCard = ({ item, isSelected, onToggleSelect, onImageClick }) => (
    <div
        className={`orphan-card ${isSelected ? 'selected' : ''}`}
        onClick={(e) => {
            if (e.target.tagName !== 'IMG') {
                onToggleSelect(item.id);
            }
        }}
    >
        <div className="card-image-container">
            {item.data.low_res_path ? (
                <img
                    src={item.data.low_res_path}
                    alt={item.data.filename || 'Orphan image'}
                    className="card-thumbnail"
                    onClick={() => onImageClick(item.data)}
                    onError={(e) => {
                        e.target.alt = 'Image not available';
                    }}
                />
            ) : (
                <div className="no-image">
                    <span>No Image</span>
                </div>
            )}
        </div>

        <div className="card-info">
            <div className="info-row">
                <label>ID:</label>
                <span>{item.id}</span>
            </div>
            <div className="info-row">
                <label>Filename:</label>
                <span>{item.data.filename || 'N/A'}</span>
            </div>
            <div className="info-row">
                <label>Registration:</label>
                <span>{item.data.registration_number || 'N/A'}</span>
            </div>
            <div className="info-row">
                <label>Plaintiff ID:</label>
                <span>{item.data.plaintiff_id || 'N/A'}</span>
            </div>
            <div className="info-row">
                <label>Plaintiff Name:</label>
                <span>{item.data.plaintiff_name || 'N/A'}</span>
            </div>
            <div className="info-row">
                <label>Method:</label>
                <span>{item.data.method || 'N/A'}</span>
            </div>
            <div className="info-row">
                <label>Type:</label>
                <span>{item.data.type || 'N/A'}</span>
            </div>
            <div className="info-row">
                <label>Created:</label>
                <span>{item.data.create_time ? new Date(item.data.create_time).toLocaleDateString() : 'N/A'}</span>
            </div>
            <div className="info-row">
                <label>Updated:</label>
                <span>{item.data.update_time ? new Date(item.data.update_time).toLocaleDateString() : 'N/A'}</span>
            </div>
        </div>
    </div>
);

// QdrantOnly Card Component
export const QdrantOnlyCard = ({ item, isSelected, onToggleSelect, onImageClick, onSimilarImageClick }) => {
    // Comparison logic for plaintiff data mismatch
    const plaintiffIdMismatch = item.data.plaintiff_id !== item.data.db_plaintiff_id;
    const plaintiffNameMismatch = item.data.plaintiff_name !== item.data.db_plaintiff_name;
    const hasMismatch = plaintiffIdMismatch || plaintiffNameMismatch;

    const handleSimilarImageClick = (e) => {
        e.stopPropagation();
        if (onSimilarImageClick && item.data.similar_point) {
            onSimilarImageClick(item.data.similar_point);
        }
    };

    return (
        <div
            className={`qdrant-only-card ${isSelected ? 'selected' : ''} ${hasMismatch ? 'mismatch-highlight' : ''}`}
            onClick={(e) => {
                if (e.target.tagName !== 'IMG' && !e.target.classList.contains('similar-image-link')) {
                    onToggleSelect(item.id);
                }
            }}
        >
            <div className="card-image-container">
                {item.data.low_res_path ? (
                    <img
                        src={item.data.low_res_path}
                        alt={item.data.filename || 'Orphan image'}
                        className="card-thumbnail"
                        onClick={() => onImageClick(item.data)}
                        onError={(e) => {
                            e.target.alt = 'Image not available';
                        }}
                    />
                ) : (
                    <div className="no-image">
                        <span>No Image</span>
                    </div>
                )}
                {item.data.similar_point && (
                    <div className="similar-image-link-container">
                        <span
                            className="similar-image-link"
                            onClick={handleSimilarImageClick}
                            title="Click to view similar image"
                        >
                            Similar Image
                        </span>
                    </div>
                )}
            </div>

            <div className="card-info">
                <div className="info-row">
                    <label>Filename:</label>
                    <span>{item.data.filename || 'N/A'}</span>
                </div>
                <div className="info-row">
                    <label>Registration:</label>
                    <span>{item.data.registration_number || 'N/A'}</span>
                </div>
                <div className={`info-row ${plaintiffIdMismatch ? 'mismatch-field' : ''}`}>
                    <label>Plaintiff ID:</label>
                    <span>{item.data.plaintiff_id || 'N/A'}</span>
                </div>
                <div className={`info-row ${plaintiffNameMismatch ? 'mismatch-field' : ''}`}>
                    <label>Plaintiff Name:</label>
                    <span>{item.data.plaintiff_name || 'N/A'}</span>
                </div>
                <div className="info-row">
                    <label>Docket:</label>
                    <span>{item.data.docket || 'N/A'}</span>
                </div>
                <div className="info-section">
                    <h4>DB Details</h4>
                    <div className="info-row">
                        <label>DB Plaintiff ID:</label>
                        <span>{item.data.db_plaintiff_id || 'N/A'}</span>
                    </div>
                    <div className="info-row">
                        <label>DB Plaintiff Name:</label>
                        <span>{item.data.db_plaintiff_name || 'N/A'}</span>
                    </div>
                </div>
            </div>
        </div>
    );
};

// CourtOnly Card Component
export const CourtOnlyCard = ({ item, isSelected, onToggleSelect, onImageClick }) => (
    <div
        className={`orphan-card ${isSelected ? 'selected' : ''}`}
        onClick={(e) => {
            if (e.target.tagName !== 'IMG') {
                onToggleSelect(item.id);
            }
        }}
    >
        <div className="card-image-container">
            {item.data.low_res_path ? (
                <img
                    src={item.data.low_res_path}
                    alt={item.data.filename || 'Orphan image'}
                    className="card-thumbnail"
                    onClick={() => onImageClick(item.data)}
                    onError={(e) => {
                        e.target.alt = 'Image not available';
                    }}
                />
            ) : (
                <div className="no-image">
                    <span>No Image</span>
                </div>
            )}
        </div>

        <div className="card-info">
            <div className="info-row">
                <label>ID:</label>
                <span>{item.id}</span>
            </div>
            <div className="info-row">
                <label>Filename:</label>
                <span>{item.data.filename || 'N/A'}</span>
            </div>
            <div className="info-row">
                <label>Full Filename:</label>
                <span>{item.data.full_filename || 'N/A'}</span>
            </div>
            <div className="info-row">
                <label>Plaintiff ID:</label>
                <span>{item.data.plaintiff_id || 'N/A'}</span>
            </div>
            <div className="info-row">
                <label>Plaintiff Name:</label>
                <span>{item.data.plaintiff_name || 'N/A'}</span>
            </div>
            <div className="info-row">
                <label>Docket:</label>
                <span>{item.data.docket || 'N/A'}</span>
            </div>
            <div className="info-row">
                <label>Case Number:</label>
                <span>{item.data.case_number || 'N/A'}</span>
            </div>
            <div className="info-row">
                <label>Court Case ID:</label>
                <span>{item.data.court_case_id || 'N/A'}</span>
            </div>
        </div>
    </div>
);

// DbOnly Card Component
export const DbOnlyCard = ({ item, isSelected, onToggleSelect, onImageClick }) => (
    <div
        className={`orphan-card ${isSelected ? 'selected' : ''}`}
        onClick={(e) => {
            if (e.target.tagName !== 'IMG') {
                onToggleSelect(item.id);
            }
        }}
    >
        <div className="card-image-container">
            {item.data.low_res_path ? (
                <img
                    src={item.data.low_res_path}
                    alt={item.data.filename || 'Orphan image'}
                    className="card-thumbnail"
                    onClick={() => onImageClick(item.data)}
                    onError={(e) => {
                        e.target.alt = 'Image not available';
                    }}
                />
            ) : (
                <div className="no-image">
                    <span>No Image</span>
                </div>
            )}
        </div>

        <div className="card-info">
            <div className="info-row">
                <label>ID:</label>
                <span>{item.id}</span>
            </div>
            <div className="info-row">
                <label>Filename:</label>
                <span>{item.data.filename || 'N/A'}</span>
            </div>
            <div className="info-row">
                <label>Registration:</label>
                <span>{item.data.registration_number || 'N/A'}</span>
            </div>
            <div className="info-row">
                <label>Plaintiff ID:</label>
                <span>{item.data.plaintiff_id || 'N/A'}</span>
            </div>
            <div className="info-row">
                <label>Plaintiff Name:</label>
                <span>{item.data.plaintiff_name || 'N/A'}</span>
            </div>
            <div className="info-row">
                <label>Method:</label>
                <span>{item.data.method || 'N/A'}</span>
            </div>
            <div className="info-row">
                <label>Type:</label>
                <span>{item.data.type || 'N/A'}</span>
            </div>
            <div className="info-row">
                <label>Created:</label>
                <span>{item.data.create_time ? new Date(item.data.create_time).toLocaleDateString() : 'N/A'}</span>
            </div>
            <div className="info-row">
                <label>Updated:</label>
                <span>{item.data.update_time ? new Date(item.data.update_time).toLocaleDateString() : 'N/A'}</span>
            </div>
        </div>
    </div>
);
