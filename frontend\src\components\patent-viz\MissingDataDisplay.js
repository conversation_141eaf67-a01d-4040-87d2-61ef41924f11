import React from 'react';
import { Card, CardContent, Typography, List, ListItem, ListItemText, Box } from '@mui/material';

// Helper function (can be moved to a utils file)
const formatPercentage = (number) => {
  if (number === null || number === undefined || isNaN(Number(number))) {
    return 'N/A';
  }
  return `${Number(number).toFixed(2)}%`;
};

// Helper function (can be moved to a utils file)
const capitalizeWords = (str) => {
  if (!str) return '';
  return str
    .toLowerCase()
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};


// TDD: TEST: MissingDataDisplay renders title
// TDD: TEST: MissingDataDisplay renders each field and its missing percentage
// TDD: TEST: MissingDataDisplay handles empty or null data gracefully
const MissingDataDisplay = ({ title, data }) => {
  if (!data || Object.keys(data).length === 0) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>{title}</Typography>
          <Typography variant="body2" color="textSecondary">
            No missing data statistics available.
          </Typography>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>{title}</Typography>
        <List dense>
          {Object.entries(data).map(([fieldName, percentage]) => (
            <ListItem key={fieldName} disableGutters>
              <ListItemText
                primary={capitalizeWords(fieldName)}
                secondary={`${formatPercentage(percentage)} missing`}
              />
            </ListItem>
          ))}
        </List>
      </CardContent>
    </Card>
  );
};

export default MissingDataDisplay;