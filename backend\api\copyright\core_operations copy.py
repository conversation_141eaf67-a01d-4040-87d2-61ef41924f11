"""
Core CRUD operations for copyright assets
"""
import uuid
import pandas as pd
from flask import request, jsonify, current_app, g
from sqlalchemy import text

from backend.extensions import db
from backend.utils.db_utils import safe_transaction
from backend.utils.file_utils import save_uploaded_file
from backend.utils.cache_utils import get_plaintiff_df, get_case_df
from .helpers import get_low_res_path, get_high_res_path, get_certificate_path, sort_by_reg_no

def _get_copyrights_files_for_plaintiffs(plaintiff_ids, filters=None, conn=None):
    """Get copyrights_files data for a list of plaintiffs with optional filters."""
    if not plaintiff_ids:
        return {}

    # Use provided connection or create a new one
    if conn is None:
        engine = db.get_engine(bind='maidalv_db')
        with engine.connect() as conn:
            return _get_copyrights_files_for_plaintiffs(plaintiff_ids, filters, conn)

    # Build the base query
    query = """
        SELECT c.plaintiff_id, cf.id, cf.filename, cf.registration_number as reg_no, cf.method, cf.production, cf.type,
               cf.create_time, cf.update_time, c.certificate_status
        FROM copyrights_files cf
        JOIN copyrights c ON c.registration_number = cf.registration_number
        WHERE c.plaintiff_id = ANY(:plaintiff_ids)
    """
    params = {"plaintiff_ids": plaintiff_ids}

    # Apply filters if provided
    if filters:
        if filters.get('registration_number'):
            query += " AND cf.registration_number ILIKE :reg_no"
            params['reg_no'] = f"%{filters['registration_number']}%"
        if filters.get('method'):
            query += " AND cf.method = :method"
            params['method'] = filters['method']
        if filters.get('type'):
            query += " AND cf.type = :type"
            params['type'] = filters['type']
        if filters.get('production'):
            if filters['production'] == 'true':
                query += " AND cf.production = true"
            elif filters['production'] == 'false':
                query += " AND cf.production = false"

    rows = conn.execute(text(query), params).fetchall()

    files_by_plaintiff = {pid: [] for pid in plaintiff_ids}
    for row in rows:
        plaintiff_id = row[0]
        file_data = {
            "id": str(row[1]),
            "filename": row[2],
            "reg_no": row[3],
            "method": row[4],
            "production": bool(row[5]) if row[5] is not None else False,
            "type": row[6],
            "create_time": row[7].isoformat() if row[7] else None,
            "update_time": row[8].isoformat() if row[8] else None,
            "certificate_status": row[9],
            "low_res_path": get_low_res_path(plaintiff_id, row[2], row[8].isoformat()),
            "high_res_path": get_high_res_path(plaintiff_id, row[2], row[8].isoformat()),
            "certificate_path": get_certificate_path(plaintiff_id, row[2])
        }
        if plaintiff_id in files_by_plaintiff:
            files_by_plaintiff[plaintiff_id].append(file_data)

    for pid in files_by_plaintiff:
        files_by_plaintiff[pid] = sort_by_reg_no(files_by_plaintiff[pid])

    return files_by_plaintiff

def _get_cn_websites_files_for_plaintiffs(plaintiff_ids, filters=None, conn=None, case_df=None):
    """Get cn_websites_files data for a list of plaintiffs with optional filters."""
    if not plaintiff_ids:
        return {}

    # Get case data if not provided
    if case_df is None:
        case_df = get_case_df()

    if case_df.empty:
        return {}

    # Get case_ids for the given plaintiffs
    case_ids = case_df[case_df['plaintiff_id'].isin(plaintiff_ids)]['id'].tolist()
    if not case_ids:
        return {}

    # Use provided connection or create a new one
    if conn is None:
        engine = db.get_engine(bind='maidalv_db')
        with engine.connect() as conn:
            return _get_cn_websites_files_for_plaintiffs(plaintiff_ids, filters, conn, case_df)

    # Build the base query
    query = """
        SELECT cw.case_id, cwf.id, cwf.filename, cwf.type, cwf.reg_no, cwf.created_at,
               cw.docket_in_title, cw.docket_formated, cw.url,
               cw.source_website, cw.posting_date
        FROM cn_websites_files cwf
        JOIN cn_websites cw ON cwf.cn_websites_id = cw.id
        WHERE cw.case_id = ANY(:case_ids)
    """
    params = {"case_ids": case_ids}

    # Apply filters if provided
    if filters:
        if filters.get('registration_number'):
            query += " AND cwf.reg_no ILIKE :reg_no"
            params['reg_no'] = f"%{filters['registration_number']}%"
        if filters.get('type'):
            query += " AND cwf.type = :type"
            params['type'] = filters['type']

    rows = conn.execute(text(query), params).fetchall()

    # Create a mapping from case_id to plaintiff_id
    case_to_plaintiff_map = case_df[case_df['id'].isin(case_ids)].set_index('id')['plaintiff_id'].to_dict()

    files_by_plaintiff = {pid: [] for pid in plaintiff_ids}
    for row in rows:
        case_id = row[0]
        plaintiff_id = case_to_plaintiff_map.get(case_id)
        if plaintiff_id:
            file_data = {
                "id": row[1],
                "filename": row[2],
                "type": row[3],
                "reg_no": row[4],
                "created_at": row[5].isoformat() if row[5] else None,
                "docket_in_title": row[6],
                "docket_formatted": row[7],
                "case_id": case_id,
                "url": row[8],
                "website": row[9],
                "site_domain": row[9],
                "post_date": row[10].isoformat() if row[10] else None,
                "low_res_path": f"/api/v1/copyright/cn-websites-files/{row[1]}/image",
                "high_res_path": f"/api/v1/copyright/cn-websites-files/{row[1]}/image"
            }
            if plaintiff_id in files_by_plaintiff:
                files_by_plaintiff[plaintiff_id].append(file_data)

    for pid in files_by_plaintiff:
        files_by_plaintiff[pid] = sort_by_reg_no(files_by_plaintiff[pid])

    return files_by_plaintiff

@safe_transaction(bind='maidalv_db')
def get_copyright_assets():
    """
    Gets a paginated list of copyright assets grouped by plaintiff.
    Returns data in the new structure: [{ plaintiff: {...}, copyrights_files: [...], cn_websites_files: [...] }]
    """
    try:
        # --- Step 1: Parse pagination and filter arguments ---
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 5))  # 5 plaintiffs per page as per PRD

        # Parse filter arguments
        filters = {
            'plaintiff_name': request.args.get('plaintiff_name', '').strip(),
            'plaintiff_id': request.args.get('plaintiff_id', '').strip(),
            'registration_number': request.args.get('registration_number', '').strip(),
            'method': request.args.get('method', '').strip(),
            'type': request.args.get('type', '').strip(),
            'production': request.args.get('production', '').strip(),
            'sort_by': request.args.get('sort_by', 'date_filed').strip()  # Default to date_filed
        }

        # Get plaintiff data
        plaintiff_df = get_plaintiff_df()
        if plaintiff_df.empty:
            return jsonify({"success": True, "data": {"plaintiffs": [], "pagination": {"page": page, "per_page": per_page, "total_pages": 0, "total_items": 0}}})

        # Get case data to find plaintiffs that have copyright or cn_websites data
        case_df = get_case_df()
        conn = g.db_conn # Use the connection from the decorator
        
        # Find plaintiffs that have either copyrights or cn_websites data
        # Get plaintiff_ids that have copyrights
        copyright_plaintiff_ids = set()
        copyright_rows = conn.execute(text("SELECT DISTINCT plaintiff_id FROM copyrights WHERE plaintiff_id IS NOT NULL")).fetchall()
        copyright_plaintiff_ids.update(row[0] for row in copyright_rows)
        
        # Get plaintiff_ids that have cn_websites (via case_df)
        cn_websites_plaintiff_ids = set()
        if not case_df.empty:
            # Get case_ids that have cn_websites
            cn_case_ids = conn.execute(text("SELECT DISTINCT case_id FROM cn_websites WHERE case_id IS NOT NULL")).fetchall()
            cn_case_ids = {row[0] for row in cn_case_ids}
            
            # Map case_ids to plaintiff_ids
            for _, case_row in case_df.iterrows():
                if case_row['id'] in cn_case_ids and pd.notna(case_row['plaintiff_id']):
                    cn_websites_plaintiff_ids.add(int(case_row['plaintiff_id']))
        
        # Combine both sets
        relevant_plaintiff_ids = copyright_plaintiff_ids | cn_websites_plaintiff_ids
        
        # Filter plaintiff_df to only include relevant plaintiffs
        relevant_plaintiffs = plaintiff_df[plaintiff_df['id'].isin(relevant_plaintiff_ids)].copy()

        # Apply plaintiff filters if provided
        if filters.get('plaintiff_name'):
            relevant_plaintiffs = relevant_plaintiffs[
                relevant_plaintiffs['plaintiff_name'].str.contains(
                    filters['plaintiff_name'], case=False, na=False
                )
            ]

        if filters.get('plaintiff_id'):
            try:
                plaintiff_id_filter = int(filters['plaintiff_id'])
                relevant_plaintiffs = relevant_plaintiffs[
                    relevant_plaintiffs['id'] == plaintiff_id_filter
                ]
            except ValueError:
                # Invalid plaintiff_id, return empty result
                return jsonify({"success": True, "data": {"plaintiffs": [], "pagination": {"page": page, "per_page": per_page, "total_pages": 0, "total_items": 0}}})

        if relevant_plaintiffs.empty:
            return jsonify({"success": True, "data": {"plaintiffs": [], "pagination": {"page": page, "per_page": per_page, "total_pages": 0, "total_items": 0}}})

        # Build plaintiff data with their files and filter out those with zero files
        plaintiffs_data = []
        plaintiff_ids_to_fetch = [int(pid) for pid in relevant_plaintiffs['id'].dropna()]

        # Bulk fetch data for all relevant plaintiffs
        all_copyrights_files = _get_copyrights_files_for_plaintiffs(plaintiff_ids_to_fetch, filters, conn)
        all_cn_websites_files = _get_cn_websites_files_for_plaintiffs(plaintiff_ids_to_fetch, filters, conn, case_df)

        for _, plaintiff_row in relevant_plaintiffs.iterrows():
            plaintiff_id = int(plaintiff_row['id']) if pd.notna(plaintiff_row['id']) else None
            if plaintiff_id is None:
                continue
            plaintiff_name = plaintiff_row['plaintiff_name']

            # Get data from the pre-fetched dictionaries
            copyrights_files = all_copyrights_files.get(plaintiff_id, [])
            cn_websites_files = all_cn_websites_files.get(plaintiff_id, [])

            # Count totals
            copyrights_count = len(copyrights_files)
            cn_websites_count = len(cn_websites_files)

            # Only include plaintiffs that have at least one file after filtering
            if copyrights_count > 0 or cn_websites_count > 0:
                # Get most recent date_filed for this plaintiff for sorting
                most_recent_date = None
                if not case_df.empty:
                    plaintiff_cases = case_df[case_df['plaintiff_id'] == plaintiff_id]
                    if not plaintiff_cases.empty and 'date_filed' in plaintiff_cases.columns:
                        # Convert to datetime and find max, handling NaT values
                        dates = pd.to_datetime(plaintiff_cases['date_filed'], errors='coerce')
                        valid_dates = dates.dropna()
                        if not valid_dates.empty:
                            most_recent_date = valid_dates.max()

                plaintiffs_data.append({
                    "plaintiff": {
                        "id": plaintiff_id,
                        "name": plaintiff_name,
                        "counts": {
                            "copyrights_files": copyrights_count,
                            "cn_websites_files": cn_websites_count,
                            "total": copyrights_count + cn_websites_count
                        },
                        "most_recent_date_filed": most_recent_date.isoformat() if most_recent_date and pd.notna(most_recent_date) else None
                    },
                    "copyrights_files": copyrights_files,
                    "cn_websites_files": cn_websites_files
                })

        # Sort plaintiffs based on sort_by parameter
        if filters.get('sort_by') == 'plaintiff_id':
            plaintiffs_data.sort(key=lambda x: x['plaintiff']['id'])
        else:  # Default to date_filed
            # Sort by most recent date_filed (descending), then by plaintiff_id for ties
            plaintiffs_data.sort(key=lambda x: (
                x['plaintiff']['most_recent_date_filed'] is None,  # None values go last
                -(pd.to_datetime(x['plaintiff']['most_recent_date_filed']).timestamp() if x['plaintiff']['most_recent_date_filed'] else 0),  # Most recent first
                x['plaintiff']['id']  # Secondary sort by plaintiff_id
            ))

        # Paginate the filtered and sorted results
        total_plaintiffs = len(plaintiffs_data)
        total_pages = (total_plaintiffs + per_page - 1) // per_page if total_plaintiffs > 0 else 0
        start_idx = (page - 1) * per_page
        end_idx = start_idx + per_page
        page_plaintiffs_data = plaintiffs_data[start_idx:end_idx]

        return jsonify({"success": True, "data": {
            "plaintiffs": page_plaintiffs_data,
            "pagination": {
                "page": page,
                "per_page": per_page,
                "total_pages": total_pages,
                "total_items": total_plaintiffs
            }
        }})

    except Exception as e:
        current_app.logger.error(f"Error getting copyright assets: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

def add_copyright_asset():
    """Adds a new copyright asset: uploads file, inserts into external copyrights, no local viz/types."""
    engine = db.get_engine(bind='maidalv_db')
    conn = None
    try:
        conn = engine.connect()
        trans = conn.begin()

        # Parse form data
        registration_number = request.form.get('registration_number')
        plaintiff_id = request.form.get('plaintiff_id')
        method = request.form.get('method', 'manual')

        if not registration_number:
            return jsonify({"success": False, "error": "Registration number is required"}), 400

        # Handle file upload
        if 'file' not in request.files:
            return jsonify({"success": False, "error": "No file provided"}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({"success": False, "error": "No file selected"}), 400

        # Save uploaded file
        saved_path = save_uploaded_file(file, 'copyright_uploads')
        if not saved_path:
            return jsonify({"success": False, "error": "Failed to save uploaded file"}), 500

        # Insert into external copyrights table
        new_id = str(uuid.uuid4())
        conn.execute(
            text("""
                INSERT INTO copyrights (id, registration_number, plaintiff_id, method, create_time, update_time)
                VALUES (:id, :reg_no, :plaintiff_id, :method, NOW(), NOW())
            """),
            {
                "id": new_id,
                "reg_no": registration_number,
                "plaintiff_id": plaintiff_id,
                "method": method
            }
        )

        trans.commit()

        return jsonify({
            "success": True,
            "data": {
                "id": new_id,
                "registration_number": registration_number,
                "plaintiff_id": plaintiff_id,
                "method": method,
                "file_path": saved_path
            }
        })

    except Exception as e:
        if conn and trans:
            trans.rollback()
        current_app.logger.error(f"Error adding copyright asset: {e}")
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        if conn:
            conn.close()

def update_copyrights_file(file_id):
    """Updates a single copyrights_files record. ID is copyrights_files.id (UUID)."""
    engine = db.get_engine(bind='maidalv_db')
    conn = None
    try:
        conn = engine.connect()
        trans = conn.begin()

        data = request.get_json()
        if not data:
            return jsonify({"success": False, "error": "No data provided"}), 400

        # Build update query dynamically
        update_fields = []
        params = {"id": file_id}

        allowed_fields = ['registration_number', 'method', 'production', 'type']
        for field in allowed_fields:
            if field in data:
                update_fields.append(f"{field} = :{field}")
                params[field] = data[field]

        if not update_fields:
            return jsonify({"success": False, "error": "No valid fields to update"}), 400

        # Add update timestamp
        update_fields.append("update_time = NOW()")

        query = f"UPDATE copyrights_files SET {', '.join(update_fields)} WHERE id = :id"
        result = conn.execute(text(query), params)

        if result.rowcount == 0:
            return jsonify({"success": False, "error": "File not found"}), 404

        trans.commit()
        return jsonify({"success": True, "data": {"updated_fields": list(data.keys())}})

    except Exception as e:
        if conn and trans:
            trans.rollback()
        current_app.logger.error(f"Error updating copyrights file id={file_id}: {e}")
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        if conn and not conn.closed:
            conn.close()
