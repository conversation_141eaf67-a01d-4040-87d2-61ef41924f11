"""
Orphan Processing Package

This package handles orphan copyright detection and fixing operations.
It has been refactored from a single large file into modular components.

Components:
- orphan_types: Constants and data models
- orphan_helpers: Shared utility functions
- orphan_detector: Orphan detection logic
- orphan_fixer: Orphan fixing logic
- orphan_api: API endpoints
"""

from .orphan_api import get_orphan_copyrights, fix_copyright_asset

__all__ = ['get_orphan_copyrights', 'fix_copyright_asset']
