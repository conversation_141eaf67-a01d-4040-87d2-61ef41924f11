"""Remove gcp_model_name and file_path fields

Revision ID: remove_gcp_model_name_and_file_path
Revises: 2a1b3c4d5e6f
Create Date: 2024-01-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'remove_gcp_model_name_and_file_path'
down_revision = '2a1b3c4d5e6f'
branch_labels = None
depends_on = None


def upgrade():
    # Remove gcp_model_name column from bounding_box_models table
    op.drop_column('bounding_box_models', 'gcp_model_name')
    
    # Remove file_path column from bounding_box_pictures table
    op.drop_column('bounding_box_pictures', 'file_path')


def downgrade():
    # Add back gcp_model_name column to bounding_box_models table
    op.add_column('bounding_box_models', sa.Column('gcp_model_name', sa.String(255), nullable=True))
    
    # Add back file_path column to bounding_box_pictures table
    op.add_column('bounding_box_pictures', sa.Column('file_path', sa.String(), nullable=False, unique=True))
