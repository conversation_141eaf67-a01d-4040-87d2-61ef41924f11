from sqlalchemy import (
    Column, String, Text, Boolean, TIMESTAMP, Integer, Date
)
from sqlalchemy.dialects.postgresql import UUID as PG_UUID, JSONB, ARRAY, BIGINT
from backend.extensions import db

class Copyrights(db.Model):
    __tablename__ = 'copyrights'
    __bind_key__ = 'maidalv_db'

    id = Column(PG_UUID(as_uuid=True), nullable=False, primary_key=True)
    tro = Column(Boolean)
    registration_number = Column(Text)
    registration_date = Column(Date)
    type_of_work = Column(Text)
    title = Column(Text)
    date_of_creation = Column(Integer)
    date_of_publication = Column(Date)
    copyright_claimant = Column(Text)
    authorship_on_application = Column(Text)
    rights_and_permissions = Column(Text)
    description = Column(Text)
    nation_of_first_publication = Column(Text)
    names = Column(Text)
    plaintiff_id = Column(Integer)
    create_time = Column(TIMESTAMP, nullable=False)
    update_time = Column(TIMESTAMP, nullable=False)
    deleted = Column(Boolean, nullable=False)
    certificate_status = Column(String)

    query = db.session.query_property()

    def __repr__(self):
        return f'<Copyrights {self.id}>'
