# backend/api/dashboard.py
import uuid # Import uuid module
from flask import Blueprint, request, jsonify
from backend.logic.metrics import (
    calculate_precision_avg_rank,
    calculate_score_distribution,
    calculate_confusion_matrix_data,
    get_active_models_for_category
)
# Import db if direct model querying/validation is needed, but logic handles it
# from backend import db
# from backend.database.models import ModelConfig, CombinedScoreConfig

dashboard_bp = Blueprint('dashboard_api', __name__, url_prefix='/api/dashboard')

@dashboard_bp.route('/performance-summary', methods=['GET'])
def get_performance_summary():
    """
    GET /api/dashboard/performance-summary
    Query Params:
        ip_category (str): Required. e.g., 'patent', 'trademark', 'copyright'
    Returns:
        JSON list of performance summaries for active models/configs.
        [
            { "model_id": int, "model_name": str, "precision_avg_rank": float | None },
            ...
        ]
    """
    ip_category = request.args.get('ip_category')
    if not ip_category:
        return jsonify({"error": "Missing 'ip_category' query parameter"}), 400
    if ip_category not in ['patent', 'trademark', 'copyright']:
         return jsonify({"error": "Invalid 'ip_category'. Must be one of 'patent', 'trademark', 'copyright'"}), 400

    try:
        active_models = get_active_models_for_category(ip_category)
        summary_data = []
        for model_info in active_models:
            model_id = model_info['id']
            model_name = model_info['name']
            # Note: The model_id here refers to the PK of either ModelConfig or CombinedScoreConfig
            avg_rank = calculate_precision_avg_rank(model_id, ip_category)
            summary_data.append({
                "model_id": model_id,
                "model_name": model_name,
                "precision_avg_rank": avg_rank
            })
        return jsonify(summary_data), 200
    except Exception as e:
        # Log the exception e
        print(f"Error in /performance-summary: {e}") # Basic logging
        return jsonify({"error": "An internal server error occurred"}), 500


@dashboard_bp.route('/score-distribution', methods=['GET'])
def get_score_distribution():
    """
    GET /api/dashboard/score-distribution
    Query Params:
        model_id (int): Required. ID of the model or combined score config.
        ip_category (str): Required. e.g., 'patent', 'trademark', 'copyright'
    Returns:
        JSON object with score distributions.
        { "gt_scores": [...], "non_gt_scores": [...] }
    """
    model_id_str = request.args.get('model_id')
    ip_category = request.args.get('ip_category')

    print(f"Received request for score distribution: model_id={model_id_str}, ip_category={ip_category}") # Added logging

    if not model_id_str:
        return jsonify({"error": "Missing 'model_id' query parameter"}), 400
    if not ip_category:
        return jsonify({"error": "Missing 'ip_category' query parameter"}), 400
    if ip_category not in ['patent', 'trademark', 'copyright']:
         return jsonify({"error": "Invalid 'ip_category'. Must be one of 'patent', 'trademark', 'copyright'"}), 400

    try:
        # model_id is a UUID string from the frontend
        distribution_data = calculate_score_distribution(uuid.UUID(model_id_str), ip_category)
        return jsonify(distribution_data), 200
    except Exception as e:
        # Log the exception e
        print(f"Error in /score-distribution: {e}") # Basic logging
        return jsonify({"error": "An internal server error occurred"}), 500


@dashboard_bp.route('/confusion-matrix', methods=['GET'])
def get_confusion_matrix():
    """
    GET /api/dashboard/confusion-matrix
    Query Params:
        model_id (int): Required. ID of the model or combined score config.
        ip_category (str): Required. e.g., 'patent', 'trademark', 'copyright'
        threshold (float): Required. Score threshold for classification.
    Returns:
        JSON object with confusion matrix counts.
        { "tp": count, "fn": count, "fp": count, "tn": count }
    """
    model_id_str = request.args.get('model_id')
    ip_category = request.args.get('ip_category')
    threshold_str = request.args.get('threshold')

    print(f"Received request for confusion matrix: model_id={model_id_str}, ip_category={ip_category}, threshold={threshold_str}") # Added logging

    if not model_id_str:
        return jsonify({"error": "Missing 'model_id' query parameter"}), 400
    if not ip_category:
        return jsonify({"error": "Missing 'ip_category' query parameter"}), 400
    if not threshold_str:
        return jsonify({"error": "Missing 'threshold' query parameter"}), 400
    if ip_category not in ['patent', 'trademark', 'copyright']:
         return jsonify({"error": "Invalid 'ip_category'. Must be one of 'patent', 'trademark', 'copyright'"}), 400

    try:
        # model_id is a UUID string from the frontend
        threshold = float(threshold_str)
    except ValueError:
        return jsonify({"error": "'threshold' must be a float"}), 400

    try:
        matrix_data = calculate_confusion_matrix_data(uuid.UUID(model_id_str), ip_category, threshold)
        return jsonify(matrix_data), 200
    except Exception as e:
        # Log the exception e
        print(f"Error in /confusion-matrix: {e}") # Basic logging
        return jsonify({"error": "An internal server error occurred"}), 500