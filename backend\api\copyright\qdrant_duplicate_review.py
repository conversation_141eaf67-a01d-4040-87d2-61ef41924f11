"""
This module handles the logic for reviewing and resolving duplicate copyrights found in the Qdrant vector store.
"""
from datetime import datetime

from flask import request, jsonify, g
import numpy as np
import uuid
from qdrant_client import models
from sklearn.metrics.pairwise import cosine_similarity
from sqlalchemy import text
from sqlalchemy.exc import IntegrityError

from backend.database.copyright_model import Copyrights
from backend.extensions import db
from backend.utils.db_utils import safe_transaction
from backend.utils.vector_store import get_qdrant_client, delete_vector
from backend.api.copyright.helpers import QDRANT_COLLECTION_NAME, get_high_res_path
from backend.database.models import CopyrightDuplicateExclusions
from backend.api.copyright.bulk_operations import delete_copyright_files


def get_copyright_duplicates_grouped_with_plaintiff():
    """
    Finds the most similar pair of images in a Qdrant collection,
    excluding pairs that have already been resolved and stored in the
    CopyrightDuplicateExclusions table.
    If skip=true, it finds the provided image_left/image_right pair and returns the next most similar pair.
    """
    data = {}
    if request.method == 'POST':
        data = request.get_json()
    skip = data.get("skip", False)
    image_left = data.get('image_left','')
    image_right = data.get('image_right','')
    
    if skip and not (image_left and image_right):
        return jsonify({"success": False, "error": "When skip is true, image_left and image_right must be provided."}), 400

    try:
        client = get_qdrant_client()
        VECTOR_NAME_FOR_COMPARISON = "siglip_vector" 

        exclusions_query = db.session.query(
            CopyrightDuplicateExclusions.reg_no_left, 
            CopyrightDuplicateExclusions.reg_no_right
        ).all()
        excluded_pairs = set(exclusions_query)
        
        copyright_filter = models.Filter(
            must=[models.FieldCondition(key="ip_type", match=models.MatchValue(value="Copyright"))]
        )

        scrolled_points, _ = client.scroll(
            collection_name=QDRANT_COLLECTION_NAME,
            scroll_filter=copyright_filter,
            limit=100000,
            with_payload=True,
            with_vectors=[VECTOR_NAME_FOR_COMPARISON]
        )

        if len(scrolled_points) < 2:
            return jsonify({"success": False, "error": "Not enough images to compare."}), 404
        try:
            vectors = np.array([point.vector[VECTOR_NAME_FOR_COMPARISON] for point in scrolled_points])
        except (KeyError, TypeError):
            # This provides a much better error message if something is wrong with the data
            return jsonify({"success": False, "error": f"Vector named '{VECTOR_NAME_FOR_COMPARISON}' was not found in all points."}), 400

        similarity_matrix = cosine_similarity(vectors)
        np.fill_diagonal(similarity_matrix, -1.0)

        temp_similarity_matrix = np.copy(similarity_matrix)
        
        # If not skipping, this flag is true from the start.
        # If skipping, this flag will be flipped to true once we find the pair to skip.
        found_skip_target = not skip
        
        while True:
            if np.max(temp_similarity_matrix) < 0:
                # If we ran out of pairs while looking for the pair to skip, it was never found.
                if skip and not found_skip_target:
                    return jsonify({"success": False, "error": "The specified pair to skip was not found in the dataset."}), 404
                return jsonify({"success": True, "message": "No new duplicate pairs found."}), 200

            max_index_flat = np.argmax(temp_similarity_matrix)
            x, y = np.unravel_index(max_index_flat, temp_similarity_matrix.shape)

            filename_x = scrolled_points[x].payload.get('filename')
            filename_y = scrolled_points[y].payload.get('filename')
            current_pair = tuple(sorted((filename_x, filename_y)))

            # If we are in "skip" mode and have NOT yet found the target pair
            if not found_skip_target:
                target_pair_to_find = tuple(sorted((image_left, image_right)))
                if current_pair == target_pair_to_find:
                    # We found the pair we were supposed to skip. 
                    # Set the flag so the next valid pair will be returned.
                    found_skip_target = True
                
                # Invalidate this pair and continue, either because it's not the target,
                # or because it IS the target and we need the *next* one.
                temp_similarity_matrix[x, y] = -2.0
                temp_similarity_matrix[y, x] = -2.0
                continue

            # This code block is reached if:
            # 1. Skip is false.
            # 2. Skip is true AND we have already found the target pair to skip.
            
            # Check if the current top pair is in the database exclusions
            if current_pair in excluded_pairs:
                temp_similarity_matrix[x, y] = -2.0
                temp_similarity_matrix[y, x] = -2.0
                continue
            else:
                # This is a valid, non-excluded pair. It's our result.
                highest_score = temp_similarity_matrix[x, y]
                break
        
        point_x = scrolled_points[x]
        point_y = scrolled_points[y]
        point_x.vector = None
        point_y.vector = None

        image_1_path = get_high_res_path(point_x.payload.get('plaintiff_id'), point_x.payload.get('filename', ''), datetime.now().isoformat())
        image_2_path = get_high_res_path(point_y.payload.get('plaintiff_id'), point_y.payload.get('filename', ''),datetime.now().isoformat())
        image_1_copyright_row = Copyrights.query.filter_by(registration_number=point_x.payload.get('reg_no')).one()
        image_2_copyright_row = Copyrights.query.filter_by(registration_number=point_y.payload.get('reg_no')).one()


        result = {
            "success": True,
            "most_similar_pair": {
                "similarity_score": float(highest_score),
                "image_details_left": {
                    "qdrant_point": point_x.dict(), "high_res_path": image_1_path, "copyright_claimant": image_1_copyright_row.copyright_claimant, "title": image_1_copyright_row.title
                },
                "image_details_right": {
                    "qdrant_point": point_y.dict(), "high_res_path": image_2_path, "copyright_claimant": image_2_copyright_row.copyright_claimant, "title": image_2_copyright_row.title
                }
            }
        }
        return jsonify(result), 200

    except Exception as e:
        return jsonify({"success": False, "error": f"An unexpected error occurred: {str(e)}"}), 500
    
@safe_transaction(bind='maidalv_db')
def resolve_copyright_duplicates():
    """
    Resolves a duplicate pair based on a provided action.
    - 'delete': Removes a point from Qdrant.
    - 'keep': Adds the pair's registration numbers to an exclusion table in the DB.
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"success": False, "error": "Missing JSON payload."}), 400

        action = data.get("action")
        if not action:
            return jsonify({"success": False, "error": "Payload must include 'action'."}), 400

        # --- DELETE ACTION ---
        if action == "delete":
            if not data.get("image_details") or not data.get("image_details").get("qdrant_point") or not data.get("image_details").get("qdrant_point").get("payload") or not data.get("image_details").get("qdrant_point").get("payload").get("filename"):
                return jsonify({"success": False, "error": "Payload for 'delete' must include 'image_details'."}), 400
            
            filename = data.get("image_details").get("qdrant_point").get("payload").get("filename")
            
            # Fetch file to delete
            file_to_delete_query = text("SELECT id, registration_number, filename, production FROM copyrights_files WHERE filename = :filename")
            file_to_delete = g.db_conn.execute(file_to_delete_query, {"filename": filename}).fetchone()

            if not file_to_delete:
                qdrant_client = get_qdrant_client()
                point_id = str(uuid.uuid5(uuid.NAMESPACE_DNS, filename))
                delete_vector(qdrant_client, QDRANT_COLLECTION_NAME, point_id)
                return jsonify({"success": False, "error": f"File with filename '{filename}' not found in database, but delete from Qdrant."}), 404

            # Use the bulk operation helper to delete the single file
            delete_copyright_files([file_to_delete], g.db_conn)
            
            return jsonify({"success": True, "message": f"File '{filename}' was deleted successfully."}), 200

        elif action == "keep":
            image_details_left = data.get("image_details_left")
            image_details_right = data.get("image_details_right")
            
            if not image_details_left or not image_details_right:
                return jsonify({"success": False, "error": "Payload for 'keep' must include 'image_details_1' and 'image_details_2'."}), 400

            filename_left = image_details_left.get("qdrant_point", {}).get("payload", {}).get("filename")
            filename_right = image_details_right.get("qdrant_point", {}).get("payload", {}).get("filename")
            filename_left_sorted = min(filename_left, filename_right)
            filename_right_sorted = max(filename_left, filename_right)
            try:
                # Since we are in a safe_transaction, we use g.db_conn
                insert_query = text("INSERT INTO copyright_duplicate_exclusions (reg_no_left, reg_no_right) VALUES (:reg_no_left, :reg_no_right)")
                g.db_conn.execute(insert_query, {"reg_no_left": filename_left_sorted, "reg_no_right": filename_right_sorted})

                return jsonify({
                    "success": True,
                    "message": f"Exclusion for pair ({filename_left}, {filename_right}) was saved successfully."
                }), 201 # 201 Created is a good status code here

            except IntegrityError:
                return jsonify({
                    "success": False,
                    "error": f"This pair ({filename_left_sorted}, {filename_right_sorted}) has already been marked for exclusion."
                }), 409 # 409 Conflict is a fitting status code
            except Exception as db_error:
                return jsonify({"success": False, "error": f"A database error occurred: {db_error}"}), 500

        else:
            return jsonify({"success": False, "error": f"Invalid action '{action}'."}), 400

    except Exception as e:
        return jsonify({"success": False, "error": f"An unexpected error occurred: {str(e)}"}), 500