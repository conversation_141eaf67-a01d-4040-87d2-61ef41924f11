import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';

// TDD: TEST: PatentDashboardPage component is correctly imported and rendered (Now importing actual)

// TDD: TEST: PatentExplorerPage component is correctly imported and rendered
// import PatentExplorerPage from './patent-viz/PatentExplorerPage'; // Import the actual component
import TrademarkImageSearchPage from './trademark-viz/ImageSearcherPage';
import TrademarkExplorerPage from './trademark-viz/TrademarkExplorerPage';
const NotFoundPage = () => <div>Platform Page Not Found</div>; // Or a more generic 404

// TDD: TEST: PatentPlatform renders DashboardPage when path is /patent-viz/dashboard
// TDD: TEST: PatentPlatform renders PatentExplorerPage when path is /patent-viz/explorer
// TDD: TEST: PatentPlatform redirects from /patent-viz to /patent-viz/dashboard by default

const TrademarkPlatformPage = () => {
    return (
        <Routes>
            {/* Default route for /patent-viz, redirects to dashboard */}
            <Route path="/" element={<Navigate to="image-search" replace />} />

            {/* <Route path="dashboard" element={<PatentDashboardPage />} /> */}
            <Route path="explorer" element={<TrademarkExplorerPage />} />
            <Route path="image-search" element={<TrademarkImageSearchPage />} />
            {/* 
        Potentially other routes like explorer/:patentId if not handled within PatentExplorerPage
        e.g. <Route path="explorer/:patentId" element={<PatentDetailPage />} /> 
      */}

            <Route path="*" element={<NotFoundPage />} />
        </Routes>
    );
};

export default TrademarkPlatformPage;