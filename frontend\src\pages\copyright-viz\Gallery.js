import React, { useState, useEffect, useCallback, useRef } from 'react';
import { getCopyrightAssets, getCopyrightTypes, getCopyrightMethods, bulkUpdateCopyrightsFiles, bulkSetProdWithProgress, bulkUnsetProdWithProgress, moveCnWebsitesFilesWithProgress } from '../../services/api_copyright_viz';
import Lightbox from "yet-another-react-lightbox";
import "yet-another-react-lightbox/styles.css";
import './Gallery.css';
import ImageDrawer from './ImageDrawer';
import SetTypeModal from './SetTypeModal';
import Pagination from './Pagination';
import DragDropContext from '../../components/DragDropContext';
import DraggableImageCard from '../../components/DraggableImageCard';
import DroppableZone from '../../components/DroppableZone';
import ProgressToast from '../../components/ProgressToast';

/**
 * Gallery component for browsing and managing copyright assets grouped by plaintiff.
 * It supports filtering, bulk actions, pagination, and detailed view of assets.
 */
const Gallery = () => {
    // State for plaintiffs data, selections, and UI controls
    const [plaintiffs, setPlaintiffs] = useState([]);
    const [selectedAssets, setSelectedAssets] = useState(new Set());
    const [currentSubsection, setCurrentSubsection] = useState('copyrights_files'); // Track which subsection is active for bulk actions
    const [expandedPlaintiffs, setExpandedPlaintiffs] = useState(new Set());
    const [filters, setFilters] = useState({
        plaintiff_name: '',
        plaintiff_id: '',
        registration_number: '',
        method: '',
        type: '',
        production: '',
        sort_by: 'date_filed'
    });
    const [pagination, setPagination] = useState({ page: 1, per_page: 5, total_pages: 1, total_items: 0 }); // 5 plaintiffs per page
    const [copyrightTypes, setCopyrightTypes] = useState([]);
    const [methodOptions, setMethodOptions] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    // Utility to safely coerce potentially NaN/undefined/null to displayable string
    const safeText = (val, fallback = 'N/A') => {
        if (val === null || val === undefined) return fallback;
        if (typeof val === 'number' && Number.isNaN(val)) return fallback;
        if (typeof val === 'string' && val.trim().toLowerCase() === 'nan') return fallback;
        return String(val);
    };

    // State for modals and drawers
    const [lightboxOpen, setLightboxOpen] = useState(false);
    const [lightboxSlides, setLightboxSlides] = useState([]);
    const [drawerAsset, setDrawerAsset] = useState(null);
    const [drawerPlaintiffId, setDrawerPlaintiffId] = useState(null);
    const [isSetTypeModalOpen, setIsSetTypeModalOpen] = useState(false);

    // Drag and drop state
    const [activeId, setActiveId] = useState(null);

    // State for progress toast
    const [progressToast, setProgressToast] = useState({
        isVisible: false,
        progress: { current: 0, total: 0, successful: 0, failed: 0 },
        title: '',
        showDetails: false,
        logs: []
    });
    const [currentEventSource, setCurrentEventSource] = useState(null);

    // Track if initial load has completed to prevent duplicate calls
    const debounceTimeout = useRef(null);
    const isInitialMount = useRef(true);


    // Initial data load - only runs once on mount
    useEffect(() => {
        const fetchInitialData = async () => {
            setLoading(true);
            setError(null);
            try {
                const [typesResult, methodsResult, plaintiffsResult] = await Promise.all([
                    getCopyrightTypes(),
                    getCopyrightMethods(),
                    getCopyrightAssets({ ...filters, page: 1, per_page: 5 })
                ]);
                setCopyrightTypes(typesResult.data || []);
                setMethodOptions(methodsResult.data || []);
                setPlaintiffs(plaintiffsResult?.data?.plaintiffs ?? []);
                setPagination(plaintiffsResult.data.pagination);
            } catch (err) {
                setError(err.message);
            } finally {
                setLoading(false);
            }
        };
        fetchInitialData();
    }, []); // Empty dependency array ensures this runs only once on mount

    // Subsequent data fetching based on filters and pagination
    const fetchPlaintiffs = useCallback(async () => {
        setLoading(true);
        setError(null);
        try {
            const activeFilters = Object.entries(filters).reduce((acc, [key, value]) => {
                if (value !== '') acc[key] = value;
                return acc;
            }, {});
            const params = { ...activeFilters, page: pagination.page, per_page: pagination.per_page };
            const result = await getCopyrightAssets(params);
            setPlaintiffs(result?.data?.plaintiffs ?? []);
            setPagination(result.data.pagination);
        } catch (err) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    }, [filters, pagination.page, pagination.per_page]); // Dependencies for re-fetching

    // Effect for applying filters and pagination changes - only runs after initial load
    useEffect(() => {
        if (isInitialMount.current) {
            isInitialMount.current = false;
            return;
        }

        // Clear the previous timeout if it exists
        if (debounceTimeout.current) {
            clearTimeout(debounceTimeout.current);
        }

        // Set a new timeout
        debounceTimeout.current = setTimeout(() => {
            fetchPlaintiffs();
        }, 800); // 800ms delay

        // Cleanup function to clear the timeout when the component unmounts or dependencies change
        return () => {
            if (debounceTimeout.current) {
                clearTimeout(debounceTimeout.current);
            }
        };
    }, [filters, pagination.page, fetchPlaintiffs]);

    // // Effect for handling asset selection changes - runs instantly
    // useEffect(() => {
    //     // We don't need to do anything here, but this hook is necessary to
    //     // re-render the component when selectedAssets changes.
    // }, [selectedAssets]);

    /**
     * Handles changes in filter inputs.
     */
    const handleFilterChange = (e) => {
        const { name, value } = e.target;
        setFilters(prev => ({
            ...prev,
            [name]: value,
        }));
    };

    /**
     * Toggles the selection of an asset.
     */
    const handleSelectAsset = (assetId, subsection) => {
        if (!assetId) return;
        const newSelectedAssets = new Set(selectedAssets);
        if (newSelectedAssets.has(assetId)) {
            newSelectedAssets.delete(assetId);
        } else {
            newSelectedAssets.add(assetId);
        }
        setSelectedAssets(newSelectedAssets);
        setCurrentSubsection(subsection); // Track which subsection the selection is from
    };

    /**
     * Toggles the expansion state of a plaintiff section.
     */
    const togglePlaintiffExpansion = (plaintiffId) => {
        const newExpanded = new Set(expandedPlaintiffs);
        if (newExpanded.has(plaintiffId)) {
            newExpanded.delete(plaintiffId);
        } else {
            newExpanded.add(plaintiffId);
        }
        setExpandedPlaintiffs(newExpanded);
    };

    /**
     * Executes a bulk action on the selected assets.
     */
    const handleBulkAction = async (action, value = null) => {
        if (selectedAssets.size === 0) return;

        const selectedIds = Array.from(selectedAssets);

        if (currentSubsection === 'copyrights_files') {
            // Handle copyrights_files bulk actions
            if (action === 'set_production_true') {
                handleStreamingBulkAction('set_prod', selectedIds);
            } else if (action === 'set_production_false') {
                handleStreamingBulkAction('unset_prod', selectedIds);
            } else if (action === 'set_type') {
                // Non-streaming action
                setLoading(true);
                setError(null);
                try {
                    await bulkUpdateCopyrightsFiles('set_type', selectedIds, value);
                    setSelectedAssets(new Set());
                    fetchPlaintiffs();
                } catch (err) {
                    setError(err.message);
                } finally {
                    setLoading(false);
                }
            } else if (action === 'delete') {
                // Non-streaming action
                setError(null);
                try {
                    await bulkUpdateCopyrightsFiles('delete', selectedIds);
                    removeAssetsFromState(selectedAssets); // Update local state instead of refetching
                    setSelectedAssets(new Set());
                } catch (err) {
                    setError(err.message);
                }
            }
        } else if (currentSubsection === 'cn_websites_files') {
            // Handle cn_websites_files bulk actions
            if (action === 'move_to_copyrights') {
                handleMoveFileWithProgress(selectedIds);
            }
        }
    };

    /**
     * Handles streaming bulk actions with progress updates
     */
    const handleStreamingBulkAction = (actionType, assetIds) => {
        // Close any existing event source
        if (currentEventSource) {
            currentEventSource.close();
        }

        // Initialize progress toast
        setProgressToast({
            isVisible: true,
            progress: { current: 0, total: assetIds.length, successful: 0, failed: 0 },
            title: actionType === 'set_prod' ? 'Setting Production...' : 'Unsetting Production...',
            showDetails: false,
            logs: []
        });

        const onProgress = (data) => {
            setProgressToast(prev => {
                const newLogs = [...prev.logs];

                if (data.type === 'progress') {
                    return {
                        ...prev,
                        progress: {
                            current: data.current,
                            total: data.total,
                            successful: data.successful,
                            failed: data.failed
                        }
                    };
                } else if (data.type === 'success') {
                    newLogs.push({
                        type: 'success',
                        time: new Date().toLocaleTimeString(),
                        message: `✓ ${data.reg_no || data.registration_number}: ${data.message}`
                    });

                    // Update the card in the UI immediately
                    updateAssetProductionFlag(data.asset_id, actionType === 'set_prod');

                    return { ...prev, logs: newLogs };
                } else if (data.type === 'error') {
                    newLogs.push({
                        type: 'error',
                        time: new Date().toLocaleTimeString(),
                        message: `✗ ${data.reg_no || data.registration_number || data.asset_id}: ${data.message}`
                    });
                    return { ...prev, logs: newLogs };
                }

                return prev;
            });
        };

        const onComplete = (data) => {
            setProgressToast(prev => ({
                ...prev,
                title: `${actionType === 'set_prod' ? 'Set Production' : 'Unset Production'} Complete`,
                logs: [...prev.logs, {
                    type: 'progress',
                    time: new Date().toLocaleTimeString(),
                    message: `Completed: ${data.successful} successful, ${data.failed} failed`
                }]
            }));

            setCurrentEventSource(null);
            setSelectedAssets(new Set());
        };

        const onError = (error) => {
            setProgressToast(prev => ({
                ...prev,
                title: 'Operation Failed',
                logs: [...prev.logs, {
                    type: 'error',
                    time: new Date().toLocaleTimeString(),
                    message: `Error: ${error.message || 'Unknown error occurred'}`
                }]
            }));
            setCurrentEventSource(null);
        };

        // Start the streaming operation
        const eventSource = actionType === 'set_prod'
            ? bulkSetProdWithProgress(assetIds, onProgress, onComplete, onError)
            : bulkUnsetProdWithProgress(assetIds, onProgress, onComplete, onError);

        setCurrentEventSource(eventSource);
    };

    const handleMoveFileWithProgress = (assetIds) => {
        if (currentEventSource) {
            currentEventSource.close();
        }

        setProgressToast({
            isVisible: true,
            progress: { current: 0, total: assetIds.length, successful: 0, failed: 0 },
            title: 'Moving CN Website File...',
            showDetails: false,
            logs: []
        });

        const onProgress = (data) => {
            setProgressToast(prev => {
                const newLogs = [...prev.logs];
                if (data.type === 'log') {
                    newLogs.push({
                        type: 'info',
                        time: new Date().toLocaleTimeString(),
                        message: `[${data.asset_id}] ${data.message}`
                    });
                    return { ...prev, logs: newLogs };
                } else if (data.type === 'progress') {
                    return {
                        ...prev,
                        progress: {
                            current: data.current,
                            total: data.total,
                            successful: data.successful,
                            failed: data.failed
                        }
                    };
                } else if (data.type === 'success') {
                    newLogs.push({
                        type: 'success',
                        time: new Date().toLocaleTimeString(),
                        message: `✓ ${data.reg_no || data.registration_number}: ${data.message}`
                    });
                    // Update UI immediately by calling the centralized handler
                    if (data.new_asset) {
                        handleAssetUpdate({ asset_to_add: data.new_asset });
                    }
                    return { ...prev, logs: newLogs };
                } else if (data.type === 'error') {
                    newLogs.push({
                        type: 'error',
                        time: new Date().toLocaleTimeString(),
                        message: `✗ ${data.reg_no || data.registration_number || data.asset_id}: ${data.message}`
                    });
                    return { ...prev, logs: newLogs };
                }
                return prev;
            });
        };

        const onComplete = (data) => {
            setProgressToast(prev => ({
                ...prev,
                title: 'Move Complete',
                logs: [...prev.logs, {
                    type: 'progress',
                    time: new Date().toLocaleTimeString(),
                    message: `Completed: ${data.successful} successful, ${data.failed} failed`
                }]
            }));
            setCurrentEventSource(null);
            setSelectedAssets(new Set());
        };

        const onError = (error) => {
            setProgressToast(prev => ({
                ...prev,
                title: 'Operation Failed',
                logs: [...prev.logs, {
                    type: 'error',
                    time: new Date().toLocaleTimeString(),
                    message: `Error: ${error.message || 'Unknown error occurred'}`
                }]
            }));
            setCurrentEventSource(null);
        };

        const eventSource = moveCnWebsitesFilesWithProgress(assetIds, onProgress, onComplete, onError);
        setCurrentEventSource(eventSource);
    };

    /**
     * Updates the production flag of an asset in the current state
     */
    const updateAssetProductionFlag = (assetId, production) => {
        setPlaintiffs(prevPlaintiffs =>
            prevPlaintiffs.map(plaintiffData => ({
                ...plaintiffData,
                copyrights_files: plaintiffData.copyrights_files.map(file =>
                    file.id === assetId ? { ...file, production } : file
                ),
            }))
        );
    };

    /**
     * Removes a set of assets from the current state
     */
    const removeAssetsFromState = (assetIdsToRemove) => {
        setPlaintiffs(prevPlaintiffs =>
            prevPlaintiffs.map(plaintiffData => ({
                ...plaintiffData,
                copyrights_files: plaintiffData.copyrights_files.filter(
                    file => !assetIdsToRemove.has(file.id)
                ),
            }))
        );
    };

    const handleAssetUpdate = (updateData) => {
        if (!updateData) return;

        console.log('updateData:', updateData);

        let asset_to_update, asset_to_delete, asset_to_add;

        // Normalize updateData to handle both old and new structures
        if (updateData.asset_to_update || updateData.asset_to_delete || updateData.asset_to_add) {
            ({ asset_to_update, asset_to_delete, asset_to_add } = updateData);
        } else {
            // This is the old structure, treat the whole object as an asset_to_update
            asset_to_update = updateData;
        }

        // Process deletion
        if (asset_to_delete && asset_to_delete.action === 'delete') {
            console.log('Deleting asset:', asset_to_delete);
            const assetIdsToRemove = new Set([asset_to_delete.id]);
            removeAssetsFromState(assetIdsToRemove);
            // If the deleted asset is the one in the drawer, close the drawer
            if (drawerAsset && drawerAsset.id === asset_to_delete.id) {
                setDrawerAsset(null);
                setDrawerPlaintiffId(null);
            }
        }

        // Process update
        if (asset_to_update) {
            console.log('Updating asset:', asset_to_update);
            setPlaintiffs(prevPlaintiffs =>
                prevPlaintiffs.map(plaintiffData => ({
                    ...plaintiffData,
                    copyrights_files: plaintiffData.copyrights_files.map(file =>
                        file.id === asset_to_update.id ? { ...file, ...asset_to_update } : file
                    ),
                    // The old logic also updated cn_websites_files, which you might want to keep
                    cn_websites_files: plaintiffData.cn_websites_files.map(file =>
                        file.id === asset_to_update.id ? { ...file, ...asset_to_update } : file
                    ),
                }))
            );
            // Also update the drawer asset if it's open
            if (drawerAsset && drawerAsset.id === asset_to_update.id) {
                setDrawerAsset(asset_to_update);
            }
        }

        // Process addition
        // if (asset_to_add) {
        //     console.log('Adding asset:', asset_to_add);
        //     setPlaintiffs(prevPlaintiffs => {
        //         const newPlaintiffs = [...prevPlaintiffs];
        //         console.log(newPlaintiffs);
        //         const plaintiffIndex = newPlaintiffs.findIndex(p => p.plaintiff.id == asset_to_add.plaintiff_id);
        //         console.log(plaintiffIndex);
        //         if (plaintiffIndex !== -1) {
        //             const plaintiffData = { ...newPlaintiffs[plaintiffIndex] };
        //             plaintiffData.copyrights_files = [...plaintiffData.copyrights_files, asset_to_add];
        //             newPlaintiffs[plaintiffIndex] = plaintiffData;
        //         }
        //         return newPlaintiffs;
        //     });
        // }
        if (asset_to_add) {
            console.log('Adding asset:', asset_to_add);
            const assets = Array.isArray(asset_to_add) ? asset_to_add : [asset_to_add];

            console.log('Adding assets:', assets);

            // 2. Update state in a single, atomic operation.
            // This is crucial for performance and to avoid bugs with stale state.
            setPlaintiffs(prevPlaintiffs => {
                // Start with a mutable copy of the previous state.
                const newPlaintiffs = [...prevPlaintiffs];

                // 3. Loop through all the assets we need to add.
                assets.forEach(asset => {
                    const plaintiffIndex = newPlaintiffs.findIndex(p => p.plaintiff.id == asset.plaintiff_id);

                    console.log(`Processing asset for plaintiff ID ${asset.plaintiff_id}. Index found: ${plaintiffIndex}`);

                    if (plaintiffIndex !== -1) {
                        // Create a copy of the plaintiff's data to avoid direct mutation.
                        const plaintiffData = { ...newPlaintiffs[plaintiffIndex] };
                        plaintiffData.copyrights_files = [...plaintiffData.copyrights_files, asset];

                        // Replace the old plaintiff object with the updated one.
                        newPlaintiffs[plaintiffIndex] = plaintiffData;
                    }
                });

                // 4. Return the final, updated state array once.
                return newPlaintiffs;
            });
        }
    };

    /**
     * Closes the progress toast
     */
    const closeProgressToast = () => {
        if (currentEventSource) {
            currentEventSource.close();
            setCurrentEventSource(null);
        }
        setProgressToast(prev => ({ ...prev, isVisible: false }));
    };

    /**
     * Toggles the progress toast details
     */
    const toggleProgressDetails = () => {
        setProgressToast(prev => ({ ...prev, showDetails: !prev.showDetails }));
    };

    /**
     * Opens the lightbox with the specified slides.
     */
    const openLightbox = (slides) => {
        setLightboxSlides(slides);
        setLightboxOpen(true);
    };

    /**
     * Handles clicks on the main image of an asset card.
     */
    const handleImageClick = (e, asset) => {
        e.stopPropagation();
        if (asset.high_res_path) {
            openLightbox([{ src: asset.high_res_path, title: safeText(asset.registration_number, 'N/A') }]);
        }
    };

    /**
     * Handles clicks on an asset card to open the details drawer.
     */
    const handleCardClick = (asset, plaintiffId) => {
        if (asset.id) {
            setDrawerAsset(asset);
            setDrawerPlaintiffId(plaintiffId);
        }
    };

    /**
     * Handles drag start event
     */
    const handleDragStart = (event) => {
        setActiveId(event.active.id);
    };

    /**
     * Handles drag end event - moves CN websites files to copyrights files
     */
    const handleDragEnd = async (event) => {
        const { active, over } = event;
        setActiveId(null);

        if (!over || !over.id) return;

        // Check if we're dropping a CN websites file onto a copyrights files zone
        if (active.id && typeof over.id === 'string' && over.id.startsWith('copyrights-files-')) {
            const draggedFileId = active.id;
            const targetPlaintiffId = over.id.replace('copyrights-files-', '');

            handleMoveFileWithProgress([draggedFileId]);
        }
    };

    /**
     * Handles page changes from the pagination component.
     */
    const handlePageChange = (newPage) => {
        setPagination(prev => ({ ...prev, page: newPage }));
    };

    /**
     * Handles clicks on the "View Certificate" link.
     */
    const handleCertificateClick = (e, asset) => {
        e.stopPropagation();
        if (asset.certificate_path) {
            openLightbox([{ src: asset.certificate_path, title: `Certificate for ${safeText(asset.registration_number, 'N/A')}` }]);
        }
    };

    if (error) return <p style={{ color: 'red' }}>{error}</p>;

    // Determines if the delete button should be shown for copyrights_files
    const canDeleteSelectedFiles = () => {
        if (currentSubsection !== 'copyrights_files' || selectedAssets.size === 0) {
            return false;
        }

        for (const plaintiffData of plaintiffs) {
            for (const file of plaintiffData.copyrights_files) {
                if (selectedAssets.has(file.id) && file.production) {
                    return false; // Found a selected file that is in production
                }
            }
        }

        return true; // No selected production files found
    };

    // Get all draggable items for DndContext
    const getAllDraggableItems = () => {
        const items = [];
        plaintiffs.forEach(plaintiffData => {
            plaintiffData.cn_websites_files.forEach(file => {
                items.push(file.id);
            });
        });
        return items;
    };

    return (
        <DragDropContext
            onDragStart={handleDragStart}
            onDragEnd={handleDragEnd}
            activeId={activeId}
            items={getAllDraggableItems()}
        >
            <div className={`gallery-container ${activeId ? 'dragging-active' : ''}`}>
                <div className="filter-panel">
                    <div className="filter-grid">
                        <div className="filter-group">
                            <label htmlFor="plaintiff_name">Plaintiff Name</label>
                            <input
                                type="text"
                                name="plaintiff_name"
                                id="plaintiff_name"
                                value={filters.plaintiff_name}
                                onChange={handleFilterChange}
                                placeholder="Fuzzy match, case-insensitive"
                            />
                        </div>

                        <div className="filter-group">
                            <label htmlFor="plaintiff_id">Plaintiff ID</label>
                            <input
                                type="number"
                                name="plaintiff_id"
                                id="plaintiff_id"
                                value={filters.plaintiff_id}
                                onChange={handleFilterChange}
                                placeholder="e.g., 123"
                            />
                        </div>

                        <div className="filter-group">
                            <label htmlFor="registration_number">Registration Number</label>
                            <input
                                type="text"
                                name="registration_number"
                                id="registration_number"
                                value={filters.registration_number}
                                onChange={handleFilterChange}
                                placeholder="e.g., VA 123456"
                            />
                        </div>

                        <div className="filter-group">
                            <label htmlFor="method">Method</label>
                            <select name="method" id="method" value={filters.method} onChange={handleFilterChange}>
                                <option value="">All</option>
                                {methodOptions.map((m) => (
                                    <option key={m.name} value={m.name}>{m.name}</option>
                                ))}
                            </select>
                        </div>

                        <div className="filter-group">
                            <label htmlFor="production">Production Status</label>
                            <select
                                name="production"
                                id="production"
                                value={filters.production}
                                onChange={handleFilterChange}
                            >
                                <option value="">All</option>
                                <option value="true">Production</option>
                                <option value="false">Non-Production</option>
                            </select>
                        </div>

                        <div className="filter-group">
                            <label htmlFor="sort_by">Sort By</label>
                            <select
                                name="sort_by"
                                id="sort_by"
                                value={filters.sort_by}
                                onChange={handleFilterChange}
                            >
                                <option value="date_filed">Most Recent Date Filed</option>
                                <option value="plaintiff_id">Plaintiff ID</option>
                            </select>
                        </div>
                    </div>
                    <div className="filter-actions">
                        <button onClick={() => fetchPlaintiffs()} disabled={loading} className="refresh-button">
                            &#x21bb; Refresh Data
                        </button>
                        <button
                            onClick={() => {
                                setFilters({
                                    plaintiff_name: '',
                                    plaintiff_id: '',
                                    registration_number: '',
                                    method: '',
                                    type: '',
                                    production: '',
                                    sort_by: 'date_filed'
                                });
                            }}
                            disabled={loading}
                        >
                            Clear Filters
                        </button>
                    </div>
                </div>
                {/* Bulk Action Toolbar - contextual to current subsection */}
                {selectedAssets.size > 0 && (
                    <div className="action-bar">
                        {currentSubsection === 'copyrights_files' && (
                            <>
                                <button onClick={() => handleBulkAction('set_production_true')} disabled={loading}>
                                    Set Production True
                                </button>
                                <button onClick={() => handleBulkAction('set_production_false')} disabled={loading}>
                                    Set Production False
                                </button>
                                <button onClick={() => setIsSetTypeModalOpen(true)} disabled={loading}>
                                    Set Type
                                </button>
                                {canDeleteSelectedFiles() && (
                                    <button onClick={() => handleBulkAction('delete')} disabled={loading} className="delete-button">
                                        Delete Selected Files
                                    </button>
                                )}
                            </>
                        )}
                        {currentSubsection === 'cn_websites_files' && (
                            <button onClick={() => handleBulkAction('move_to_copyrights')} disabled={loading}>
                                Move to Copyrights Files
                            </button>
                        )}
                    </div>
                )}

                {loading ? <p>Loading...</p> : (
                    <>
                        <div className="plaintiffs-container">
                            {plaintiffs.map(plaintiffData => (
                                <div key={plaintiffData.plaintiff.id} className="plaintiff-section">
                                    <div
                                        className="plaintiff-header"
                                        onClick={() => togglePlaintiffExpansion(plaintiffData.plaintiff.id)}
                                    >
                                        <h3>
                                            {plaintiffData.plaintiff.name} (ID: {plaintiffData.plaintiff.id})
                                            <span className="counts">
                                                - Copyrights: {plaintiffData.plaintiff.counts.copyrights_files},
                                                CN Websites: {plaintiffData.plaintiff.counts.cn_websites_files}
                                            </span>
                                        </h3>
                                        <span className={`expand-icon ${expandedPlaintiffs.has(plaintiffData.plaintiff.id) ? 'expanded' : ''}`}>
                                            ▼
                                        </span>
                                    </div>

                                    {expandedPlaintiffs.has(plaintiffData.plaintiff.id) && (
                                        <div className="plaintiff-content">
                                            {/* Copyrights Files Subsection */}
                                            <div className="subsection">
                                                <h4>Copyrights Files ({plaintiffData.copyrights_files.length})</h4>
                                                <DroppableZone
                                                    id={`copyrights-files-${plaintiffData.plaintiff.id}`}
                                                    className="image-grid"
                                                    acceptsFrom={['cn_websites_files']}
                                                >
                                                    {plaintiffData.copyrights_files.map(file => (
                                                        <DraggableImageCard
                                                            key={file.id}
                                                            id={file.id}
                                                            file={file}
                                                            isSelected={selectedAssets.has(file.id)}
                                                            onSelect={handleSelectAsset}
                                                            onCardClick={handleCardClick}
                                                            onImageClick={handleImageClick}
                                                            subsection="copyrights_files"
                                                            plaintiffId={plaintiffData.plaintiff.id}
                                                            safeText={safeText}
                                                            isDragDisabled={true}
                                                        />
                                                    ))}
                                                </DroppableZone>
                                            </div>

                                            {/* CN Websites Files Subsection */}
                                            <div className="subsection">
                                                <h4>CN Websites Files ({plaintiffData.cn_websites_files.length})</h4>
                                                <div className="image-grid">
                                                    {plaintiffData.cn_websites_files.map(file => (
                                                        <DraggableImageCard
                                                            key={file.id}
                                                            id={file.id}
                                                            file={file}
                                                            isSelected={selectedAssets.has(file.id)}
                                                            onSelect={handleSelectAsset}
                                                            onCardClick={handleCardClick}
                                                            onImageClick={handleImageClick}
                                                            subsection="cn_websites_files"
                                                            plaintiffId={plaintiffData.plaintiff.id}
                                                            safeText={safeText}
                                                            isDragDisabled={false}
                                                        />
                                                    ))}
                                                </div>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            ))}
                        </div>
                        <Pagination
                            currentPage={pagination.page}
                            totalPages={pagination.total_pages}
                            onPageChange={handlePageChange}
                        />
                    </>
                )}
            </div>
            {/* Modals and Drawers */}
            <Lightbox
                open={lightboxOpen}
                close={() => setLightboxOpen(false)}
                slides={lightboxSlides}
            />
            {drawerAsset && (
                <ImageDrawer
                    asset={drawerAsset}
                    plaintiffs={plaintiffs}
                    plaintiffId={drawerPlaintiffId}
                    onClose={() => {
                        setDrawerAsset(null);
                        setDrawerPlaintiffId(null);
                    }}
                    onUpdate={(updatedAsset) => {
                        handleAssetUpdate(updatedAsset);
                        // Keep the drawer open and update its asset
                        if (drawerAsset && updatedAsset && drawerAsset.id === updatedAsset.id) {
                            setDrawerAsset(updatedAsset);
                        }
                        // OR: Close the drawer after an update
                        // setDrawerAsset(null);
                        // setDrawerPlaintiffId(null);
                    }}
                    onMoveFileWithProgress={handleMoveFileWithProgress}
                />
            )}
            {isSetTypeModalOpen && (
                <SetTypeModal
                    types={copyrightTypes}
                    onClose={() => setIsSetTypeModalOpen(false)}
                    onConfirm={(selectedType) => {
                        handleBulkAction('set_type', selectedType);
                        setIsSetTypeModalOpen(false);
                    }}
                />
            )}

            {/* Progress Toast */}
            <ProgressToast
                isVisible={progressToast.isVisible}
                progress={progressToast.progress}
                title={progressToast.title}
                showDetails={progressToast.showDetails}
                logs={progressToast.logs}
                onClose={closeProgressToast}
                onToggleDetails={toggleProgressDetails}
            />
        </DragDropContext>
    );
};

export default Gallery;
