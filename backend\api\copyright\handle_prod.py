import uuid

from flask import g
from sqlalchemy import text
import numpy as np

import tempfile
import os

from backend.AI.shared_models import get_siglip_model
from backend.utils.db_utils import safe_transaction
from backend.utils.vector_store import get_qdrant_client, store_vector, delete_vector
from backend.utils.cache_utils import get_plaintiff_df, get_case_df
from backend.api.copyright.helpers import get_high_res_path, get_low_res_path, download_image_bytes, QDRANT_COLLECTION_NAME, QDRANT_VECTOR_SIZE


def _get_qdrant_payload(asset_row, plaintiff_df, case_df):
    reg_no = asset_row.get('registration_number')
    plaintiff_id = asset_row.get('plaintiff_id')
    if plaintiff_id is not None:
        plaintiff_id = int(plaintiff_id)
    filename = asset_row.get('filename')
    ip_type = "Copyright"  # Default to 'Copyright'

    # Get plaintiff_name
    plaintiff_name = None
    if plaintiff_id and not plaintiff_df.empty:
        plaintiff_row = plaintiff_df[plaintiff_df['id'].astype(str) == str(plaintiff_id)]
        if not plaintiff_row.empty:
            plaintiff_name = plaintiff_row.iloc[0].get('plaintiff_name')

    # Get number_of_cases
    number_of_cases = 0
    if plaintiff_id and not case_df.empty and 'plaintiff_id' in case_df.columns:
        number_of_cases = case_df.query(f'plaintiff_id == {plaintiff_id}').shape[0]


    full_filename = asset_row.get('filename')  # Default fallback
    docket = None
    if not case_df.empty and 'images' in case_df.columns:
        for _, row in case_df.iterrows():
            images = row.get('images', {})
            if not isinstance(images, dict):
                continue

            copyrights = images.get('copyrights', {})
            if filename in copyrights:
                data = copyrights[filename]
                full_filename = data.get('full_filename', [filename])[0]  # fallback to filename
                docket = row.get('docket')
                break  # stop at first match
    
    if docket is None and plaintiff_id and not case_df.empty:
        plaintiff_cases = case_df[case_df['plaintiff_id'] == plaintiff_id]
        if not plaintiff_cases.empty:
            latest_case = plaintiff_cases.sort_values(by='date_filed', ascending=False).iloc[0]
            docket = latest_case.get('docket')

    payload = {
        "reg_no": reg_no,
        "filename": filename,
        "full_filename": full_filename,
        "plaintiff_name": plaintiff_name,
        "plaintiff_id": plaintiff_id,
        "docket": docket,
        "number_of_cases": number_of_cases,
        "ip_type": ip_type
    }

    return payload


@safe_transaction(bind='maidalv_db')
def process_copyright_production(file_id, reg_no, production, filename, asset_row=None):
    """
    Synchronous version of process_copyright_production for direct API calls.
    Returns (success: bool, message: str, updated_asset: dict | None)
    """
    import logging
    logger = logging.getLogger(__name__)

    try:
        plaintiff_df = get_plaintiff_df()
        case_df = get_case_df()
        conn = g.db_conn

        # Only update database if asset_row is not provided
        if asset_row is None:
            # Update the production status in the database and fetch the updated row
            update_query = text("""
                UPDATE copyrights_files
                SET production = :production, update_time = now()
                WHERE id = :file_id
                RETURNING id, registration_number, method, filename, type, production, (
                    SELECT plaintiff_id FROM copyrights WHERE registration_number = :reg_no
                ) as plaintiff_id
            """)
            
            result = conn.execute(
                update_query,
                {"production": production, "file_id": file_id, "reg_no": reg_no}
            ).first()

            if not result:
                return False, f"Copyright with file_id '{file_id}' not found", None

            # Convert the SQLAlchemy Row object to a dictionary
            asset_row = dict(result._mapping) if result else None
        else:
            logger.info(f"Using provided asset_row for reg_no {reg_no}, skipped database update")

        # Get Qdrant client
        qdrant_client = get_qdrant_client()
        if not qdrant_client:
            return False, "Qdrant client not available", None

        if production:
            # Add to Qdrant if setting production to True
            # Get high-res image URL (prioritize high-res for embeddings)
            plaintiff_id = asset_row.get('plaintiff_id')

            if plaintiff_id and filename:
                high_url = get_high_res_path(plaintiff_id, filename)
                low_url = get_low_res_path(plaintiff_id, filename)

                # Download image bytes
                img_bytes = download_image_bytes(high_url)
                if not img_bytes:
                    # Fallback to low-res if high-res fails
                    img_bytes = download_image_bytes(low_url)
            else:
                logger.warning(f"Missing plaintiff_id or filename for reg_no: {reg_no}")
                img_bytes = None

            if not img_bytes:
                return False, f"Failed to download image for registration number: {reg_no}", None

            # Compute embedding using SigLIP model
            with tempfile.NamedTemporaryFile(delete=False, suffix=".jpg") as temp_file:
                temp_file.write(img_bytes)
                temp_path = temp_file.name

            try:
                model = get_siglip_model(load_if_needed=True)
                feats = model.compute_features([temp_path], data_type="image")

                if hasattr(feats, 'numpy'):  # Handle tensor case
                    feats = feats.numpy()

                if isinstance(feats, np.ndarray):
                    vector = feats[0].tolist() if feats.ndim >= 2 else feats.tolist()
                else:
                    # Fallback for other iterable types
                    vector = list(feats[0]) if feats and len(feats) > 0 else []
            finally:
                if os.path.exists(temp_path):
                    os.remove(temp_path)

            if not vector or len(vector) != QDRANT_VECTOR_SIZE:
                return False, f"Failed to compute valid embedding for registration number: {reg_no}", None

            # Get payload with all required fields
            payload = _get_qdrant_payload(asset_row, plaintiff_df, case_df)

            # Store vector in Qdrant
            point_id = str(uuid.uuid5(uuid.NAMESPACE_DNS, f"{payload['filename']}"))
            success = store_vector(qdrant_client, QDRANT_COLLECTION_NAME, point_id, vector, payload)
            if not success:
                return False, f"Failed to store vector in Qdrant for registration number: {reg_no}", None

        else:
            # Delete from Qdrant if setting production to False
            point_id = str(uuid.uuid5(uuid.NAMESPACE_DNS, f"{filename}"))
            try:
                success = delete_vector(qdrant_client, QDRANT_COLLECTION_NAME, point_id)
                if success:
                    logger.info(f"Successfully removed vector from Qdrant for reg_no {reg_no}")
                else:
                    logger.warning(f"Failed to remove vector from Qdrant for reg_no {reg_no} (point may not exist)")
            except Exception as delete_err:
                # Don't fail the whole operation if Qdrant deletion fails
                logger.warning(f"Error removing vector from Qdrant for reg_no {reg_no}: {delete_err}")

        logger.info(f"Successfully processed copyright production for reg_no {reg_no}: production={production}")

        return True, f"Production status for '{reg_no}' updated to {production} successfully", asset_row

    except Exception as e:
        logger.error(f"Error processing copyright production for reg_no {reg_no}: {e}")
        raise e