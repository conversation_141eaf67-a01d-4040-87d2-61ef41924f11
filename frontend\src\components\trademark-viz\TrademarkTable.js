import React from 'react';
import {
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    TableSortLabel,
    Paper,
    Link,
    Typography,
    Box
} from '@mui/material';

const formatCellContent = (content) => {
    if (Array.isArray(content)) {
        return content.join(', ');
    }
    if (typeof content === 'boolean') {
        return content ? 'Yes' : 'No';
    }
    if (content === null || content === undefined || content === '') {
        return 'N/A';
    }
    return String(content);
};


function TrademarkTable({
    trademarks,
    displayed_columns, // Array of { field, headerName }
    on_row_id_click,
    on_row_title_click,
    current_sort_by,
    current_sort_dir,
    on_sort_change,
}) {
    const handleSortRequest = (propertyField) => {
        const isAsc = current_sort_by === propertyField && current_sort_dir === 'asc';
        const newDir = isAsc ? 'desc' : 'asc';
        on_sort_change(propertyField, newDir);
    };

    if (!trademarks || trademarks.length === 0) {
        return (
            <Paper sx={{ p: 2, textAlign: 'center' }}>
                <Typography>No trademark data to display.</Typography>
            </Paper>
        );
    }

    if (!displayed_columns || displayed_columns.length === 0) {
        return (
            <Paper sx={{ p: 2, textAlign: 'center' }}>
                <Typography>No columns selected to display.</Typography>
            </Paper>
        );
    }

    return (
        <TableContainer component={Paper} sx={{ mt: 2 }} elevation={2}>
            <Table stickyHeader aria-label="trademarks table" size="small">
                <TableHead>
                    <TableRow>
                        {displayed_columns.map((column) => (
                            <TableCell
                                key={column.field}
                                align={column.numeric ? 'right' : 'left'}
                                padding={column.disablePadding ? 'none' : 'normal'}
                                sortDirection={current_sort_by === column.field ? current_sort_dir : false}
                                sx={{ fontWeight: 'bold', whiteSpace: 'nowrap' }}
                            >
                                <TableSortLabel
                                    active={current_sort_by === column.field}
                                    direction={current_sort_by === column.field ? current_sort_dir : 'asc'}
                                    onClick={() => handleSortRequest(column.field)}
                                >
                                    {column.headerName}
                                    {current_sort_by === column.field ? (
                                        <Box component="span" sx={visuallyHidden}>
                                            {current_sort_dir === 'desc' ? 'sorted descending' : 'sorted ascending'}
                                        </Box>
                                    ) : null}
                                </TableSortLabel>
                            </TableCell>
                        ))}
                    </TableRow>
                </TableHead>
                <TableBody>
                    {trademarks.map((trademark) => (
                        <TableRow hover key={trademark.id || trademark.reg_no} sx={{ '&:last-child td, &:last-child th': { border: 0 } }}>
                            {displayed_columns.map((column) => (
                                <TableCell key={`${trademark.id}-${column.field}`}>
                                    {column.field === 'reg_no' ? (
                                        <Link
                                            component="button"
                                            variant="body2"
                                            onClick={() => on_row_id_click(trademark.id)} // Use internal ID for detail fetching
                                            sx={{ textAlign: 'left' }}
                                        >
                                            {formatCellContent(trademark[column.field])}
                                        </Link>
                                    ) : column.field === 'mark_text' ? (
                                        <Link
                                            component="button"
                                            variant="body2"
                                            onClick={() =>
                                                on_row_title_click({
                                                    ser_no: trademark.ser_no, // Internal ID
                                                    reg_no: trademark.reg_no,
                                                    mark_text: trademark.mark_text,
                                                })
                                            }
                                            sx={{ textAlign: 'left' }}
                                        >
                                            {formatCellContent(trademark[column.field])}
                                        </Link>
                                    ) : (
                                        formatCellContent(trademark[column.field])
                                    )}
                                </TableCell>
                            ))}
                        </TableRow>
                    ))}
                </TableBody>
            </Table>
        </TableContainer>
    );
}

// For screen readers
const visuallyHidden = {
    border: 0,
    clip: 'rect(0 0 0 0)',
    height: 1,
    margin: -1,
    overflow: 'hidden',
    padding: 0,
    position: 'absolute',
    top: 20,
    width: 1,
};

export default TrademarkTable;