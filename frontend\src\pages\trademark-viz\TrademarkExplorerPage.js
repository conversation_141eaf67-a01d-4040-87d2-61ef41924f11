import React, { useState, useEffect, useCallback } from 'react';
import {
    Container,
    Typography,
    CircularProgress,
    Alert,
} from '@mui/material';
import {
    getPatentsForExploration,
    getPatentDetails,
    getPatentImagesInfo,
} from '../../services/api_patent_viz';
import { DayPicker } from "react-day-picker"; // Added
import "react-day-picker/style.css"; // Added

// Import sub-components
import TrademarkFilterControls from '../../components/trademark-viz/FilterControls';
import QuickStatsDisplay from '../../components/patent-viz/QuickStatsDisplay';
import TrademarkTable from '../../components/trademark-viz/TrademarkTable';
import PaginationControls from '../../components/patent-viz/PaginationControls';
import PatentImageModal from '../../components/patent-viz/PatentImageModal';
import { getTrademarksForExploration } from '../../services/api_trademark_viz';
import { getTrademarkImagesInfo } from '../../services/api_trademark_viz';
import { getTrademarkDetails } from '../../services/api_trademark_viz';
import TrademarkDetailModal from '../../components/trademark-viz/TrademarkDetailsModal';
import TrademarkImageModal from '../../components/trademark-viz/TrademarkImageModal';
// TDD: TEST: PatentExplorerPage fetches patent list on mount and when filters change
// TDD: TEST: PatentExplorerPage displays loading/error states for patent list
// TDD: TEST: PatentExplorerPage renders FilterControls and PatentTable
// TDD: TEST: PatentExplorerPage handles pagination and sorting correctly
// TDD: TEST: PatentExplorerPage opens/closes PatentDetailModal and fetches data
// TDD: TEST: PatentExplorerPage opens/closes PatentImageModal and fetches image paths
const INITIAL_FILTERS = {
    reg_no: "",
    ser_no: "",
    mark_text: "",
    applicant_name: "",
    filing_date_range: { from: null, to: null },
    tro: "All",  // special handling needed since 'tro' is Boolean in DB
    mark_standard_character_indicator: "All", // also Boolean in DB
    mark_current_status_code: "",
    mark_feature_code: "",
    plaintiff_id: "",
    page: 1,
    per_page: 25,
    sort_by: "filing_date",
    sort_dir: "desc",
    selected_columns: [
        "reg_no",
        "mark_text",
        "applicant_name",
        "filing_date",
        "tro"
    ]
};


// Define based on trademarks table from schema
const ALL_AVAILABLE_COLUMNS = [
    { field: "id", headerName: "ID", alwaysVisible: false, defaultHidden: true }, // Internal ID, usually not shown
    { field: "reg_no", headerName: "Registration No.", alwaysVisible: true },
    { field: "ser_no", headerName: "Serial No." },
    { field: "mark_text", headerName: "Mark Text", alwaysVisible: true },
    { field: "applicant_name", headerName: "Applicant Name", alwaysVisible: true },
    { field: "filing_date", headerName: "Filing Date" },
    { field: "tro", headerName: "TRO" },
    { field: "int_cls", headerName: "International Class" }, // Array, might need formatting
    { field: "plaintiff_id", headerName: "Plaintiff ID", defaultHidden: true },
    { field: "nb_suits", headerName: "Number of Suits", defaultHidden: true },
    { field: "country_codes", headerName: "Country Codes" }, // Array, might need formatting
    { field: "associated_marks", headerName: "Associated Marks", defaultHidden: true }, // Array, might need formatting
    { field: "info_source", headerName: "Info Source" },
    { field: "image_source", headerName: "Image Source", defaultHidden: true },
    { field: "certificate_source", headerName: "Certificate Source", defaultHidden: true },
    { field: "mark_current_status_code", headerName: "Current Status Code" },
    { field: "mark_feature_code", headerName: "Feature Code", defaultHidden: true },
    { field: "mark_standard_character_indicator", headerName: "Standard Character", defaultHidden: true },
    { field: "mark_disclaimer_text", headerName: "Disclaimer Text", defaultHidden: true }, // Array, might need formatting
    { field: "mark_image_colour_claimed_text", headerName: "Color Claimed", defaultHidden: true },
    { field: "mark_image_colour_part_claimed_text", headerName: "Color Part Claimed", defaultHidden: true },
    { field: "mark_translation_statement_daily", headerName: "Translation Statement", defaultHidden: true },
    { field: "name_portrait_statement_daily", headerName: "Name Portrait Statement", defaultHidden: true },
    { field: "mark_description_statement_daily", headerName: "Description Statement", defaultHidden: true },
    { field: "certification_mark_statement_daily", headerName: "Certification Statement", defaultHidden: true },
    { field: "national_design_code", headerName: "National Design Code", defaultHidden: true }, // Array, might need formatting
    { field: "goods_services", headerName: "Goods & Services", defaultHidden: true }, // JSONB, might need formatting
    { field: "goods_services_text_daily", headerName: "Goods & Services Text", defaultHidden: true },
    { field: "mark_current_status_external_description_text", headerName: "Status Description", defaultHidden: true },
    { field: "create_time", headerName: "Created", defaultHidden: true },
    { field: "update_time", headerName: "Updated", defaultHidden: true },
];


function TrademarkExplorerPage() {
    const [filters, setFilters] = useState(INITIAL_FILTERS);
    const [trademarksData, setTrademarksData] = useState({ trademarks: [], pagination: {}, quick_stats: {} });
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState(null);

    const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
    const [selectedTrademarkForDetail, setSelectedTrademarkForDetail] = useState(null);
    const [isDetailLoading, setIsDetailLoading] = useState(false);
    const [detailError, setDetailError] = useState(null);

    const [isImageModalOpen, setIsImageModalOpen] = useState(false);
    const [selectedTrademarkForImages, setSelectedTrademarkForImages] = useState(null);
    const [imagePathsData, setImagePathsData] = useState({ image_paths: [], trademark_reg_no: "", base_image_folder_path: "" });
    const [isImagesLoading, setIsImagesLoading] = useState(false);
    const [imagesError, setImagesError] = useState(null);

    const [availableColumns] = useState(ALL_AVAILABLE_COLUMNS);

    const fetchTrademarks = useCallback(async () => {
        setIsLoading(true);
        setError(null);
        try {
            // TDD: TEST: API_CLIENT.get for trademark list is called with correct filter params
            const apiFilters = {
                ...filters,
                filing_date_start: filters.filing_date_range?.from,
                filing_date_end: filters.filing_date_range?.to,
            };
            delete apiFilters.filing_date_range; // Clean up the temporary range object

            const response = await getTrademarksForExploration(apiFilters);
            setTrademarksData(response.data);
        } catch (apiError) {
            setError(apiError.message || 'Failed to fetch trademarks');
            setTrademarksData({ trademarks: [], pagination: {}, quick_stats: {} }); // Reset data on error
        } finally {
            setIsLoading(false);
        }
    }, [filters]);

    useEffect(() => {
        fetchTrademarks();
    }, [fetchTrademarks]);

    const handleFilterChange = useCallback((newFilterValues) => {
        setFilters((prevFilters) => ({
            ...prevFilters,
            ...newFilterValues,
            page: 1, // Reset to page 1 on filter change
        }));
    }, []);

    const handlePageChange = useCallback((newPage) => {
        setFilters((prevFilters) => ({ ...prevFilters, page: newPage }));
    }, []);

    const handlePerPageChange = useCallback((newPerPage) => {
        setFilters((prevFilters) => ({
            ...prevFilters,
            per_page: newPerPage,
            page: 1, // Reset to page 1
        }));
    }, []);

    const handleSortChange = useCallback((sortByField, sortDirection) => {
        setFilters((prevFilters) => ({
            ...prevFilters,
            sort_by: sortByField,
            sort_dir: sortDirection,
        }));
    }, []);

    const handleColumnSelectionChange = useCallback((newlySelectedColumnsFieldsArray) => {
        setFilters((prevFilters) => ({
            ...prevFilters,
            selected_columns: newlySelectedColumnsFieldsArray,
        }));
    }, []);

    const openDetailModal = useCallback(async (trademarkIdForDetail) => {
        setIsDetailModalOpen(true);
        setIsDetailLoading(true);
        setDetailError(null);
        setSelectedTrademarkForDetail(null); // Clear previous data
        try {
            // TDD: TEST: API_CLIENT.get for trademark detail is called with correct trademark_id
            const response = await getTrademarkDetails(trademarkIdForDetail); // TODO: Replace with getTrademarkDetails when available
            setSelectedTrademarkForDetail(response.data);
        } catch (apiError) {
            setDetailError(apiError.message || 'Failed to fetch trademark details');
        } finally {
            setIsDetailLoading(false);
        }
    }, []);

    const closeDetailModal = useCallback(() => {
        setIsDetailModalOpen(false);
        setSelectedTrademarkForDetail(null);
    }, []);

    const openImageModal = useCallback(async (trademarkForImages) => { // trademarkForImages is {id, reg_no, mark_text}
        setSelectedTrademarkForImages(trademarkForImages);
        setIsImageModalOpen(true);
        setIsImagesLoading(true);
        setImagesError(null);
        setImagePathsData({ image_paths: [], trademark_reg_no: "", base_image_folder_path: "" }); // Clear previous
        try {
            const response = await getTrademarkImagesInfo(trademarkForImages.ser_no); // TODO: Replace with getTrademarkImagesInfo when available
            setImagePathsData(response.data);
        } catch (apiError) {
            setImagesError(apiError.message || 'Failed to fetch trademark images');
        } finally {
            setIsImagesLoading(false);
        }
    }, []);

    const closeImageModal = useCallback(() => {
        setIsImageModalOpen(false);
        setSelectedTrademarkForImages(null);
    }, []);

    const displayedColumnsConfig = React.useMemo(() => {
        return filters.selected_columns
            .map(field => availableColumns.find(col => col.field === field))
            .filter(Boolean); // Ensure only valid columns are passed
    }, [filters.selected_columns, availableColumns]);

    return (
        <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
            <Typography variant="h4" component="h1" gutterBottom>
                Trademark Explorer
            </Typography>

            {/* TDD: TEST: FilterControls receives current_filters and callbacks */}
            <TrademarkFilterControls
                current_filters={filters}
                on_filter_change={handleFilterChange}
                available_columns={availableColumns}
                on_column_selection_change={handleColumnSelectionChange}
            />

            {/* TDD: TEST: QuickStatsDisplay receives correct stats */}
            {trademarksData.quick_stats && trademarksData.quick_stats.total_filtered_results != null && (
                <QuickStatsDisplay stats={trademarksData.quick_stats} />
            )}

            {isLoading ? (
                <CircularProgress sx={{ display: 'block', margin: '20px auto' }} />
            ) : error ? (
                <Alert severity="error" sx={{ mt: 2 }}>{error}</Alert>
            ) : trademarksData.trademarks && trademarksData.trademarks.length > 0 ? (
                <>
                    {/* TDD: TEST: PatentTable receives trademarks and callbacks */}
                    <TrademarkTable
                        trademarks={trademarksData.trademarks}
                        displayed_columns={displayedColumnsConfig}
                        on_row_id_click={openDetailModal}
                        on_row_title_click={openImageModal}
                        current_sort_by={filters.sort_by}
                        current_sort_dir={filters.sort_dir}
                        on_sort_change={handleSortChange}
                    />
                    {/* TDD: TEST: PaginationControls receives correct props and callbacks */}
                    <PaginationControls
                        current_page={filters.page}
                        items_per_page={filters.per_page}
                        total_items={trademarksData.total || 0}
                        on_page_change={handlePageChange}
                        on_per_page_change={handlePerPageChange}
                    />
                </>
            ) : (
                <Alert severity="info" sx={{ mt: 2 }}>No trademarks found matching your criteria.</Alert>
            )}

            {/* TDD: TEST: PatentDetailModal receives correct props */}
            <TrademarkDetailModal
                is_open={isDetailModalOpen}
                on_close={closeDetailModal}
                trademark_data={selectedTrademarkForDetail}
                is_loading={isDetailLoading}
                error={detailError}
            />

            {/* TDD: TEST: PatentImageModal receives correct props */}
            <TrademarkImageModal
                is_open={isImageModalOpen}
                on_close={closeImageModal}
                trademark_text={selectedTrademarkForImages ? selectedTrademarkForImages.mark_text : ""}
                image_data={imagePathsData}
            />
        </Container>
    );
}

export default TrademarkExplorerPage;
