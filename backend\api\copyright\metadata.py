"""
Metadata and cache operations for copyright assets
"""
from flask import request, jsonify, current_app
from sqlalchemy import text

from backend.extensions import db
from backend.utils.cache_utils import refresh_cache

def get_copyright_types():
    """Gets distinct type values from cache, refreshing lazily from external copyrights table if empty."""
    from backend.utils.cache_utils import get_copyright_meta, set_copyright_meta
    engine = db.get_engine(bind='maidalv_db')
    try:
        # Try cache first
        meta = get_copyright_meta()
        types_list = meta.get("types", [])
        if not types_list:
            with engine.connect() as conn:
                result = conn.execute(text("SELECT DISTINCT type FROM copyrights_files WHERE type IS NOT NULL AND type != '' ORDER BY type"))
                types_list = [r[0] for r in result] if result.returns_rows else []
            # Update cache
            set_copyright_meta(types_list=types_list)
        
        # Format as expected by frontend
        formatted_types = [{"name": t} for t in types_list]
        return jsonify({"success": True, "data": formatted_types})
    except Exception as e:
        current_app.logger.error(f"Error getting copyright types: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

def get_copyright_methods():
    """Gets distinct method values from cache, refreshing lazily from external copyrights table if empty."""
    from backend.utils.cache_utils import get_copyright_meta, set_copyright_meta
    engine = db.get_engine(bind='maidalv_db')
    try:
        # Try cache first
        meta = get_copyright_meta()
        methods_list = meta.get("methods", [])
        if not methods_list:
            with engine.connect() as conn:
                result = conn.execute(text("SELECT DISTINCT method FROM copyrights_files WHERE method IS NOT NULL AND method != '' ORDER BY method"))
                methods_list = [r[0] for r in result] if result.returns_rows else []
            # Update cache
            set_copyright_meta(methods_list=methods_list)
        
        # Format as expected by frontend
        formatted_methods = [{"name": m} for m in methods_list]
        return jsonify({"success": True, "data": formatted_methods})
    except Exception as e:
        current_app.logger.error(f"Error getting copyright methods: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

def manage_copyright_types():
    """Deprecated: taxonomy removed. Kept for compatibility; returns 410."""
    return jsonify({"success": False, "error": "This endpoint is deprecated"}), 410

def refresh_data_cache():
    """
    Triggers a refresh of the specified data cache (plaintiff, case, or all).
    """
    data = request.get_json(silent=True) or {}
    cache_type = data.get('type', 'all')
    results = refresh_cache(cache_type)
    
    if any(res.get('status') == 'failed' for res in results.values()):
        return jsonify({"success": False, "error": "One or more caches failed to refresh.", "details": results}), 500
    
    return jsonify({"success": True, "data": results})

def get_plaintiff_registrations(plaintiff_id):
    """Get all registration numbers for a plaintiff."""
    try:
        engine = db.get_engine(bind='maidalv_db')
        with engine.connect() as conn:
            rows = conn.execute(
                text("SELECT registration_number, title FROM copyrights WHERE plaintiff_id = :plaintiff_id ORDER BY registration_number"),
                {"plaintiff_id": plaintiff_id}
            ).fetchall()
            
            registrations = [{"reg_no": row[0], "title": row[1] or ""} for row in rows]
            
        return jsonify({"success": True, "data": registrations})
        
    except Exception as e:
        current_app.logger.error(f"Error getting plaintiff registrations: {e}")
        return jsonify({"success": False, "error": str(e)}), 500
