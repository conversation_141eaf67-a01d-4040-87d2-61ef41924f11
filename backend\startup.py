import threading
from backend.models.registration import sync_models_from_config
from backend.extensions import db

def _sync_models(logger):
    """
    Synchronizes models from the configuration file.
    Requires a temporary app context to access the database.
    """
    logger.info("Model synchronization from config started...")
    try:
        from backend import create_app
        app = create_app()
        with app.app_context():
            session = db.session
            sync_models_from_config(session)
        logger.info("Model synchronization from config finished.")
    except Exception as e:
        logger.critical("Model synchronization from config failed: %s", e, exc_info=True)
        raise

def load_data_and_warmup_model(logger, force_refresh=True):
    """
    Coordinates the concurrent loading of cache data, warming up of the Siglip model,
    and synchronization of models from config.
    Returns a dictionary with the loaded data or an error message.
    """
    logger.info("Kicking off concurrent data and model loading...")
    
    cache_data = {}

    def _load_data_cache():
        """Target function for the data loading thread."""
        logger.info("Data loading thread started...")
        try:
            from backend.utils.db_utils import get_table_from_GZ
            plaintiff_df = get_table_from_GZ('tb_plaintiff', force_refresh=force_refresh)
            case_df = get_table_from_GZ('tb_case', force_refresh=force_refresh)
            cache_data['plaintiff'] = plaintiff_df
            cache_data['case'] = case_df
            logger.info(f"Data loading thread finished. Loaded {len(plaintiff_df)} plaintiffs and {len(case_df)} cases.")
        except Exception as e:
            logger.critical("Data loading thread failed: %s", e, exc_info=True)
            cache_data['data_error'] = e

    def _warmup_model():
        """Target function for the model loading thread."""
        logger.info("Model warmup thread started...")
        try:
            from backend.AI.shared_models import warmup_siglip_model
            warmup_siglip_model()
            logger.info("Model warmup thread finished.")
        except Exception as e:
            logger.critical("Model warmup thread failed: %s", e, exc_info=True)
            cache_data['model_error'] = e

    # Create and start the threads
    data_thread = threading.Thread(target=_load_data_cache, name="DataCacheThread")
    model_thread = threading.Thread(target=_warmup_model, name="ModelWarmupThread")
    
    data_thread.start()
    model_thread.start()

    # Wait for both threads to complete
    data_thread.join()
    model_thread.join()

    # Check if any thread failed and raise an exception to stop the app from starting
    if 'data_error' in cache_data:
        raise cache_data['data_error']
    if 'model_error' in cache_data:
        raise cache_data['model_error']

    logger.info("Concurrent data and model loading complete.")
    
    # Synchronize models after data and model loading
    _sync_models(logger)
    
    return cache_data
