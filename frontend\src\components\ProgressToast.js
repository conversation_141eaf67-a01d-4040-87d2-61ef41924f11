import React from 'react';
import './ProgressToast.css';

const ProgressToast = ({
  isVisible,
  progress,
  uploadProgress = 0, // New prop for upload progress
  onClose,
  title = "Processing...",
  showDetails = false,
  onToggleDetails,
  logs = []
}) => {
  if (!isVisible) return null;

  const percentage = progress.total > 0 ? Math.round((progress.current / progress.total) * 100) : 0;

  return (
    <div className="progress-toast">
      <div className="progress-toast-header">
        <h4>{title}</h4>
        <div className="progress-toast-actions">
          {onToggleDetails && (
            <button 
              className="toggle-details-btn"
              onClick={onToggleDetails}
              title={showDetails ? "Hide details" : "Show details"}
            >
              {showDetails ? '▼' : '▶'}
            </button>
          )}
          <button className="close-btn" onClick={onClose} title="Close">
            ×
          </button>
        </div>
      </div>
      
      {/* Upload Progress - only show if upload is in progress */}
      {uploadProgress > 0 && (
        <div className="progress-bar-container">
          <div className="progress-bar">
            <div
              className="progress-bar-fill upload-progress"
              style={{ width: `${uploadProgress}%` }}
            />
          </div>
          <div className="progress-text">
            Upload: {uploadProgress}%
          </div>
        </div>
      )}

      {/* Processing Progress */}
      <div className="progress-bar-container">
        <div className="progress-bar">
          <div
            className="progress-bar-fill"
            style={{ width: `${percentage}%` }}
          />
        </div>
        <div className="progress-text">
          Processing: {progress.current} / {progress.total} ({percentage}%)
        </div>
      </div>
      
      <div className="progress-stats">
        <span className="success-count">✓ {progress.successful || 0}</span>
        <span className="error-count">✗ {progress.failed || 0}</span>
      </div>

      {showDetails && (
        <div className="progress-details">
          <div className="progress-log">
            {logs && logs.map((log, index) => (
              <div key={index} className={`log-entry ${log.type}`}>
                <span className="log-time">{log.time}</span>
                <span className="log-message">{log.message}</span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ProgressToast;
