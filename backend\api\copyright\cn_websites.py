"""
CN Websites functionality for copyright assets
"""
import os
import uuid
import json
from flask import request, jsonify, current_app, send_file
from sqlalchemy import text

from backend.extensions import db
from backend.utils.cache_utils import get_case_df
from .helpers import (
    sanitize_name, generate_md_registration_number,
    get_low_res_path, get_high_res_path, get_certificate_path
)
from .move_cn_to_copyright import process_cn_website_file_move

def get_cn_websites_file_image(file_id):
    """Serve CN websites file images from local storage."""
    try:
        engine = db.get_engine(bind='maidalv_db')
        with engine.connect() as conn:
            # Get file info with joined cn_websites data
            row = conn.execute(
                text("""
                    SELECT cwf.filename, cw.source_website, cw.docket_formated
                    FROM cn_websites_files cwf
                    JOIN cn_websites cw ON cwf.cn_websites_id = cw.id
                    WHERE cwf.id = :id
                """),
                {"id": file_id}
            ).first()
            
            if not row:
                return jsonify({"success": False, "error": "File not found"}), 404
            
            filename = row[0]
            source_website = row[1]
            docket_formatted = row[2]
            
            if not filename:
                return jsonify({"success": False, "error": "No filename available"}), 404
            
            # Construct file path: /Documents/IP/{source_website}/sanitize_name(docket_formated)
            # Use config for base path
            ip_base_path = current_app.config.get('IP_BASE_PATH', 'D:\\Documents\\Programing\\TRO\\Documents\\IP' if os.name == 'nt' else '/Documents/IP')
            base_path = os.path.join(ip_base_path, source_website, sanitize_name(docket_formatted))
            file_path = os.path.join(base_path, filename)
            
            if not file_path:
                return jsonify({"success": False, "error": f"File not found in expected locations"}), 404
            
            return send_file(file_path)
            
    except Exception as e:
        current_app.logger.error(f"Error serving CN websites file image: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

from flask import g


def move_cn_websites_files_stream_generator(asset_ids):
    """
    Generator function that moves CN websites files to copyrights_files
    and yields progress updates.
    """
    current_app.logger.info(f"Starting move_cn_websites_files_stream_generator for asset_ids: {asset_ids}")
    total_files = len(asset_ids)
    processed_count = 0
    successful_count = 0
    failed_count = 0

    def stream_event(event_type, data):
        return f"data: {json.dumps({'type': event_type, **data})}\n\n"

    yield stream_event('progress', {
        'current': processed_count,
        'total': total_files,
        'successful': successful_count,
        'failed': failed_count
    })

    engine = db.get_engine(bind='maidalv_db')
    with engine.connect() as conn:
        trans = conn.begin()
        try:
            case_df = get_case_df()

            cn_files_rows = conn.execute(
                text("""
                    SELECT cwf.id, cwf.filename, cwf.type, cwf.reg_no, cwf.cn_websites_id,
                            cw.case_id, cw.docket_formated, cw.source_website
                    FROM cn_websites_files cwf
                    JOIN cn_websites cw ON cwf.cn_websites_id = cw.id
                    WHERE cwf.id IN :ids
                """),
                {"ids": tuple(asset_ids)}
            ).fetchall()

            found_ids = {row[0] for row in cn_files_rows}
            for asset_id in asset_ids:
                if int(asset_id) not in found_ids:
                    processed_count += 1
                    failed_count += 1
                    yield stream_event('error', {
                        'asset_id': asset_id,
                        'message': 'File not found in database.'
                    })
                    yield stream_event('progress', {
                        'current': processed_count,
                        'total': total_files,
                        'successful': successful_count,
                        'failed': failed_count
                    })
                else:
                    yield stream_event('log', {
                        'asset_id': asset_id,
                        'message': 'File found in database.'
                    })
                

            for cn_file_row in cn_files_rows:
                asset_id = cn_file_row[0]
                reg_no = cn_file_row[3]
                
                try:
                    # Get plaintiff_id from case
                    case_id = cn_file_row[5]
                    if case_df.empty:
                        raise Exception("Case data not available")

                    case_row = case_df[case_df['id'] == case_id]
                    if case_row.empty:
                        raise Exception("Case not found")

                    plaintiff_id = int(case_row.iloc[0]['plaintiff_id'])
                    
                    # Generate MD registration number if needed
                    if not reg_no:
                        yield stream_event('log', {'asset_id': asset_id, 'message': 'Generating Registration Number...'})
                        reg_no = generate_md_registration_number(plaintiff_id)

                    # Process the file (copy, resize, upload to COS, etc.)
                    move_generator = process_cn_website_file_move(
                        source_filename=cn_file_row[1],
                        source_website=cn_file_row[7],
                        docket_formatted=cn_file_row[6],
                        reg_no=reg_no,
                        plaintiff_id=plaintiff_id,
                        case_id=case_id
                    )
                    
                    new_filename = None
                    for event in move_generator:
                        if event.get('type') == 'log':
                            yield stream_event('log', {'asset_id': asset_id, 'message': event['message']})
                        elif event.get('type') == 'result':
                            new_filename = event.get('filename')

                    if not new_filename:
                        raise Exception("File processing failed to return a new filename.")

                    current_app.logger.info("process_cn_website_file_move : done and now we will update the database")

                    # If reg_no was generated, insert into copyrights table
                    if not cn_file_row[3]:
                        conn.execute(
                            text("""
                                INSERT INTO copyrights (registration_number, tro, plaintiff_id)
                                VALUES (:reg_no, true, :plaintiff_id)
                                ON CONFLICT (registration_number) DO NOTHING
                            """),
                            {"reg_no": reg_no, "plaintiff_id": plaintiff_id}
                        )

                    # Create new copyrights_files entry
                    yield stream_event('log', {'asset_id': asset_id, 'message': 'Updating Database...'})
                    print("copyrights table updated")
                    method = os.path.splitext(new_filename)[0].split('_')[1]
                    new_file_row = conn.execute(
                        text("""
                            WITH new_file AS (
                                INSERT INTO copyrights_files (filename, registration_number, method, production, type)
                                VALUES (:filename, :reg_no, :method, false, null)
                                RETURNING id, filename, registration_number, method, production, type, create_time, update_time
                            )
                            SELECT nf.id, nf.filename, nf.registration_number, nf.method, nf.production, nf.type,
                                    nf.create_time, nf.update_time, c.certificate_status, c.plaintiff_id
                            FROM new_file nf
                            JOIN copyrights c ON c.registration_number = nf.registration_number
                        """),
                        {
                            "filename": new_filename,
                            "reg_no": reg_no,
                            "method": method
                        }
                    ).first()
                    print(f"copyrights_files table updated, our new asset is : {new_file_row}")

                    if new_file_row:
                        plaintiff_id = new_file_row[9]
                        newly_created_asset = {
                            "id": str(new_file_row[0]),
                            "filename": new_file_row[1],
                            "registration_number": new_file_row[2],
                            "method": new_file_row[3],
                            "production": bool(new_file_row[4]) if new_file_row[4] is not None else False,
                            "type": new_file_row[5],
                            "create_time": new_file_row[6].isoformat() if new_file_row[6] else None,
                            "update_time": new_file_row[7].isoformat() if new_file_row[7] else None,
                            "certificate_status": new_file_row[8],
                            "plaintiff_id": plaintiff_id,
                            "low_res_path": get_low_res_path(plaintiff_id, new_file_row[1], new_file_row[7].isoformat()),
                            "high_res_path": get_high_res_path(plaintiff_id, new_file_row[1], new_file_row[7].isoformat()),
                            "certificate_path": get_certificate_path(plaintiff_id, new_file_row[1])
                        }
                        successful_count += 1
                        yield stream_event('success', {
                            'asset_id': asset_id,
                            'reg_no': reg_no,
                            'message': 'Successfully moved file.',
                            'new_asset': newly_created_asset
                        })
                    else:
                        raise Exception("Failed to fetch newly created asset information.")

                except Exception as e:
                    failed_count += 1
                    yield stream_event('error', {
                        'asset_id': asset_id,
                        'reg_no': reg_no,
                        'message': str(e)
                    })
                
                processed_count += 1
                yield stream_event('progress', {
                    'current': processed_count,
                    'total': total_files,
                    'successful': successful_count,
                    'failed': failed_count
                })
            
            trans.commit()
            current_app.logger.info("Transaction committed successfully.")
            
        except Exception as e:
            trans.rollback()
            current_app.logger.error(f"Exception in generator, transaction rolled back: {e}", exc_info=True)
            yield stream_event('error', {
                'asset_id': 'GENERAL',
                'message': f"A general error occurred: {str(e)}"
            })

        finally:
            current_app.logger.info("Generator finished, sending complete event.")
            yield stream_event('complete', {
                'current': processed_count,
                'total': total_files,
                'successful': successful_count,
                'failed': failed_count
            })
